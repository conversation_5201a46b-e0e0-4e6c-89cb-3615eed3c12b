@import "tailwindcss";

.flex-c{
    @apply gap-1;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.my-sheet {
    height: 80%;
}
.my-sheet.sheet-modal-in:not(.sheet-modal-step) {
    height: 80%; /* full height */
}
.preloader-modal{
    border-radius: 100px !important;
}
.sheet-modal .sheet-modal-inner{
    position:unset;
    background: #222222
}
@theme {
    --text-xs: 0.800rem;
    --text-xxs: 0.700rem;
    --text-gand: 20rem;
    --text-primary: #76a0fb
}

.sheet-modal-inner {
    background-color: theme('color.gray.100'); /* Light gray background */
}
.calendar-sheet .sheet-modal-inner{
background: #171717;
}

.calendar-sheet .toolbar-inner{
background: #171717;
}

.toolbar{
    background: #171717 !important;
}

h6{
    @apply opacity-80
}
.card-white{
    @apply bg-white shadow-xs rounded-2xl p-3
}
.white-sheet h6{
    opacity:1
}
h5{
    font-size: 1.1rem;
    @apply font-semibold
}
.icon-bg-white{
    @apply bg-white/20 rounded-full p-1 w-[30px] h-[30px] flex items-center justify-center
}
.title-widget{
    @apply bg-white/10 rounded-lg p-3 py-2.5 flex items-center
}
.text-sm {
    font-size: 16px;
}
.white-badge{
    @apply  bg-white/15 p-1 px-2 rounded-md outline-1 outline-white/20 -outline-offset-1
}
.white-badge-rounded{
    @apply  bg-white/20 p-1 px-2 rounded-full
}
.text-green-on-dark{
    color:#2B9B94;
}
.text-green{
    color:#2B9B94;
}

.text-red-on-dark{
    color:#F86F6E;
}
.green-badge-on-dark{
    background: rgba(43, 155, 148, 0.25);
    color:#2B9B94;
    @apply  font-semibold text-xs p-0.5 px-1 rounded-full
}

.green-badge{
    background: #2B9B94;
    @apply  font-semibold text-xs p-0.5 px-1 rounded-full text-white
}


.blue-badge-on-dark{
    background: #4176E9;
    @apply  text-white font-semibold text-xs p-0.5 px-1 rounded-full
}

.center{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.button{
    font-weight: 800 !important;
    margin:revert-layer !important;
}

.toast-content{
    display: flex !important;
    width:fit-content !important;
    align-items: center;
    z-index:9999999999999999999 !important;
    padding:10px !important;
    justify-content: left !important;
}

.toast{
    background-color: transparent !important;
    backdrop-filter:unset !important;
    padding:10px;
    display: flex;
    justify-content: center;
}

.occupied .main-area{
    background:#F0F7FF;
    color:var(--color-primary)
}


.printed .main-area{
    background:#FFF1F0;
    color:#E53935
}

.deleted-kot{
    background:linear-gradient(90deg, rgba(255, 247, 242, 1) 59%, rgba(255, 255, 255, 1) 100%)
}

.merged .main-area{
    background:#8b5dff1f;
    color:#7c24ff
}


.item-floating-label{
    color: theme("colors.gray.500") !important;
}
.item-content::before {
    background: transparent !important;
}
.item-input.item-content{
    margin:0px !important;
    padding-left:0px !important;
}

.customer-info-sheet{
    z-index:9999999999999999999 !important;
}
.free .main-area{
    color:theme("colors.gray.400");
    opacity:0.7;
}


.toast-content{
    background-color: #222222 !important;
    border-radius: 100px !important;
    color:white !important;
}

.ripple-wave {
    background-color: rgba(117, 117, 117, 0.07) !important;
}

.item-floating-label{
    font-size:10px !important;
}

.item-floating-label{
    font-weight: 600 !important;
    opacity:0.6;
}
.list-index ul{
    color:theme("colors.gray.400") !important;
}

.custom-lazy-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.cart-more-info ul:first-child li{
    border-bottom:1px solid theme("colors.gray.200");
}

.custom-lazy-image.lazy-load-image-loaded {
    object-fit: cover;
}


.list-index:before {
    display:none !important;
}
.item-title{
    text-transform: capitalize;
}
.list .item-content{
    padding-left:5px !important;
}

.list-index li {
    height: 18px !important;
}


.list-index-label {
    margin-right: 10px;
}


.item-floating-label{
    top:6px
}
.item-input-with-value .item-floating-label{
    top:-3px !important;
}
.item-input-focused .item-floating-label{
    top:-3px !important;
}
.two-line-ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2; /* Number of lines to show */
    -webkit-box-orient: vertical;
    overflow: hidden;
}


:root {
    --primary: #296eeb;
    --f7-page-bg-color:#000 !important;
    --f7-input-font-size: 14px !important;
    --f7-sheet-border-radius:22px !important;
    --f7-font-family: 'Gilroy' !important;
    --f7-font-size: 14px !important;
    --f7-button-border-radius:0.75rem !important;
    --f7-button-large-height:53px !important;
    --f7-sheet-height:'auto' !important;
}

a {
    color: rgba(255, 255, 255, 0.65) !important;
}

@theme {
    --color-primary: #296eeb;
    --color-black: theme("colors.gray.600");
}
.text-muted{
    opacity:0.7;
}
.text-muted-dark{
    opacity:0.7
}
body{
    overflow: hidden;
    height: 100%;

    font-size:14px !important;
    font-family:'inter' !important;
    font-weight: 400;

}
.zoom-btn{
    transition:all 0.2s ease
}
.zoom-btn:active{
    transform:scale(0.9) !important;
}
.page-content{
    overflow:hidden !important;
}

.page{

}
.sheet-modal{
    background:white !important;
}

h1,h2,h3,h4{
    font-family:'Poppins';
}

h2{
    font-size: 2.5rem
}
h3{
    font-size: 1.5rem
}

.progressbar, .progressbar-infinite{
    background:transparent !important;
}
.progressbar-infinite:before, .progressbar-infinite:after {
    background:white !important;
}

h1{
    font-size:27px
}

@font-face {
    font-family: 'Poppins';
    src: url('/font/poppins-bold-webfont.woff') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Poppins';
    src: url('/font/Poppins-ExtraBold.ttf') format('truetype');
    font-weight: 800;
    font-style: normal;
}

@font-face {
    font-family: 'inter';
    src: url('/font/Inter-Regular.woff2') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'inter';
    src: url('/font/Inter-SemiBold.woff2') format('truetype');
    font-weight: 500;
    font-style: normal;
}


/*@font-face {*/
/*    font-family: 'Gilroy';*/
/*    src: url('/font/Gilroy-Medium.ttf') format('truetype');*/
/*    font-weight: 500;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'Gilroy';*/
/*    src: url('/font/Gilroy-SemiBold.ttf') format('truetype');*/
/*    font-weight: 600;*/
/*    font-style: normal;*/
/*}*/


/*@font-face {*/
/*    font-family: 'Gilroy';*/
/*    src: url('/font/Gilroy-Bold.ttf') format('truetype');*/
/*    font-weight: 700;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'Gilroy';*/
/*    src: url('/font/Gilroy-ExtraBold.ttf') format('truetype');*/
/*    font-weight: 800;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'Gilroy';*/
/*    src: url('/font/Gilroy-Heavy.ttf') format('truetype');*/
/*    font-weight: 900;*/
/*    font-style: normal;*/
/*}*/