import {createFileRoute, useNavigate} from '@tanstack/react-router'
import { useState } from 'react'
import { Form, Input, Button, Checkbox, Typography, Space, Divider, Alert, message } from "antd"
import {
    EyeInvisibleOutlined,
    EyeTwoTone,
    GoogleOutlined,
    InfoCircleOutlined,
    PhoneOutlined,
    SafetyOutlined,
    ArrowLeftOutlined
} from '@ant-design/icons'
import api from "../utils/api.js";
import {setupAccount} from "../utils/util.jsx";

const { Title, Text } = Typography

export const Route = createFileRoute('/login')({
    component: RouteComponent,
})

function RouteComponent() {
    const [step, setStep] = useState('email') // 'email', 'phone', 'otp'
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const [phoneNumber, setPhoneNumber] = useState('')
    const [otp, setOtp] = useState('')
    const [countdown, setCountdown] = useState(0)
    const navigate = useNavigate()

    const handleEmailLogin = async (values) => {
        setLoading(true)
        setPhoneNumber(values.Phone)
        try {
            await api.get('/admin/Step1_VerifyMobileNumber?Phone=' + values.Phone);
            setStep('otp')
        } finally {
            setLoading(false)
        }
    }

    const handlePhoneSubmit = async () => {
        if (!phoneNumber || phoneNumber.length < 10) {
            message.error('Please enter a valid phone number')
            return
        }

        setLoading(true)
        try {
            await new Promise(resolve => setTimeout(resolve, 1500))
            setStep('otp')
            setCountdown(60)
            message.success('OTP sent to your phone')

            const timer = setInterval(() => {
                setCountdown(prev => {
                    if (prev <= 1) {
                        clearInterval(timer)
                        return 0
                    }
                    return prev - 1
                })
            }, 1000)
        } catch (error) {
            message.error('Failed to send OTP')
        } finally {
            setLoading(false)
        }
    }

    const handleOtpSubmit = async () => {

        setLoading(true)
        try {
            var data= await api.get('/admin/Step2_VerifyOTP?Phone=' + phoneNumber + '&Otp=' + otp);
            if(data.data.result == "ACCOUNT_EXIST_PULL_EVERYTHING_NEW"){
                setupAccount(data)
                navigate({ to: '/reports/orders' })
            }else{
                alert('Your account is not active')
                setLoading(false)

            }



        }  finally {
            setLoading(false)
        }
    }

    return (
        <div style={{background: 'URL("/images/bg-queen.jpeg")'}}
             className=" !bg-contain w-screen  h-screen flex-col  flex-c items-center ">
            <div
                className="absolute via-black/80 bottom-0 w-full h-[40%] to-transparent from-black/90 bg-gradient-to-t"></div>
            <div className="absolute top-0 w-full h-[40%] to-transparent  from-black/80 bg-gradient-to-b"></div>
            <div className="h-screen p-12 fixed top-0 left-0 z-10 relative flex justify-between flex !flex-col">
                <div><img className={'w-[200px]'} src={'/images/For black Background.png'} /></div>
                <div className="max-w-2xl pb-4">
                    <h2 className="text-white font-bold mb-7  text-3xl">
                        <span className="text-emerald-500">India's #1 </span>
                        AI Powered Modern & Secured Restaurant Software
                    </h2>
                    <h1 className="opacity-50 text-white mb-12 mt-4 ">
                        India's first premium & Quantum based security restaurant software,
                        revolutionizing the industry with cutting-edge and unmatched
                        innovation
                    </h1>
                    <img src={'/images/Group 1742.svg'} className={''} width={500}/>
                </div>
            </div>

            <div className={'p-4 ms-auto z-50 h-full'}>
                <div className="bg-white shadow-lg p-12 rounded-xl shadow-lg w-[500px] h-full">
                    {step === 'email' && (
                        <>
                            <div className=" mb-8">
                                {/*<Title level={2} className="text-gray-800 mb-2">*/}
                                {/*  Sign In*/}
                                {/*</Title>*/}
                                <img className={'w-[40px] mb-5'} src={'/images/fq black logo.png'}/>
                                <Title level={3} className="text-gray-800 -mb-1">
                                    Sign In
                                </Title>
                                <Text className="text-gray-600">
                                    Welcome to Foodyqueen.
                                </Text>
                            </div>

                            {/* Demo Credentials Alert */}
                            {/*        <Alert*/}
                            {/*            message={*/}
                            {/*                <span>*/}
                            {/*  For security reasons, please ensure you're on a trusted device before signing in.*/}
                            {/*</span>*/}
                            {/*            }*/}
                            {/*            type="info"*/}
                            {/*            icon={<InfoCircleOutlined />}*/}
                            {/*            className="mb-6"*/}
                            {/*            showIcon*/}
                            {/*        />*/}

                            <div className={'flex gap-5 mt-0'}>
                                <Button
                                    onClick={() => {
                                        alert('Google sign in is not supported yet. Please use email/password sign in.')
                                    }}
                                    size="large"
                                    block
                                    icon={<img className={'w-6'} src={'/images/google.svg'}/>}
                                    className="  border-gray-300"
                                >
                                    Sign in with Google
                                </Button>
                                <Button
                                    onClick={() => {
                                        alert('Apple sign in is not supported yet. Please use email/password sign in.')
                                    }}
                                    size="large"
                                    block
                                    icon={<img className={'w-[17px]'} src={'/images/Apple Logo.svg'}/>}

                                    className="h-12 border-gray-300"
                                >
                                    Sign in with Apple
                                </Button>
                            </div>
                            <Divider className={'!mt-7'}>
                                <Text className="opacity-50 100 text-sm">Or with phone</Text>
                            </Divider>


                            {/* Email/Password Form */}
                            <Form
                                className="!mt-5"
                                form={form}
                                onFinish={handleEmailLogin}
                                layout="vertical"
                                initialValues={{
                                    remember: true
                                }}
                            >
                                <Form.Item
                                    label={<Text strong>Phone</Text>}
                                    name="Phone"
                                    rules={[
                                        {required: true, message: ''},
                                    ]}
                                >
                                    <Input
                                        size="large"
                                        placeholder="Owner phone number"
                                        className="rounded-md"
                                    />
                                </Form.Item>

                                <h6 className={'flex -mt-1'}><img className={'w-5.5 mr-1'}
                                                                  src={'/images/security_user_male.svg'}/> <span
                                    className={'opacity-50'}>By
                                continue you're agree</span> <span
                                    className={'font-bold ml-1.5'}>Terms and Conditions</span></h6>

                                <div className="flex items-center justify-between mb-0">
                                    <Form.Item name="remember" valuePropName="checked" className="!mt-6 mb-0">
                                        <Checkbox>
                                            <Text>Remember me</Text>
                                        </Checkbox>
                                    </Form.Item>

                                </div>

                                <Form.Item>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        size="large"
                                        block
                                        loading={loading}
                                        className=" bg-blue-600 hover:bg-blue-700 rounded-md font-medium"
                                    >
                                        Sign In
                                    </Button>
                                </Form.Item>
                            </Form>


                            {/* Google Sign In */}
                            <div className={'space-y-4'}>


                            </div>


                            <div className="text-center mt-6">
                                <Text className="text-gray-600">
                                    Don't have an account?{' '}
                                    <Button type="link" className="p-0 !px-0 text-blue-600 font-medium">
                                        Create account
                                    </Button>
                                </Text>
                            </div>


                        </>
                    )}

                    {step === 'phone' && (
                        <>
                            <div className="mb-6">
                                <Button
                                    type="text"
                                    icon={<ArrowLeftOutlined/>}
                                    onClick={() => setStep('email')}
                                    className="mb-4 p-0 text-blue-600"
                                >
                                    Back
                                </Button>

                                <Title level={3} className="text-gray-800 mb-2">
                                    Enter your phone number
                                </Title>
                                <Text className="text-gray-600">
                                    We'll send you a verification code
                                </Text>
                            </div>

                            <Space direction="vertical" size="large" className="w-full">
                                <div>
                                    <Text strong className="block mb-2">
                                        Phone Number
                                    </Text>
                                    <Input
                                        size="large"
                                        prefix={<PhoneOutlined className="text-gray-400"/>}
                                        placeholder="Enter your phone number"
                                        value={phoneNumber}
                                        onChange={(e) => setPhoneNumber(e.target.value)}
                                        maxLength={15}
                                        className="rounded-md"
                                    />
                                </div>

                                <Button
                                    type="primary"
                                    size="large"
                                    block
                                    loading={loading}
                                    onClick={handlePhoneSubmit}
                                    className="h-12 bg-blue-600 hover:bg-blue-700 rounded-md font-medium"
                                >
                                    Continue
                                </Button>
                            </Space>
                        </>
                    )}

                    {step === 'otp' && (
                        <>
                            <div className="mb-6">


                                <Title level={3} className="text-gray-800 mb-2">
                                    Enter verification code
                                </Title>
                                <Text className="text-gray-600">
                                    We sent a 4-digit code to your
                                    <Text strong> {phoneNumber}</Text>
                                </Text>
                            </div>

                            <Space direction="vertical" size="large" className="w-full">
                                <div>
                                    <Text strong className="block mb-2">
                                        Verification Code
                                    </Text>
                                    <Input
                                        size="large"
                                        prefix={<SafetyOutlined className="text-gray-400"/>}
                                        placeholder="Enter 4-digit code"
                                        value={otp}
                                        onChange={(e) => setOtp(e.target.value.replace(/\D/g, ''))}
                                        maxLength={4}
                                        className="rounded-md text-center text-lg tracking-widest"
                                    />
                                </div>

                                <Button
                                    type="primary"
                                    size="large"
                                    block
                                    loading={loading}
                                    onClick={handleOtpSubmit}
                                    className="h-12 bg-blue-600 hover:bg-blue-700 rounded-md font-medium"
                                >
                                    Verify & Sign In
                                </Button>

                                <div className="text-center">
                                    <Text className="text-gray-600 text-sm">
                                        Didn't receive the code?{' '}
                                    </Text>
                                    <Button
                                        type="link"
                                        disabled={countdown > 0}
                                        className="p-0 !px-0 text-blue-600 text-sm"
                                    >
                                        {countdown > 0 ? `Resend in ${countdown}s` : 'Resend code'}
                                    </Button>
                                </div>
                            </Space>
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}
