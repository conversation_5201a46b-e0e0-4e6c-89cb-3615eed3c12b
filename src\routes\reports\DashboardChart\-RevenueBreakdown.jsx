import {Button, Tag} from "antd";
import React, {useEffect, useState} from "react";
import api from "../../../utils/api.js";
import {Money} from "../../../utils/util.jsx";
import {maxBy, orderBy} from "lodash";
import moment from "moment";
import {Pie} from "@ant-design/charts";


const RevenueBreakdown = () => {
    const [data,setData] = useState([])
    const [length,setLength] = useState(15)
    const [dateRange, setDateRange] = useState([
        moment().startOf('month').toDate(), // first day of month
        moment().endOf('month').toDate(),   // last day of month
    ]);
    const [selectedFilter,setSelectedFilter] = useState('This month')
    useEffect(()=>{
        api.get('/dashboard/GetRevenueBreakdown?from='+dateRange[0].toISOString()+'&to='+dateRange[1].toISOString()).then(res => {
            setData(res.data)
        });
    },[])
    const config = {
        data:data,
        "scale": {
            "color": {
                "type": "identity"
            }
        },
        angleField: 'Sum',
        // onReady: ({chart}) => {
        //
        //     // Wait a short delay to ensure chart is rendered
        //     setTimeout(() => {
        //         const scale = chart.getScale().color;
        //         const { domain } = scale.getOptions();
        //         const items = domain.map(() => {});
        //     }, 100); // adjust delay if needed
        // },
        colorField: 'Color',
        paddingBottom: 0,

        pieStyle: {
            lineWidth: 2,           // gap width
            stroke: '#fff',         // gap color (usually white)
            radius: 1,              // keep full radius
            cornerRadius: 8,        // rounded corners
        },

        innerRadius: 0.6,
        label: {
            text: (datum) => datum.Type,
            style: {
                fontWeight: 'bold',
            },
        },
        legend: false

    };
    return (<div className={'lg:col-span-1 card'}>
        <h6 className={'card-header font-bold'}>Revenue Breakdown
            <div className={'ms-auto'}><Button size={'small'} type={'text'}
                                               icon={<i className={'fa fa-refresh'}></i>}></Button></div>
        </h6>
        <div className={'p-7 min-w-[350px]'}>
            <div className={'relative'}>
                <div className={'w-full center -mt-40 -mb-40'}>
                    <div className={'w-[240px]'}><Pie {...config} /></div>
                </div>
            </div>
            <div className={'px-4'}>
                <div className={'space-y-3 !mt-6'}>
                    { data?.map((item, index) => (
                            <div className={'flex-c bg-gray-50 rounded p-1 px-2'} key={index}>
                                <h6 className={'!font-normal text-slate-500  gap-2.5 flex-c'}>
                                    {item.Type == "Delivery" && <i style={{color: item.color}}
                                                                   className={'opacity-80 text-xxs fa fa-person-biking-mountain'}></i>}
                                    {item.Type == "DineIn" && <i style={{color: item.color}}
                                                                   className={'opacity-80 text-xxs fa fa-store'}></i>}

                                    {item.Type == "Parcel" && <i style={{color: item.color}}
                                                                   className={'opacity-80 text-xxs fa fa-box-taped'}></i>}

                                    {item.Type == "Zomato" && <i style={{color: item.color}}
                                                                   className={'opacity-80 text-xxs fa fa-person-biking-mountain'}></i>}

                                    {item.Type == "Swiggy" && <i style={{color: item.color}}
                                                                   className={'opacity-80 text-xxs fa fa-person-biking-mountain'}></i>}

                                    {item.Type}
                                </h6>
                                <div className={'flex-c gap-7.5 ms-auto'}>
                                    <h6 className={'!font-bold '}>{Money(item.Sum)}</h6>
                                    <h6 className={'gap-1.5 flex-c'}>
                                        <i className={'text-sm text-green fa fa-arrow-up'}></i>{item.Percentage}
                                    </h6>
                                </div>
                            </div>
                        ))}
                </div>
            </div>

        </div>
    </div>)
}

export default RevenueBreakdown;