import WidgetHeaderButtons from "./-WidgetHeaderButtons.jsx";
import {filter, sumBy} from "lodash";
import {Money, SalesChange} from "../../../utils/util.jsx";
import React, {useEffect, useState} from "react";
import api from "../../../utils/api.js";
import moment from "moment";
import SimpleBar from "simplebar-react";
import {Progress, Tag} from "antd";

export const TopSellingItem = () =>{
    const [data,setData] = useState([])
    const [dateRange, setDateRange] = useState([
        moment().subtract(10, 'days').startOf('day').toISOString(),
        moment().endOf('day').toISOString()
    ])
    useEffect(()=> {
        api.get('/dashboard/GetTopSellingItem?from=' + dateRange[0] + '&to=' + dateRange[1]).then((res) => {
                setData(res.data)
            }
        )

    },[])


    return (

        <div className={'lg:col-span-1 card h-full'}>
            <h6 className={'card-header font-bold'}>Top Selling Items

                <WidgetHeaderButtons/>

            </h6>
            <div className={'flex-c gap-1 p-8  pb-2.5 mb-0 w-full'}>
                {filter(data.topSellingItemsWithPercentage, (o, i) => i < 7).map((o) => (
                    <div style={{width: o.Percentage + '%', background: o.Color}}
                         className={'rounded-[2px] h-[8px] '}></div>
                ))}
                <div className={'ms-auto'}>
                    <SalesChange range={data.range} today={data.today} yesterday={data.result?.toString().replace('-','').replace('+','')} />

                </div>


            </div>
            <div className={'p-6 relative space-y-3.5'}>

                <SimpleBar
                    style={{
                        height: '355px',
                        position: 'absolute', // <- Absolutely positioned
                        inset: 0, // top: 0; right: 0; bottom: 0; left: 0;
                    }}
                    autoHide={true} // optional
                >
                    <div className={'p-5'}>
                        <table className="w-full border-0">
                            <thead>
                            <tr className={'border-b-2 font-bold border-gray-200'}>
                                <th className="text-start  p-2.5 text-sm ">#</th>
                                <th className="text-start  p-2.5 text-sm ">Name</th>
                                <th className="text-start  p-2.5 text-sm ">Qty</th>
                                <th className="text-start  p-2.5 text-sm ">Category</th>
                                <th className="text-start  p-2.5 text-sm ">Total</th>
                                <th className="text-start  p-2.5 text-sm ">Avg.</th>
                                <th className="text-start  p-2.5 text-sm ">Compare</th>
                            </tr>
                            </thead>
                            <tbody>
                            {data?.topSellingItemsWithPercentage?.map((x, idx) => (
                                <tr
                                    key={idx}
                                    className={idx !== data.length - 1 ? 'border-b border-gray-200' : ''}
                                >
                                    <td className=" p-2.5 ">{idx + 1}</td>
                                    <td className=" p-2.5 text-primary">{x.Name}</td>
                                    <td className="p-2.5"><Tag className={'small'} color={'#5db182'}
                                                               bordered={false}>{x.TotalQuantity}</Tag></td>
                                    <td className="p-2.5">{x.Category}</td>
                                    <td className=" p-2.5">{Money(x.TotalAmount)}</td>
                                    <td className=" p-2.5">{Money(x.Average)}</td>
                                    <td className=" p-2.5"><Progress percent={x.Percentage} size="small"/></td>
                                </tr>
                            ))}
                            {/* Add more rows as needed */}
                            </tbody>
                        </table>
                    </div>
                </SimpleBar>

            </div>
        </div>

    )
}