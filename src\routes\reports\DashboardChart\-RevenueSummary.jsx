import {<PERSON><PERSON>, <PERSON>, Tooltip} from "antd";
import {useEffect, useState} from "react";
import api from "../../../utils/api.js";
import {AbbribateNumber, Money, SalesChange} from "../../../utils/util.jsx";
import {find, maxBy, sumBy} from "lodash";
import moment from "moment/moment.js";
import {Column, DualAxes} from '@ant-design/charts';
import WidgetHeaderButtons from "./-WidgetHeaderButtons.jsx";
//

const RevenueSummary = () => {
    const [data,setData] = useState([])
const [dateRange, setDateRange] = useState([
    moment().subtract(10, 'days').startOf('day').toISOString(),
    moment().endOf('day').toISOString()
])
    useEffect(()=> {
        api.get('/dashboard/GetRevenue?dashboard=false&from=' + dateRange[0] + '&to=' + dateRange[1]).then((res) => {
                setData(res.data)
            }
        )

    },[])


    const transformData = data?.taxes?.map(date => ({
        time: date.key,
        value: date.value, // sample value for 'count'
    }));

    const config = {
        xField: 'time',
        columnWidthRatio: 0.1, // Wider bars
        marginRatio: 0.0, // Less gap between groups
        legend: {
            color: {
                position: 'bottom',
                layout: { justifyContent: 'left' },
            },
        },

        axis: {

            x: {
                labelFontSize: 12,
                labelFormatter: (d) => moment(d).format('DD MMM'),
                transform: [
                    // 旋转
                    {
                    },
                ],
            },
        },
        // scale: { color: { range: ['#403394', '#F9B627', '#47BC8A', '#47BC8A', '#3FC1DE', '#3FC1DE'] } },
        "scale": {
            "color": {
                "palette": "category10"
            }
        },
        children: [
            {
                data: data?.merged,
                type: 'interval',

                yField: 'value',
                scale: { y: { domainMax: 1500000, tickCount: 5 } },
                // scale: { x: { padding: 10 }, y: { domainMax: 312, tickCount: 5 } },
                xAxis: {
                    label: {
                        // Custom formatter
                        formatter: (text) => `Year ${text}`,
                        // Style
                        style: {
                            fill: '#888',
                            fontSize: 14,
                            fontWeight: 'bold',
                        },
                        // Rotation (optional)
                        rotate: 30, // or -45, etc.
                    },
                },

                colorField: 'type',
                 group: true,
                style: { 
                    maxWidth: 35,
                    radiusTopLeft: 2,
                    radiusTopRight: 2,
                    
                },
                interaction: { elementHighlight: { background: true } },
            },
            {
                data: transformData,
                type: 'line',
                shapeField: 'smooth',
                columnStyle: {
                },
                yField: 'value',
                style: { lineWidth: 2 },
                axis: { y: { position: 'right' } },
                interaction: {
                    tooltip: {
                        crosshairs: false,
                        marker: false,
                    },
                },
            },

        ],
    };



    return (<div className={'lg:col-span-1 card'}>
        <h6 className={'card-header font-bold'}>Revenue

            <WidgetHeaderButtons/>

        </h6>
        <div className={'p-6 space-y-3.5'}>
                {/* Existing revenue blocks */}
                <div className={'grid grid-cols-4 mb-9'}>
                    <div>
                        <h6 className={'flex-c'}>
                            Total revenue <Tooltip title={'Gross revenue without deducing charges, taxes, discount'}><i
                            className={'far fa-question-circle text-[11px] text-slate-400'}></i></Tooltip>
                        </h6>
                        <h4 className={'mt-1 flex-c gap-3'}>
                            {Money(sumBy(data.result, (x) => x.GrossRevenue))}
                            <SalesChange range={data.resultComparor?.range} today={sumBy(data.result, (x) => x.GrossRevenue)} yesterday={data.resultComparor?.GrossRevenue} />
                        </h4>
                    </div>
                    <div>
                        <h6 className={'flex-c'}>
                            Charges <Tooltip title={'Card, delivery, packing charges'}><i
                            className={'far fa-question-circle text-[11px] text-slate-400'}></i></Tooltip>
                        </h6>
                        <h4 className={'mt-1 flex-c gap-3'}>
                            {Money(sumBy(data.result, (x) => x.Charges))}
                            <SalesChange range={data.resultComparor?.range}  today={sumBy(data.result, (x) => x.Charges)}
                                         yesterday={data.resultComparor?.Charges}/>
                        </h4>
                    </div>
                    <div>
                        <h6 className={'flex-c'}>
                            Taxes <Tooltip title={'All your GST, VAT, CESS taxes'}><i
                            className={'far fa-question-circle text-[11px] text-slate-400'}></i></Tooltip>
                        </h6>
                        <h4 className={'mt-1 flex-c gap-3'}>
                            {Money(sumBy(data.result, (x) => x.Tax))}
                            <SalesChange range={data.resultComparor?.range}  today={sumBy(data.result, (x) => x.Tax)}
                                         yesterday={data.resultComparor?.Tax}/>
                        </h4>
                    </div>
                    <div>
                        <h6 className={'flex-c'}>
                            Net profit <Tooltip title={'Actual earn after card charges, taxes, packing charge, delivery charge'}><i
                            className={'far fa-question-circle text-[11px] text-slate-400'}></i></Tooltip>
                        </h6>
                        <h4 className={'mt-1 flex-c gap-3'}>
                            {Money(sumBy(data.result, (x) => x.NetRevenue))}
                            <SalesChange range={data.resultComparor?.range}  today={sumBy(data.result, (x) => x.NetRevenue)}
                                         yesterday={data.resultComparor?.NetRevenue}/>
                        </h4>

                    </div>
                </div>

            {/* Ant Design Histogram Chart */}
            <div className="h-[300px] -m-4">
                <DualAxes {...config} />
                {/*<Column {...config} />*/}
            </div>


        </div>
    </div>)
}

export default RevenueSummary;