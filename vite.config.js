import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import { resolve } from 'path';

export default defineConfig({
  build: {
    rollupOptions: {
      input: {
        businessapp: resolve(__dirname, 'businessapp.html'),
      },
    },
  },
  server: {
    host: true,
  },
  optimizeDeps: {
    include: ['react-apexcharts', 'apexcharts'],
    force: true // Force re-optimization
  },
  plugins: [
    react(),
    tailwindcss(),
    TanStackRouterVite(),
  ],
})