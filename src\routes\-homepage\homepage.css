/* Custom animations for homepage */
.animate-shiny-text {
  animation: shiny-text 8s infinite;
}

@keyframes shiny-text {

  0%,
  90%,
  100% {
    background-position: calc(-100% - var(--shiny-width)) 0;
  }

  30%,
  60% {
    background-position: calc(100% + var(--shiny-width)) 0;
  }
}

.circular-gallery {
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: grab;
}

.circular-gallery:active {
  cursor: grabbing;
}