import { motion } from "framer-motion";

// Simple cn utility function since we don't have the full lib
const cn = (...classes) => classes.filter(Boolean).join(" ");

export const AnimatedShinyText = ({
  children,
  className,
  shimmerWidth = 100,
  ...props
}) => {
  return (
    <span
      className={cn("relative inline-block overflow-hidden", className)}
      {...props}
    >
      {/* Base text */}
      <span className="text-neutral-600/70 dark:text-neutral-400/70">
        {children}
      </span>

      {/* Shiny overlay */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        initial={{ x: "-100%" }}
        animate={{ x: "100%" }}
        transition={{
          duration: 2,
          repeat: Infinity,
          repeatDelay: 3,
          ease: "easeInOut",
        }}
        style={{
          background:
            "linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)",
        }}
      />
    </span>
  );
};
