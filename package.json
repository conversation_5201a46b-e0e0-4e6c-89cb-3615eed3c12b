{"name": "react-admin-new", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && xcopy /E /Y /I dist \"C:\\Users\\<USER>\\Downloads\\QueenNet\\BackOffice\\wwwroot\"", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.4.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-router": "^1.121.0", "ag-grid-community": "^33.3.2", "ag-grid-enterprise": "^33.3.2", "ag-grid-react": "^33.3.2", "antd": "^5.21.4", "apexcharts": "^4.7.0", "axios": "^1.9.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.16.0", "framework7": "^8.3.4", "framework7-react": "^8.3.4", "immer": "^10.1.1", "insert-css": "^2.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "moment": "^2.30.1", "motion": "^12.23.11", "ogl": "^1.0.11", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "simplebar": "^6.3.1", "simplebar-react": "^3.3.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tanstack/router-devtools": "^1.58.3", "@tanstack/router-vite-plugin": "^1.58.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.14", "tailwindcss": "^4.0.0", "vite": "^5.4.10"}}