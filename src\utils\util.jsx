import {toast} from "sonner";
import {But<PERSON>, Form, Progress, Spin, Tooltip} from "antd";
import {LoadingOutlined} from "@ant-design/icons";
import React, {useEffect, useState} from "react";
import {AnimatePresence, motion} from "framer-motion";

export const ArrowDown = () => {
    return <i
        className={'fas fa-chevron-down text-xs'}></i>
}

export const loadingList = () => {
    return [{ className:'hover:!bg-white', label: <>
            <div className="animate-pulse space-y-6 pr-3">

                <div className="flex-c gap-2 mb-6 mt-2">
                    <p className="h-2.5 bg-gray-200/80 rounded-full w-[100px]"></p>
                </div>

                {[1, 2, 3, 4, 5, 6].map((item, index) => {
                    const width = `${40 + Math.random() * 40}%`; // 60% to 100%
                    return (
                        <div key={index} className="flex-c pl-2 gap-4">
                            <span className="min-w-6.5 min-h-6.5 block bg-gray-200/80 rounded-sm"></span>
                            <p className="h-2.5 bg-gray-200/70 rounded-full" style={{width}}></p>
                        </div>
                    );
                })}

                <div className="border-t border-gray-200/50 flex-c gap-2 pt-6 pb-2">
                    <p className="h-2.5 bg-gray-200/80 rounded-full w-[100px]"></p>
                </div>
            </div>
        </>
    }]
}


export const setupAccount = (data) => {
    localStorage.setItem('jwt', data.data.JWT)
    localStorage.setItem('outletName', data.data.outlet.Name)
    localStorage.setItem('outletArea', data.data.outlet.Area)
    localStorage.setItem('outletCity', data.data.outlet.City)
    localStorage.setItem('outletAddress', data.data.outlet.Address)
    localStorage.setItem('phone', data.data.phone)
    localStorage.setItem('outletAvtar', data.data.outlet.Avtar ?? '1')
}

export const Loading = () => {
    return <div className={'flex-c justify-center fixed bg-white top-0 left-0 w-full h-full z-50'}><Spin
        size={'default'} indicator={<LoadingOutlined style={{fontSize: 48}} spin/>}/></div>
}


export const Media = ({image, title, description, badge}) => (<div className={'flex-c gap-2 max-w-full'}><img className={'min-w-[27px] mr-2 rounded'}
                                                                        src={image}/>
    <div className={'flex-c w-full'}>
        <div>
            <h6 className={'capitalize'}>{title?.toLowerCase()}</h6>
            <h6 className="text-sm opacity-60 truncate max-w-[200px] w-full">
                {description && description.charAt(0).toUpperCase() + description.slice(1).toLowerCase()}
            </h6>
        </div>
        <span className={'ms-auto mr-2.5'}>{badge}</span>
        {/*<i className={'far ms-auto mr-2.5 text-sm fa-star'}></i>*/}
    </div>
</div>)

export const cell = (rendererFn) => (params) => {
    if (params.node.group) return null;
    return rendererFn(params);
};

export const Money = (value) => {
    if (value == null) {
        return '-'
    }
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0
    }).format(value || 0);
};

export const SourceEnum = {
    0: 'POS',
    1: 'KIOSK',
    2: 'QR',
    3: 'ZOMATO',
    4: 'SWIGGY',
    5: 'Captain',
};


export function AbbribateNumber(value,money) {
    if (value === null || value === undefined) return '';
    if (value >= 1e9) return (value / 1e9).toFixed(1).replace(/\.0$/, '') + 'B';
    if (value >= 1e6) return (value / 1e6).toFixed(1).replace(/\.0$/, '') + 'M';
    if (value >= 1e3) return (value / 1e3).toFixed(1).replace(/\.0$/, '') + 'K';
    return money ? Money(value).toString() : value.toString();
}

export const LiveOrderStatus = {
    0: 'Placed',
    1: 'Acknowledged',
    2: 'Ready',
    3: 'Picked',
    4: 'Cancelled',
    5: 'Delivered',
    6: 'Dispatched',
    7: 'Completed',
};


export const OrderStatusEnum = {
    Created: 0,
    Billed: 1,
    Settled: 2,
    Deleted: 3,
    Due: 4,
    UnPaid: 5,
};

export function EnumToArray(enumObject) {
    return Object.entries(enumObject)
        .filter(([key, value]) => !isNaN(value))
        .map(([label, value]) => ({
            label,
            value,
        }));
}


export const Capitalize = (text) => {
    if (!text) return '';
    const spaced = text.replace(/([a-z])([A-Z])/g, '$1 $2');
    return spaced.charAt(0).toUpperCase() + spaced.slice(1);
};

export const showToast = (title,description,showError) => {
    const toastId = toast.custom((t) => <SimpleToast t={t} title={title} description={description} showError={showError} />, {
        duration: 3000,
    });
    return toastId;
};


export const showToastProgress = () => {
    const toastId = toast.custom((t) => <ToastProgress t={t} />, {
        duration: 1000000,
    });
    return toastId;
};

export const SalesChange = ({ today, yesterday,range }) => {
    const getPercentageChange = () => {
        if (yesterday === 0) return today === 0 ? 0 : 100; // Avoid divide-by-zero
        return ((today - yesterday) / Math.abs(yesterday)) * 100;
    };

    const percentage = getPercentageChange();
    const isPositive = percentage >= 0;

    return (
        <>
            {isPositive && <Tooltip title={range+' - '+yesterday}>
                <p className={'text-green font-semibold'} color={'green'}><i
                    className={'fa fa-arrow-up'}></i> {Math.abs(percentage).toFixed(0)}%</p>
            </Tooltip>}
            {!isPositive && <Tooltip title={range+' - '+yesterday}><p className={'text-red font-semibold'} color={'red'}><i
                className={'fa fa-arrow-down'}></i> {Math.abs(percentage).toFixed(0)}%</p></Tooltip>}
        </>
    )
};

export const Dialog = ({width,footer, open, setOpen, children,formState,success,initValue,api,validate,saveButtonText }) => {


    const [visible, setVisible] = useState(false);

    useEffect(() => {
        if (open)
        {
            setVisible(true);

        }else{
            setVisible(false);

        }

    }, [open]);



    const [internalForm] = Form.useForm();
    const tableForm = formState || internalForm;
    const [sending,setSending] = useState(false);

    useEffect(()=>{
        if(open){
            setSending(false)
            try{
                tableForm.resetFields()
            }catch{}
        }
    },[open])

    const handleSave = async() =>{
        if(api==undefined){
            try{
                const values = await tableForm.validateFields();
                setSending(true)

                await success(values)
                setSending(false)
                setOpen(false)

            }catch{
            }
            try{
                const values = await tableForm.validateFields();
                setSending(true)

                await validate(values)
                setSending(false)
                setOpen(false)
            }catch{
            }

        }else{
            try{
                await tableForm.validateFields();
                tableForm.submit()
            }catch{
            }
        }
    }
    function mergeFormFinalValues(initialValues, formValues, submittedValues) {
        const final = { ...submittedValues };

        for (const key in formValues) {
            if (!(key in final)) {
                final[key] = formValues[key];
            }
        }

        for (const key in initialValues) {
            if (!(key in final)) {
                final[key] = initialValues[key];
            }
        }

        return final;
    }

    return (
        <AnimatePresence
            mode="wait"
            onExitComplete={() => {
                // This runs when both modal & backdrop have fully exited
                if (!open) setVisible(false);
            }}
        >
            {(open || visible) && (
                <motion.div
                    key="backdrop2"

                    className="fixed z-[100] flex items-start justify-center pt-25 bg-[#091e428a] top-0 left-0 w-full h-full"
                    onClick={(e) => {
                        if (e.target === e.currentTarget) setOpen(false)
                    }}
                    initial={{opacity: 0}}
                    animate={{opacity: 1}}
                    exit={{opacity: 0}}
                >
                    <motion.div
                        key="modal2"
                        className={"bg-white relative overflow-hidden shadow-xl rounded-md "}
                        style={{width:(width??400)+'px'}}
                        onClick={(e) => e.stopPropagation()}
                        initial={{opacity: 0, y: 30}}
                        animate={{opacity: 1, y: 0}}
                        exit={{opacity: 0, y: -30}}
                        transition={{duration: 0.3}}
                    >
                        <Button onClick={()=>setOpen(false)} className={'!absolute z-50 right-2 top-2'} type={'text'} icon={<i className={'fa fa-xmark'} />}></Button>
                        <Form className={'w-full h-full'} form={tableForm} initialValues={initValue}
                              layout={"vertical"} onFinish={async (values) => {
                            setSending(true)
                            // var varities = formState.getFieldValue('Varity')
                            // var allfieldvalues = formState.getFieldsValue(true);
                            values = mergeFormFinalValues(initValue, tableForm.getFieldsValue(true), values);
                            if (postString) {
                                values = JSON.stringify(JSON.stringify(values));
                            }
                            try {
                                var result = await apiLocalClient.post(api, postJson || postString ? values : formData(values), {
                                    headers: {"Content-Type": "application/json"}
                                })
                                handleClose()
                                success(result.data)
                            } catch {
                                // notification({title:'Something went wrong',description:result.data.message})
                                setSending(false)
                            }


                        }}>

                            {children}

                            {!footer && <div className={'flex-c p-7 pt-0 justify-end gap-3'}>
                                <Button onClick={() => setOpen(false)} type={'text'}>Cancel</Button>
                                <Button onClick={handleSave} loading={sending}
                                        type={'primary'}>{saveButtonText ?? 'OK'}</Button>
                            </div>}

                        </Form>
                    </motion.div>

                </motion.div>
            )}
        </AnimatePresence>
    );
};


const ToastProgress = ({t}) => {
    const [percent, setPercent] = useState(0)

    useEffect(() => {
        const interval = setInterval(() => {
            setPercent((prev) => {
                if (prev >= 95) {
                    clearInterval(interval)
                    return 95
                }
                return prev + Math.floor(Math.random() * 10)
            })
        }, 50)

        return () => clearInterval(interval)
    }, [])

    return (
        <div
            className="p-5 flex items-start gap-5 border border-gray-200 bg-white dark:bg-zinc-900 rounded-lg shadow-md w-[330px]">
            <div className="relative mt-0.5  flex-c justify-center">
                <Spin size={'default'} indicator={<LoadingOutlined spin/>}/>
                <i className="fa fa-download absolute inset-0 mt-[4px] ml-[5px] z-50 text-primary text-xs"/>
            </div>

            <div className={'w-full'}>
                <div className={'flex-c'}><h6 className="font-semibold mb-1 ">File downloading..</h6> <Button
                    onClick={() => toast.dismiss(t)} className={'!ms-auto'} type={'text'} size={'small'}
                    icon={<i className={'fa fa-xmark'}/>}/></div>
                <p className=" font-normal heading-0">Please wait until the download is completed.</p>
                <Progress className={'my-1'} showInfo={false} percent={percent} status="active" size="small" />
                <Button className={'mt-2 -ml-2.5 !text-sm'} onClick={() => toast.dismiss(t)} type="link" size="small">Cancel</Button>

            </div>
        </div>
    )
}


const SimpleToast = ({t,showError,title,description}) => {
    return (
        <div
            className="p-5 flex items-start gap-5 border border-gray-200 bg-white dark:bg-zinc-900 rounded-lg shadow-md w-[330px]">
            <div className="relative mt-0.5  flex-c justify-center">
                <i className="fa fa-check-circle absolute inset-0 mt-[4px] ml-[5px] z-50 text-[#22A06B] text-[18px]"/>
            </div>

            <div className={'w-full ml-6'}>
                <div className={'flex-c'}><h6 className="font-semibold mb-1 ">{title}</h6> <Button
                    onClick={() => toast.dismiss(t)} className={'!ms-auto'} type={'text'} size={'small'}
                    icon={<i className={'fa fa-xmark'}/>}/></div>
                <p className="!font-normal heading-0 pr-3">{description}</p>
                <Button className={'mt-2 -ml-2.5 !text-sm'} onClick={() => toast.dismiss(t)} type="link" size="small">Cancel</Button>
            </div>
        </div>
    )
}