import {Button, Tag} from "antd";
import {useEffect, useState} from "react";
import api from "../../../utils/api.js";
import {AbbribateNumber, Money} from "../../../utils/util.jsx";
import {maxBy} from "lodash";
import moment from "moment";


const PeriodicSale = () => {
  const [data,setData] = useState([])
  const [date, setDate] = useState([moment().startOf('month').toISOString(), moment().endOf('month').toISOString()])
  useEffect(()=>{
    api.get('/dashboard/GetAllWeekSale?from='+date[0]+'&to='+date[1]).then((res) => {
      setData(res.data)
    }
    )
  },[])
  return (<div className={'lg:col-span-1 card'}>
    <h6 className={'card-header font-bold'}>Highlights
      <div className={'ms-auto'}><Button size={'small'} type={'text'}
                                         icon={<i className={'fa fa-refresh'}></i>}></Button></div>
    </h6>
    <div className={'p-6 space-y-3.5'}>
      <div>
        <h6>All time sales</h6>
        <h2 className={'mt-1 flex-c gap-3'}>{AbbribateNumber(maxBy(data, 'Value')?.Value)} <Tag bordered={false} className={'!mt-1'}
                                                         color={'green'}><i
            className={'fa fa-arrow-trend-up'}></i> {maxBy(data, 'Value')?.Period}</Tag></h2>

      </div>
      <div className={'flex-c gap-1 mb-0 w-full'}>
        {data?.map((o)=> (
            <div style={{width:o.Percentage+'%', background:o.Color}} className={'rounded-[2px] h-[8px] '}></div>
        ))}

      </div>
      <div className={'flex-c border-b border-gray-300/70 !py-7 gap-8'}>
        {data?.map((o)=> (
            <p className={'flex-c gap-1'}>
              <div style={{background:o.Color}} className={' w-1.5 h-1.5 rounded-full'}></div>
              {o.Period}
            </p>
        ))}


      </div>
      <div className={'space-y-3 !mt-6'}>
        {data?.map((o)=> (<div className={'flex-c'}>
          <h6 className={'!font-normal text-slate-500  gap-2.5 flex-c'}>
            {o.Period == 'Morning' && <i className={'opacity-80 fa fa-sun'}></i>}
            {o.Period == 'Evening' && <i className={'opacity-80 fa fa-cloud-moon-rain'}></i>}
            {o.Period == 'Night' && <i className={'opacity-80 fa fa-moon-cloud'}></i>}
            {o.Period == 'Midnight' && <i className={'opacity-80 fa fa-stars'}></i>}

            {o.Period}
          </h6>
          <div className={'flex-c gap-7.5 ms-auto'}>
            <h6 className={'!font-bold '}>{Money(o.Value)}</h6>
            <h6 className={'gap-1.5 min-w-[50px] flex-c'}><i
                className={(o.arrow=="arrow-up"?"text-green":"text-red")+' text-sm  fa fa-'+o.arrow}></i>{o.Percentage.toFixed(1)}%</h6>
          </div>
        </div>))}



      </div>
    </div>
  </div>)
  }

export default PeriodicSale;