// import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, Home, Instagram, Github, Youtube, Twitter } from "lucide-react"
import { But<PERSON> } from "antd"

export default function Footer() {
  return (
    <footer className="bg-black text-white">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="container mx-auto px-6 py-16 lg:py-24">
          <div className="text-center mb-12">
            <h2 className="text-4xl lg:text-6xl font-bold mb-8 leading-tight">
              Transforming potential
              <br />
              into performance
            </h2>

            <div className="flex items-center justify-center gap-2 mb-12">
              <div className="flex items-center gap-1">
                <span className="text-sm font-semibold bg-gray-800 px-2 py-1 rounded">G2</span>
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </div>
              <span className="text-gray-400 text-sm">630+ Reviews</span>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {/* Testimonial Card */}
            <div className="bg-gray-900 p-8 rounded-lg">
              <blockquote className="text-lg mb-6 leading-relaxed">
                "FoodyQueen had everything we needed to streamline our restaurant operations in no time. The attention
                to detail in this POS system is simply remarkable."
              </blockquote>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold">JB</span>
                </div>
                <div>
                  <div className="font-semibold">John B</div>
                  <div className="text-gray-400 text-sm">Restaurant Manager</div>
                </div>
              </div>
            </div>

            {/* CTA Card */}
            <div className="relative bg-gradient-to-br from-orange-400 via-purple-500 to-purple-600 p-8 rounded-lg overflow-hidden">
              <div className="relative z-10 flex items-center justify-center h-full">
                <Button
                  size="lg"
                  className="bg-white text-black hover:bg-gray-100 font-semibold px-8 py-3 rounded-full"
                >
                  Get FoodyQueen now
                </Button>
              </div>
              <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 via-purple-500/20 to-purple-600/20"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer as */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-6 py-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
            {/* Pages */}
            <div>
              <h3 className="font-semibold mb-4 text-white">Pages</h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Overview
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Pricing
                  </a>
                </li>
              </ul>
            </div>

            {/* More pages */}
            <div>
              <h3 className="font-semibold mb-4 text-white">More pages</h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Menu Management
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Order Tracking
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Analytics
                  </a>
                </li>
              </ul>
            </div>

            {/* Information */}
            <div>
              <h3 className="font-semibold mb-4 text-white">Information</h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    User Guide
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Updates
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    Terms of Service
                  </a>
                </li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="font-semibold mb-4 text-white">Support</h3>
              <ul className="space-y-3">
                <li>
                  <span className="text-gray-400">+614 3029 1827</span>
                </li>
                <li>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <EMAIL>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="flex flex-col md:flex-row items-center justify-between pt-8 border-t border-gray-800">
            <div className="flex items-center gap-2 mb-4 md:mb-0">
              <Home className="w-5 h-5" />
              <span className="font-semibold text-lg">foodyqueen</span>
            </div>

            <div className="text-gray-400 text-sm mb-4 md:mb-0">© Medium Rare powered by Webflow</div>

            <div className="flex items-center gap-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Youtube className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
