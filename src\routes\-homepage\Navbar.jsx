import { motion } from "framer-motion";

const Navbar = () => {
  const navItems = [
    { label: "AI POS", href: "#features" },
    { label: "KIOSK", href: "#about" },
    { label: "AdStudio", href: "#pricing" },
    { label: "KDS", href: "#blog" },
    { label: "Prices", href: "#career" },
  ];

  return (
    <div className="relative">
      {/* Gradient background layer */}
      <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 via-pink-500 via-purple-600 to-blue-600 h-40"></div>

      {/* White background layer with rounded corners */}
      <div className="absolute inset-0 bg-white rounded-t-3xl h-32 -mb-16 top-8"></div>

      <motion.nav
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative w-full bg-white/80 backdrop-blur-md border-b border-gray-100 rounded-3xl"
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between px-8 py-4">
          {/* Logo */}
          <motion.div
            transition={{ delay: 0.2, duration: 0.5 }}
            className="flex items-center space-x-2"
          >
            <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">F</span>
            </div>
            <span className="text-xl font-bold text-gray-900">FoodyQueen</span>
          </motion.div>

          {/* Navigation Items */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item, index) => (
              <motion.a
                key={item.label}
                href={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1, duration: 0.5 }}
                whileHover={{ scale: 1.05 }}
                className="text-gray-600 hover:text-gray-900 hover:bg-gray-50 px-3 py-2 rounded-lg transition-all duration-200 text-sm font-medium cursor-pointer"
              >
                {item.label}
              </motion.a>
            ))}
          </div>

          {/* Auth Buttons */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            className="flex items-center space-x-4"
          >
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900 px-6 py-2.5 rounded-full transition-all duration-200 font-medium cursor-pointer border-none shadow-sm hover:shadow-md"
            >
              Sign in →
            </motion.button>
          </motion.div>
        </div>
      </motion.nav>
    </div>
  );
};

export default Navbar;
