import React from 'react'
import ReactDOM from 'react-dom/client'
import {RouterProvider, createRouter} from '@tanstack/react-router'
import {routeTree} from './routeTree.gen'
import './index.css'
import {ConfigProvider} from "antd";
import {Toaster} from "sonner";

// Create a new router instance
const router = createRouter({routeTree})

ReactDOM.createRoot(document.getElementById('root')).render(
    <React.StrictMode>
        <ConfigProvider
            motion={{
                // Override default motion for dropdown components
                dropdown: {
                    motionName: 'ant-fade',
                    motionAppear: true,
                    motionEnter: true,
                    motionLeave: true,
                    motionDeadline: 300
                }
            }}
            theme={{
                token: {
                    fontFamily: '"OpenSans"',
                    // fontWeightStrong: 800,
                    fontSize: "var(--text-size)",
                    // fontWeightNormal: 800,
                    colorPrimary: '#1868db',
                    colorLink: '#1868db',
                    "colorTextBase": "#505258",
                    colorLinkHover: '#0749a6',
                    // Disable default blur/focus effects
                    boxShadow: 'none',
                    borderRadius: 4,
                    // This affects all components that inherit from the global border radius
                    borderRadiusLG: 4,
                    borderRadiusSM: 4,
                    borderRadiusXS: 4,
                },
                components: {
                    "Dropdown": {
                        "paddingBlock": 9,
                        "controlPaddingHorizontal": 16

                    },


                    // fontWeightStrong: 800,
                    // fontWeightNormal: 800,
                    Button: {
                        "borderColorDisabled": "rgba(217,217,217,0)",
                        defaultShadow: 'none',
                        primaryShadow: 'none',
                        fontWeight: 600,
                        "paddingInline": 10,
                        "colorText": "var(--text-color)",
                        "textTextColor": "var(--text-color)",
                        "textHoverBg": "var(--text-button-hover)",
                        dangerShadow: 'none',
                        // Button hover styles
                        colorHover: 'red', // Lighter blue on hover
                        colorPrimaryHover: '#0946ae', // Lighter blue on hover
                        defaultHoverBg: 'rgba(45,46,49,0.04)', // Lighter blue on hover
                        defaultHoverBorderColor: '#d9d9d9', // Lighter blue on hover
                        defaultHoverColor: '#172b4d', // Lighter blue on hover
                        colorPrimaryTextHover: '#000', // Text color on hover
                        colorPrimaryBorderHover: 'red', // Border color on hover
                        // Default button hover styles
                        colorTextHover: '#ff4073', // Text color on hover for default buttons
                        colorBorderHover: 'red', // Border color on hover for default buttons
                    },
                    Input: {
                        colorBorder: 'var(--input-border)',
                        controlOutline: '#1890ff',
                        hoverBorderColor: 'var(--input-border)',
                        activeShadow: 'none',
                        paddingBlock:8,
                        "paddingBlockSM": 6,
                        "paddingInline": 12,
                        "paddingInlineSM": 12,
                    },
                    "InputNumber": {
                        "paddingBlock": 1
                    },
                    "DatePicker": {
                        "controlHeight": 35,
                        colorBorder: 'var(--input-border)',
                        hoverBorderColor: '#2D2E313F',
                        activeShadow: 'none',

                    }
                },
            }}
        >

            <Toaster position="bottom-left" richColors  />
                <RouterProvider router={router}/>
        </ConfigProvider>
    </React.StrictMode>,
)
