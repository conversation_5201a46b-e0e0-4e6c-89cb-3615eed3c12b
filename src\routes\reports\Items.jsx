import { createFileRoute } from '@tanstack/react-router'
import {EnumToArray, OrderStatusEnum, SourceEnum} from "../../utils/util.jsx";
import {Tag} from "antd";
import {AGGridBase} from "../../component/AGGridBase.jsx";
import moment from "moment/moment.js";

export const Route = createFileRoute('/reports/Items')({
  component: RouteComponent,
})


function RouteComponent() {

  const summary = [
    { field: "Taxable", type: "sum", positionColumn:'Taxable',cellTemplate: (summary) => (summary.Taxable) },
    { field: "TotalAmount", type: "sum", cellTemplate: (summary) => (summary.TotalAmount) },
    { field: "TotalTax", type: "sum", cellTemplate: (summary) => (summary.TotalTax) },
    { field: "Name", type: "count",positionColumn: 'Name',cellTemplate: (summary) => (summary.Name+' COUNT') },
  ]

  const filterArray = []

  const colDef = [
    {field: "Name",width:250,hide:true},
    {field: "Category", enableRowGroup: true,
      rowGroup: true, cellRenderer: (params) => {
        return <div className={'text-slate-500'}>{params.value}</div>
      },
      hide: true},
    {field: "Quntity",aggregate:'Sum',cellRenderer: (params) => {
        return <div className={'flex-c gap-2'}><i className={'far fa-box'}></i> {params.value + ' orders'} </div>
      }
    },

    {field: "CreatedOn"},
    {field: "TotalTax",money:true},
    {field: "Dep",headerName:'Department'},
    {field: "Taxable",aggregate:'Sum',money:true},
    {field: "TotalAmount",aggregate:'Sum',money:true},
    {field: "Month",cellRenderer: (params) => {
        const date = moment(params.value, "M-YYYY");
        const formatted = date.isValid() ? date.format("MMMM YYYY") : params.value;

        return (
            <Tag bordered={false} className="!text-xs">
              {formatted}
            </Tag>
        );
      }},
  ]

  return <AGGridBase defaultDate={'This month'} sortArray={[{colId: 'CreatedOn', sort: 'desc'}]}
                     group={[{label: 'Category'}, {label: 'Dep'},{label: 'Month'}]}
                     filterList={filterArray}
                     title={"O"}
                     summary={summary}
                     column={colDef}
                     loadURL={'/AGOrder/GetItems'}/>
}
