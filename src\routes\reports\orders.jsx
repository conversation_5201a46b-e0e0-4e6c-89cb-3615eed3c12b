import { createFileRoute } from '@tanstack/react-router'
import {AGGridBase} from "../../component/AGGridBase.jsx";
import React, {useEffect, useState} from "react";
import {Affix, Button, Collapse, Divider, Dropdown, Input, Tabs, Tag, Tooltip, Typography} from "antd";
import {ArrowDown, EnumToArray, LiveOrderStatus, Money, OrderStatusEnum, SourceEnum} from "../../utils/util.jsx";
import moment from "moment";
import {AnimatePresence, hover} from "framer-motion";
import api from "../../utils/api.js";
const { Text, Link } = Typography;

export const Route = createFileRoute('/reports/orders')({
    component: RouteComponent,
})

function RouteComponent() {

    const summary = [
        { field: "Taxable", type: "sum", positionColumn:'Taxable',cellTemplate: (summary) => (summary.Taxable) },
        { field: "TotalTax", type: "sum", positionColumn:'TotalTax',cellTemplate: (summary) => (summary.TotalTax) },
        { field: "SettleAmount", type: "sum", cellTemplate: (summary) => (summary.SettleAmount) },
        { field: "ID", type: "count",positionColumn: 'BillNo',cellTemplate: (summary) => (summary.ID+' COUNT') },
    ]
    const [orderId,setOrderId] = useState(null)

    const filterArray = [
        {key:'Status',operator:'TagBox', value:EnumToArray(OrderStatusEnum),checked:[OrderStatusEnum.Settled]},
    ]

    const colDef = [
        {
            field: "BillNo", headerName:'Bill No #', hide: true,flex:null, width:240,  cellRenderer: params => {
                return (
                    <Tooltip placement={'topLeft'} title={'Viw details'}>
                        <span onClick={() => setOrderId(params.data.ID)}
                              className="  w-full flex items-center group  cursor-pointer">
    <div style={{lineHeight:0}} className="hover:bg-gray-200  text-slate-500 h-[22px] flex-c px-1.5 gap-1 rounded">
       {params.data.Type === "DineIn" &&
           <img src={'/images/home.svg?q=5'} width={14} alt="Dine In"/>}
        {params.data.Type === "Parcel" &&
            <img src={'/images/Group 2192.svg?q=5'} width={14} alt="Dine In"/>}
        <span className={''}>{'#' + params.data.BillNo}</span>
    </div>
  </span>
                    </Tooltip>
);
            }
        },
        {
            field: "Type",  icon: 'box-taped', enableRowGroup: true, rowGroup:true,hide:true, cellRenderer: (params) => {
                if (params.value == 'Parcel') {
                    return <Tag bordered={false} className={'small'}>{params.value}</Tag>
                } else if (params.value == 'DineIn') {
                    return <Tag color={'purple'} bordered={false} className={'small'}>{params.value}</Tag>
                } else if (params.value == 'Delivery') {
                    return <Tag color={'blue'} bordered={false} className={'small'}>{params.value}</Tag>
                }else{
                    return <Tag color={'warning'} bordered={false} className={'small'}>{params.value}</Tag>
                }
            }
        },
        {field: "Taxable",sortable: true,icon:'indian-rupee-sign',money:true,headerName: 'Taxable'},
        {field: "CreatedOn",sortable: true,icon:'clock'},
        {field: "BillBy",headerName:'User',icon:'light clipboard-user',sortable: true,cellRenderer: (params) => (
                <div className={'flex-c gap-2'}>
                    <img className="w-6" src="/images/default-avatar (1).png"/>
                    {params.value}
                </div>
            )},
        {field: "TotalTax",icon:'building-columns',money:true,headerName: 'Tax'},
        {
            field: "SettleAmount",

            money: true,
            aggregate:'Sum',
            icon: '#check-circle',
            headerName: 'Settle',
        },
        {field: "PayBy",icon:'credit-card-blank',headerName: 'Mode'},
        {field: "Source",icon:'code-pull-request-draft',cellRenderer: params => {
                return <span className={'opacity-50 font-semibold'}>{ SourceEnum[params.value]}</span>
            }},
        {
            field: "Status",icon:'circle-exclamation-check', cellRenderer: params => {
                if(params.value==2){

                    return  <Tag color={'green'} bordered={false} className="small">Paid</Tag>
                }else{
                    return params.value
                }

            }
        },
        {width:65,pinned: 'right',cellRenderer: (params) => (<div className={'flex items-center justify-end'}>
                <Dropdown trigger={['click']} menu={{items:[
                    {onClick:()=>setOrderId(params.data.ID), label:'View details'},
                        {type:'divider'},
                    {onClick:()=>downloadReport(params.data.CreatedOn), label:'Copy blockchain id'},
                    {onClick:()=>downloadReport(params.data.CreatedOn), label:'Mark unpaid'},
                    {onClick:()=>downloadReport(params.data.CreatedOn), label:'Delete bill'},
                    ]}}>
                    <Button className={'!bg-gray-100 hover:!bg-gray-300 mt-1.5'} icon={<i className={'fa fa-ellipsis '} />} type={'text'} size={'small'}></Button>

                </Dropdown>

            </div>)}
        // {
        //     field: "groupSummary", headerName: "SettleAmount", valueGetter: params => params.node.group
        //         ? params.data?.aggData?.groupSummary
        //         : params.data?.groupSummary }
    ]



    return  <AGGridBase
        sortArray={[{colId: 'CreatedOn',sort: 'asc'}, {colId: 'BillNo'}]}
        defaultDate={'Last 7 days'}
        // sortArray={[{colId: 'CreatedOn', sort: 'asc'}, {colId: 'BillNo'}]}
                    group={[{label: 'Type'}, {label: 'Status'}, {label: 'Source'}, {label: 'PayBy'}]}
                    filterList={filterArray}
                    title={"O"}
                    summary={summary}
                    column={colDef}
                    loadURL={'/AGOrder/Get'}>
         <OrderDetail id={orderId}/>
    </AGGridBase>
}

import { motion } from "framer-motion";
import SimpleBar from "simplebar-react";
import {filter} from "lodash";

const OrderDetail = ({id}) => {
    const [order, setOrder] = useState(null);
    useEffect(() => {
        api.get('/agorder/GetById?id=' + id).then((res) => {
            setOrder(res.data);
        });
    }, [id]);

    return (<>
            <AnimatePresence>
                {order && <motion.div
                    className={'ml-10 flex flex-col'}
                    initial={{width: 0}}
                    animate={{width: 600}}
                    exit={{width: 0}}

                    transition={{duration: 0.2}}
                    style={{overflow: "hidden"}}
                >
                            <span className="w-full border-b border-gray-300 pb-1 mb-1 flex items-center  ">
                <h6 style={{lineHeight: 0}}
                    className="  text-slate-500 h-[22px] flex-c gap-2 rounded">
                    {order.Type === "DineIn" &&
                        <img src={'/images/home.svg?q=5'} width={16} alt="Dine In"/>}
                    {order.Type === "Parcel" &&
                        <img src={'/images/Group 2192.svg?q=5'} width={16} alt="Dine In"/>}
                    <span className={''}>Bill {'#' + order.BillNo}</span>
                </h6>
                   <div className={'ms-auto'}>
                        <Dropdown menu={{
                            items: [
                                {onClick: () => downloadReport(params.data.CreatedOn), label: 'Copy blockchain id'},
                                {onClick: () => downloadReport(params.data.CreatedOn), label: 'Mark unpaid'},
                                {onClick: () => downloadReport(params.data.CreatedOn), label: 'Delete bill'}
                            ]
                        }}>
                            <Button type={'text'} icon={<i className={'fa fa-ellipsis'}></i>}></Button>
                        </Dropdown>
                        <Button onClick={() => setOrder(null)} type={'text'}
                                icon={<i className={'fa fa-xmark'}></i>}></Button>
                   </div>
            </span>
                    <div className={'relative flex-1'}>
                        <SimpleBar
                            style={{
                                maxHeight: '100%',
                                position: 'absolute', // <- Absolutely positioned
                                inset: 0, // top: 0; right: 0; bottom: 0; left: 0;
                            }}
                            autoHide={true} // optional
                        >
                            <Tabs rootClassName={'no-border'} defaultActiveKey="1" items={[
                                {
                                    key: '1',
                                    label: 'Detail',
                                    children: <div>


                                        <div className={'space-y-5.5 my-3'}>
                                            <div className={'flex-c'}>
                                                <h6 className={'min-w-[150px] opacity-80 flex-c font-semibold'}>Status</h6>
                                                <Tag
                                                    color={'green'}
                                                    bordered={false}>PAID</Tag>
                                            </div>
                                            <div className={'flex-c'}>
                                                <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Actual
                                                    Date</h6> <h6>{moment(order.ActualDate).format('dddd, DD MMMM YYYY hh:mm A')}</h6>
                                            </div>
                                            <div className={'flex-c'}>
                                                <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Cashier</h6>
                                                <h6><img
                                                    className="w-5"
                                                    src="/images/default-avatar (1).png"/> {order.BillBy}
                                                </h6>
                                            </div>
                                            <div className={'flex-c'}>
                                                <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Payment
                                                    Mode</h6> <h6>{order.PayBy}
                                            </h6>
                                            </div>

                                            <div>
                                                <Collapse defaultActiveKey={['1']}
                                                          items={[{
                                                              key: '1', label: 'Details', children:

                                                                  <div className={'space-y-5'}>
                                                                      <div className={'flex-c'}>
                                                                          <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Order
                                                                              type</h6>
                                                                          <h6>{order.Type}
                                                                          </h6>
                                                                      </div>

                                                                      <div className={'flex-c'}>
                                                                          <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Table</h6>
                                                                          <h6>{order.Table}
                                                                          </h6>
                                                                      </div>

                                                                      <div className={'flex-c'}>
                                                                          <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Source</h6>
                                                                          <h6>{SourceEnum[order.Source]}
                                                                          </h6>
                                                                      </div>

                                                                      <div className={'flex-c'}>
                                                                          <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Customer
                                                                              detail</h6>
                                                                          <h6>{order.Name??'N/A'}
                                                                          </h6>
                                                                      </div>

                                                                      <div className={'flex-c'}>
                                                                          <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Total
                                                                              person</h6>
                                                                          <h6>{order.Person} Person
                                                                          </h6>
                                                                      </div>

                                                                      <div className={'flex-c'}>
                                                                          <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Assigned
                                                                              waiter</h6>
                                                                          <h6>{order.Waiter??'-'}
                                                                          </h6>
                                                                      </div>

                                                                  </div>

                                                          }]}
                                                />
                                            </div>

                                            <div>
                                                <Collapse
                                                    items={[{
                                                        key: '1', label: 'Online order', children: <div className={''}>

                                                            <div className={'space-y-5'}>
                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Order#</h6>
                                                                    <h6>{order.ZwigatoOrderId??'-'}
                                                                    </h6>
                                                                </div>

                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Order
                                                                        Status</h6>
                                                                    <h6>{LiveOrderStatus[order.LiveStatus]}
                                                                    </h6>
                                                                </div>

                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Rider</h6>
                                                                    <h6>{order.RiderDetail.FullName??'N/A'}
                                                                    </h6>
                                                                </div>

                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Address</h6>
                                                                    <h6>{order.Address??'N/A'}
                                                                    </h6>
                                                                </div>

                                                                <div className={''}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Instruction</h6>
                                                                    <p className={'mt-1'}>{order.SingleNote??'N/A instruction'}
                                                                    </p>
                                                                </div>


                                                            </div>
                                                        </div>
                                                    }]}/>
                                            </div>

                                            <div>
                                                <Collapse
                                                    items={[{
                                                        key: '1', label: 'Structure', children: <div className={''}>

                                                            <div className={'space-y-5'}>
                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Taxable</h6>
                                                                    <h6>{Money(order.Taxable)}
                                                                    </h6>
                                                                </div>

                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Tax</h6>
                                                                    <h6>{Money(order.TotalTax)}
                                                                    </h6>
                                                                </div>

                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Discount</h6>
                                                                    <h6>{Money(order.Discount)}
                                                                    </h6>
                                                                </div>

                                                                <div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Round
                                                                        Off</h6>
                                                                    <h6>{Money(order.RoundOff)}
                                                                    </h6>
                                                                </div><div className={'flex-c'}>
                                                                    <h6 className={'min-w-[150px]  opacity-80 flex-c font-semibold'}>Settle Amount</h6>
                                                                    <h6>{Money(order.SettleAmount)}
                                                                    </h6>
                                                                </div>



                                                            </div>
                                                        </div>
                                                    }]}/>
                                            </div>


                                        </div>
                                    </div>,
                                },
                                {
                                    key: '2',
                                    label: 'Items',
                                    children: <div className={' rounded-lg overflow-hidden'}>
                                       <table className="w-full border-0">
                                         <thead>
                                           <tr className={'border-b-2 font-bold border-gray-200'}>
                                             <th className="text-start  p-2.5 text-sm ">Item</th>
                                             <th className="text-start  p-2.5 text-sm ">Qty</th>
                                             <th className="text-start  p-2.5 text-sm ">Price</th>
                                             <th className="text-start  p-2.5 text-sm ">Total</th>
                                           </tr>
                                         </thead>
                                         <tbody>
                                     {filter(order.Items,(x)=>(x.KdsItemStatus!=3)).map((x, idx) => (
                                         <tr
                                             key={idx}
                                             className={idx !== order.Items.length - 1 ? 'border-b border-gray-200' : ''}
                                         >
                                             <td className=" p-2.5">{x.Name}</td>
                                             <td className="p-2.5">{x.Quntity}</td>
                                             <td className=" p-2.5">{Money(x.Price)}</td>
                                             <td className=" p-2.5">{Money(x.TotalAmount)}</td>
                                         </tr>
                                     ))}
                                         {/* Add more rows as needed */}
                                         </tbody>
                                       </table>
                                    </div>,
                                },
                                {
                                    key: '3',
                                    label: 'Inventory',
                                    children: 'There is no track available',
                                }

                                ]}/>

                        </SimpleBar>
                    </div>

                </motion.div>}
            </AnimatePresence>
        </>
    )
        ;
}