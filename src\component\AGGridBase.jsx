// Set AG Grid Enterprise license
import { createFileRoute } from '@tanstack/react-router';
import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import { AgGridReact } from "ag-grid-react";
// import "ag-grid-community/styles/ag-grid.css";
// import "ag-grid-community/styles/ag-theme-alpine.css";
import { LoadingOutlined } from '@ant-design/icons';
import {Calendar, DatePicker, Form, InputNumber, Spin, Tooltip} from 'antd';
import { AnimatePresence, motion } from 'framer-motion';
import { themeQuartz } from 'ag-grid-community';
import { LicenseManager, ServerSideRowModelModule, RowGroupingModule,RowGroupingPanelModule } from "ag-grid-enterprise";
import { ModuleRegistry,PinnedRowModule ,RowStyleModule} from "ag-grid-community";
import {
    assign,
    debounce,
    filter,
    find,
    includes,
    isEmpty,
    map,
    pullAt,
    reject,
    sumBy,
    uniqBy,
    without,
    xor
} from "lodash";
import api from "../utils/api.js";
import {Button, Dropdown, Input, Tag} from "antd";
import {ArrowDown, Capitalize, Dialog, Money, showToast, showToastProgress} from "../utils/util.jsx";
import animationData from './../../public/images/lottie/connection-failed.json';
import Lottie from "lottie-react";
import moment from "moment/moment.js";
import {DayPicker} from "react-day-picker";
import 'react-day-picker/dist/style.css';
import dayjs from "dayjs";
import {toast} from "sonner";
import {produce} from "immer";


// AG Grid license
// LicenseManager.setLicenseKey("[TRIAL]_this_{AG_Charts_and_AG_Grid}_Enterprise_key_{AG-087410}_is_granted_for_evaluation_only___Use_in_production_is_not_permitted___Please_report_misuse_to_legal@ag-grid.com___For_help_with_purchasing_a_production_key_please_contact_info@ag-grid.com___You_are_granted_a_{Single_Application}_Developer_License_for_one_application_only___All_Front-End_JavaScript_developers_working_on_the_application_would_need_to_be_licensed___This_key_will_deactivate_on_{14 June 2025}____[v3]_[0102]_MTc0OTg1NTYwMDAwMA==d32caadaa45d7052a15febfa3ab0a37e");

// Register AG Grid modules
ModuleRegistry.registerModules([ServerSideRowModelModule,RowStyleModule, PinnedRowModule,RowGroupingPanelModule,RowGroupingModule]);


const CustomNoRowsOverlay = (props) => {
    return (
        <motion.div
            initial={{opacity: 0}}
            animate={{opacity: 1}}
            exit={{opacity: 0}}
            transition={{duration: 0.3}}
            className="flex flex-col items-center"
        >
            <Lottie className="w-[170px]" animationData={animationData} loop/>
            <h6 className="text-lg mb-1">There is no data to display</h6>
            <h6 className="text-sm mb-5 opacity-50">Please try changing the filters or search</h6>
        </motion.div>
    );
};

const noRowsOverlayComponentParams = () => {
    return {
        noRowsMessageFunc: () =>
            "No rows found at: " + new Date().toLocaleTimeString(),
    };
};

const CustomInnerHeader = (props) => {

    const icon =  props.column.getColDef().icon;
    return (
        <span>
      {icon && <i className={(icon.startsWith('#') ? 'fas' : 'far')+` fa-${icon.replace('#','')} text-slate-500`} style={{fontSize: 15, marginRight: 7 }}></i>}
            {props.displayName}
    </span>
    );


    // return (<div className="customInnerHeader">
    //     {props.icon && <i className={`fa ${props.icon}`}></i>}
    //     <span>{props.displayName}</span>
    // </div>);
};



const NoRowsOverlay = () => (
    <div style={{textAlign: "center", padding: 20}}>
        <img
            src="/empty.png"
            alt="No Data"
            style={{width: 150, opacity: 0.6}}
        />
        <div style={{marginTop: 10, color: "#888"}}>No data available</div>
    </div>
);

const CustomDropdown = ({defaultDate, range,setRange,addFilter,removeFilter}) => {
    const [open, setOpen] = useState(false);
const [dateOpen,setDateOpen] = useState(false)
    const [secondDateOpen,setSecondDateOpen] = useState(false)
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);

const startDateRef = useRef()
    const [firstDateView,setFirstDateView] = useState(false)
    const [secondDateView,setSecondDateView] = useState(false)

    const secondDateRef = useRef()

    const handleSelect = (selectedRange) => {
        debugger
        var isSecondSelected = range?.to

        setRange(selectedRange);

        if(isSecondSelected){
            setEndDate(dayjs(selectedRange.to))
            setTimeout(()=>{
                addFilter('CreatedOn', 'range', [selectedRange.from, selectedRange.to]);
                setOpen(false)
            },200)
        }else{
            secondDateRef.current?.focus?.()
           setStartDate(dayjs(selectedRange.from))
        }

        // if (selectedRange?.from && selectedRange?.to) {
        //     // ✅ Only fires when both start and end dates are selected
        //     console.log('Selected range:', selectedRange.from, selectedRange.to);
        //
        //     // Example function: replace with your logic
        //     //addFilter('CreatedOn', 'range', [selectedRange.from, selectedRange.to]);
        // }
    };

    return (
        <Dropdown
            trigger={['click']}
            open={open}
            overlayClassName={'selectable'}
            onOpenChange={(open)=>{

                setOpen(open)
                setFirstDateView(false)
                setSecondDateView(false)

                setTimeout(() => {
                    if(defaultDate && document.querySelectorAll('.ant-dropdown-menu-item-selected').length ==0){
                        const items = document.querySelectorAll('.ant-dropdown-menu-item');
                        items.forEach(el => {
                            if (el.textContent.includes(defaultDate)) {
                                el.classList.add('ant-dropdown-menu-item-selected');
                            }
                        });
                    }

                }, 50);


            }}

            popupRender={(menu) => (
                <div
                    className="w-[295px] max-h-[550px] ant-dropdown-menu ant-dropdown-menu-root ant-dropdown-menu-vertical ant-dropdown"
                    style={{
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        borderRadius: 6,
                        background: '#fff',
                    }}
                >
                    <div
                        layout
                        initial={{opacity: 0}}
                        animate={{opacity: 1}}
                        transition={{duration: 0.3}}
                    >
                        <div className="border-r border-gray-200">
                            <div mode="wait">
                                {/* Only animate the first open */}
                                {(!firstDateView && !secondDateView) && (
                                    <div key="menu">
                                        {/* Header */}
                                        <div className="ant-dropdown-menu-item no-hover">
                                            Date Filter
                                            {range?.from && (
                                                <h6
                                                    onClick={() => {
                                                        setOpen(false);
                                                        removeFilter('CreatedOn');
                                                        setRange(null);
                                                        setStartDate(null);
                                                        setEndDate(null);
                                                        document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                            el.classList.remove('ant-dropdown-menu-item-selected')
                                                        );
                                                    }}
                                                    className="link text-sm ms-auto"
                                                >
                                                    Clear
                                                </h6>
                                            )}
                                        </div>

                                        {/* Today */}
                                        <div className="ant-dropdown-menu-item date-range" onClick={(e) => {
                                            document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                el.classList.remove('ant-dropdown-menu-item-selected')
                                            );
                                            e.currentTarget.classList.add('ant-dropdown-menu-item-selected');

                                            const today = moment().format('YYYY-MM-DD');

                                            setRange({ from: today, to: today });
                                            addFilter('CreatedOn', 'range', [today, today]);
                                            setOpen(false);
                                        }}>
                                            Today <span className="ms-auto text-sm opacity-50">{moment().format('MMM D')}</span>
                                        </div>

                                        {/* Yesterday */}
                                        <div className="ant-dropdown-menu-item date-range" onClick={(e) => {
                                            document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                el.classList.remove('ant-dropdown-menu-item-selected')
                                            );
                                            e.currentTarget.classList.add('ant-dropdown-menu-item-selected');

                                            const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');

                                            setRange({ from: yesterday, to: yesterday });
                                            addFilter('CreatedOn', 'range', [yesterday, yesterday]);
                                            setOpen(false);
                                        }}>
                                            Yesterday <span className="ms-auto text-sm opacity-50">{moment().subtract(1, 'day').format('MMM D')}</span>
                                        </div>

                                        {/* Last 7 Days */}
                                        <div className="ant-dropdown-menu-item date-range" onClick={(e) => {
                                            document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                el.classList.remove('ant-dropdown-menu-item-selected')
                                            );
                                            e.currentTarget.classList.add('ant-dropdown-menu-item-selected');

                                            const from = moment().subtract(6, 'days').format('YYYY-MM-DD');
                                            const to = moment().format('YYYY-MM-DD');

                                            setRange({ from, to });
                                            addFilter('CreatedOn', 'range', [from, to]);
                                            setOpen(false);
                                        }}>
                                            Last 7 days <span className="ms-auto text-sm opacity-50">{`${moment().subtract(6, 'days').format('MMM D')} - ${moment().format('MMM D')}`}</span>
                                        </div>

                                        {/* This Week */}
                                        <div className="ant-dropdown-menu-item date-range" onClick={(e) => {
                                            document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                el.classList.remove('ant-dropdown-menu-item-selected')
                                            );
                                            e.currentTarget.classList.add('ant-dropdown-menu-item-selected');

                                            const from = moment().startOf('week').format('YYYY-MM-DD');
                                            const to = moment().endOf('week').format('YYYY-MM-DD');

                                            setRange({ from, to });
                                            addFilter('CreatedOn', 'range', [from, to]);
                                            setOpen(false);
                                        }}>
                                            This week <span className="ms-auto text-sm opacity-50">{`${moment().startOf('week').format('MMM D')} - ${moment().endOf('week').format('MMM D')}`}</span>
                                        </div>

                                        {/* This Month */}
                                        <div className="ant-dropdown-menu-item date-range" onClick={(e) => {
                                            document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                el.classList.remove('ant-dropdown-menu-item-selected')
                                            );
                                            e.currentTarget.classList.add('ant-dropdown-menu-item-selected');

                                            const from = moment().startOf('month').format('YYYY-MM-DD');
                                            const to = moment().endOf('month').format('YYYY-MM-DD');

                                            setRange({ from, to });
                                            addFilter('CreatedOn', 'range', [from, to]);
                                            setOpen(false);
                                        }}>
                                            This month <span className="ms-auto text-sm opacity-50">{`${moment().startOf('month').format('MMM D')} - ${moment().endOf('month').format('MMM D')}`}</span>
                                        </div>

                                        {/* Last Month */}
                                        <div className="ant-dropdown-menu-item date-range" onClick={(e) => {
                                            document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                el.classList.remove('ant-dropdown-menu-item-selected')
                                            );
                                            e.currentTarget.classList.add('ant-dropdown-menu-item-selected');

                                            const from = moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
                                            const to = moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD');

                                            setRange({ from, to });
                                            addFilter('CreatedOn', 'range', [from, to]);
                                            setOpen(false);
                                        }}>
                                            Last month <span className="ms-auto text-sm opacity-50">{`${moment().subtract(1, 'month').startOf('month').format('MMM D')} - ${moment().subtract(1, 'month').endOf('month').format('MMM D')}`}</span>
                                        </div>

                                        {/* Last 3 Months */}
                                        <div className="ant-dropdown-menu-item date-range" onClick={(e) => {
                                            document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                                                el.classList.remove('ant-dropdown-menu-item-selected')
                                            );
                                            e.currentTarget.classList.add('ant-dropdown-menu-item-selected');


                                            const from = moment().subtract(3, 'months').startOf('day').format('YYYY-MM-DD');
                                            const to = moment().endOf('day').format('YYYY-MM-DD');

                                            setRange({ from, to });
                                            addFilter('CreatedOn', 'range', [from, to]);
                                            setOpen(false);
                                        }}>
                                            Last 3 months <span className="ms-auto text-sm opacity-50">{`${moment().subtract(3, 'month').startOf('month').format('MMM D')} - ${moment().subtract(1, 'month').endOf('month').format('MMM D')}`}</span>
                                        </div>
                                    </div>
                                )}



                                {/* Calendars (no motion here to avoid re-animation) */}
                                <div mode="sync">
                                    {(firstDateView || secondDateView) && (
                                        <motion.div
                                            key={firstDateView ? 'first' : 'second'}
                                            initial={{ x: 15, opacity: 0 }}
                                            animate={{ x: 0, opacity: 1 }}
                                            exit={{ x: -15, opacity: 0 }}
                                            transition={{ duration: 0.3 }}
                                            className="relative overflow-hidden"
                                        >
                                            {firstDateView && (
                                                <div className={''}>
                                                    <DayPicker
                                                        mode="range"
                                                        navLayout={'around'}
                                                        captionLayout={'dropdown'}
                                                        animate
                                                        showOutsideDays
                                                        selected={range}
                                                        onSelect={handleSelect}
                                                    />
                                                    {/*<Calendar*/}
                                                    {/*    onPanelChange={()=>{alert('v')}}*/}
                                                    {/*    onChange={()=>alert('1')}*/}
                                                    {/*    onSelect={(data) => {*/}
                                                    {/*        alert('vv')*/}
                                                    {/*        secondDateRef.current?.focus?.();*/}
                                                    {/*        setStartDate(data);*/}
                                                    {/*        setFirstDateView(false);*/}
                                                    {/*        setSecondDateView(true);*/}

                                                    {/*        var firstDate = data;*/}
                                                    {/*        setEndDate(data);*/}
                                                    {/*        var secondDate = secondDate;*/}
                                                    {/*        if (firstDate && secondDate) {*/}
                                                    {/*            addFilter('CreatedOn', 'range', [startDate, endDate]);*/}
                                                    {/*        }*/}

                                                    {/*    }}*/}
                                                    {/*    fullscreen={false}*/}
                                                    {/*/>*/}
                                                </div>
                                            )}

                                            {secondDateView && (
                                                <>
                                                    <h6 className="font-bold top-3 left-4 absolute">End date</h6>
                                                    <Calendar
                                                        onSelect={(data) => {
                                                            var firstDate = startDate;
                                                            setEndDate(data);
                                                            var secondDate = data;
                                                               if (firstDate && secondDate) {
                                                                   addFilter('CreatedOn', 'range', [startDate, endDate]);
                                                               }
                                                            setOpen(false);

                                                        }}
                                                        fullscreen={false}
                                                    />
                                                </>
                                            )}
                                        </motion.div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Bottom section untouched */}
                        <div className="border-t p-4 mt-1 border-gray-200">
                            {!firstDateView && <h6 className="font-bold text-sm mb-4">Custom</h6>}
                            <div className="flex-c">
                                <div>
                                    <h6 className="text-sm mb-1 !font-semibold">Start</h6>
                                    <DatePicker
                                        size={'small'}

                                        value={startDate}
                                        // format={{
                                        //     format: 'YYYY-MM-DD',
                                        //     type: 'mask',
                                        // }}
                                        onOpenChange={(open) => {
                                            if(open) {
                                                setStartDate(null)
                                                setEndDate(null)
                                                setRange({ from: null });
                                                setFirstDateView(true);
                                            }
                                        }}
                                        onKeyDown={(e)=>{
                                            if (e.key === 'Enter') {
                                               //handleSelect({from:startDate})
                                                // perform your action
                                            }
                                        }}
                                        open={false}

                                    />
                                </div>
                                <i className="fas fa-arrow-right mx-3 mt-5"></i>
                                <div>
                                    <h6 className="text-sm mb-1 !font-semibold">End</h6>
                                    <DatePicker
                                        size={'small'}
                                        value={endDate}
                                        ref={secondDateRef}
                                        onOpenChange={setSecondDateOpen}
                                        open={false}

                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        >
            <Button type="text" className={(range?.from ? 'ant-dropdown-open' : '')}>
                <div className="flex-c gap-2">
                    <i className="mt-[1px] far fa-clock"/> Date
                </div>
                <div className={'flex-c'}>
                {range?.from && <Tag onClick={() => {
                    removeFilter('CreatedOn')
                    setRange(null)
                    setStartDate(null)
                    setEndDate(null)

                    document.querySelectorAll('.date-range.ant-dropdown-menu-item-selected').forEach(el =>
                        el.classList.remove('ant-dropdown-menu-item-selected')
                    );

                    setTimeout(() => {
                        setOpen(false)
                    }, 1)
                }} color={'#1868db'} className={'small '}><i className={'fa fa-xmark'}/></Tag>
                }
                    <ArrowDown/>
                </div>
            </Button>
        </Dropdown>
    );
};

export const AGGridBase = ({loadURL,defaultDate, summary,column, title, group,sortArray,filterList,children}) => {
    const gridRef = useRef();
    const [gridApi, setGridApi] = useState(null);
    const [loading, setLoading] = useState(true)
    const [selectedGroup, setSelectedGroup] = useState(find(column, {rowGroup: true})?.field);
    const [openSort, setOpenSort] = useState(false);
    const [lastSortModel, setLastSortModel] = useState([]);
    const [openFilter,setOpenFilter] = useState(false);
const [whatsAppOpen,setWhatsappOpen] = useState(false)
    const [range, setRange] = useState(undefined);

    const [filterArray, setFilterArray] = useState(filterList);

    const totalCheckedCount = sumBy(filterArray, item => item.checked?.length || 0);
console.log(selectedGroup)
    const [colDef, setColDef] = useState(column.map(col => {
        if (col.field === "CreatedOn") {
            return {
                ...col,
                headerName: "Date",
                sortable: true,
            };
        }
        // if(selectedGroup==null){
        //     return {
        //         ...col,
        //         hide: false,
        //     };
        // }
        return col;
    }));
console.log(colDef)
    const [extraSortModel, setExtraSortModel] = useState(sortArray??[]);
    const filters = useRef([]);
    useEffect(()=>{
    if(defaultDate=='Last 7 days'){
        const from = moment().subtract(6, 'days').format('YYYY-MM-DD');
        const to = moment().format('YYYY-MM-DD');
        setRange({from:from, to:to});
        addFilter('CreatedOn', 'range', [from, to],false);
    }
    else if (defaultDate === 'Last 3 months') {
        const from = moment().subtract(3, 'months').startOf('day').format('YYYY-MM-DD');
        const to = moment().endOf('day').format('YYYY-MM-DD');
        setRange({ from, to });
        addFilter('CreatedOn', 'range', [from, to], false);
    }else if (defaultDate === 'This month') {
        const from = moment().startOf('month').format('YYYY-MM-DD');
        const to = moment().endOf('month').format('YYYY-MM-DD');
        setRange({ from, to });
        addFilter('CreatedOn', 'range', [from, to], false);
    }



    },[defaultDate])

    const toggleValue = (key, label) => {
        let updatedFilters;

        setFilterArray(prev => {
            const produceData = produce(prev, draft => {
                const item = find(draft, { key });
                if (!item) return;

                if (includes(item.checked, label)) {
                    item.checked = without(item.checked, label);
                } else {
                    item.checked.push(label);
                }
            });

            updatedFilters = produceData; // Capture updated state
            return produceData;
        });

        // Delay setting datasource to ensure state updates
        setTimeout(() => {
            gridRef.current.setGridOption('serverSideDatasource', datasource(updatedFilters));
        }, 0);
    };

    function callApiWithFilters() {
        // const where = filters.map(f => {
        //     if (f.operator === 'contains') return `${f.field}.Contains("${f.value}")`;
        //     if (f.operator === '==') return `${f.field} == "${f.value}"`;
        //     if (f.operator === 'range') {
        //         const [start, end] = f.value;
        //         return `${f.field} >= DateTime.Parse("${start}") AND ${f.field} <= DateTime.Parse("${end}")`;
        //     }
        //     return '';
        // }).join(' OR ');

        gridRef.current.setGridOption('serverSideDatasource', datasource());

        // // Call API with the built where clause
        // fetch('/api/data', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify({ where }),
        // })
        //     .then(res => res.json())
        //     .then(data => {
        //         console.log('Filtered result:', data);
        //     });
    }


    function removeFilter(field) {
        const index = filters.current.findIndex(f => f.field === field);
        if (index !== -1) {
            filters.current.splice(index, 1);
        }

        callApiWithFilters(); // Auto-call API
    }

    function addFilter(field, operator, value,doApiCall=true) {

        const index = filters.current.findIndex(f => f.field === field);
        if (index !== -1) {
            filters.current[index] = {field, operator, value};
        } else {
            filters.current.push({field, operator, value});
        }
if(doApiCall){
    callApiWithFilters(); // Auto-call API
}
    }

       function addSort(field, value,clickFromTag) {
        const index = extraSortModel.findIndex(f => f.colId === field);
        if (index !== -1) {
            //pullAt(extraSortModel, index)
            var what = extraSortModel[index]
            if(isEmpty(what?.sort) || clickFromTag){
                extraSortModel[index] = {colId:field, sort: value};
            }else{
                    extraSortModel[index] = {colId:field};
            }
        } else {
            extraSortModel.push({colId:field, sort: value});
        }

        callApiWithFilters(); // Auto-call API
    }




//     addFilter('name', 'contains', 'john');
// // → Automatically hits API with `name.Contains("john")`
//
//     addFilter('createdDate', 'range', ['2024-01-01', '2024-01-31']);
// // → API now gets both filters
//
//     removeFilter('name');
// // → Now only date range filter goes to API
//
//




    useEffect(() => {
        if (selectedGroup == null) {
            colDef.unshift({
                headerCheckboxSelection: true,
                checkboxSelection: true,
                width: 50,
                pinned: 'left',
            });
        }
    }, [selectedGroup]);

    useEffect(() => {
        window.groupCol = selectedGroup
    }, [selectedGroup]);



// Create a custom cell renderer for amount fields
    const AmountCellRenderer = (props) => {
        if (props.node.rowPinned && (props.value === null || props.value === undefined || props.value === '')) {
            return null;
        }


        if (props.node.group == true && !props.column.colDef.aggregate) {
            return null;
        }

        if(props.value==0){
            if (props.node.group == true) {
                return null;
            }
            if (props.node.rowPinned) {
                return null;
            }

            return "-"
        }
        return <span>{Money(props.value)}</span>;
    };

    const processedColDef = colDef.map(col => {
        if (col.aggregate) {
            return {
                ...col,
                valueGetter: col.valueGetter || ((params) => {
                    if (params.node.group) {
                        return params.node?.data?.aggData?.[col.field] ?? '';
                    }
                    return params.data?.[col.field];
                }),
                cellRenderer: col.cellRenderer || ((params) => {
                    if (params.node.group) {
                        return <strong>{params.value}</strong>;
                    }
                    return params.value;
                })
            };
        }
        return col;
    });

// Modify your columnDefs initialization
    const [columnDefs] = useState(processedColDef.map(col => {
        const originalRenderer = col.cellRenderer;

        const colDef = {
            ...col,
        };

        if (col.money === true) {

            colDef.cellRenderer = AmountCellRenderer;
        } else {
            colDef.cellRenderer = (props) => {

                if (props.node.rowPinned && (props.value === null || props.value === undefined || props.value === '')) {
                    return null;
                }
                // Skip rendering group rows (except aggregate columns)
                if (props.node.group === true && !props.column.colDef.aggregate) {
                    return null;
                }

                if(props.column.colDef.field=="CreatedOn"){
                    props.column.colDef.headerName="Date"
                    if(props.value==undefined){
                        return "-"
                    }
                    return  (<div className={'text-slate-500'}><Tooltip title={moment(props.value).format('DD MMM, YYYY h:mm:ss A')}>{moment(props.value).format('DD MMM, YYYY')}</Tooltip></div>)
                }
                // Use original renderer if present
                if (typeof originalRenderer === 'function') {
                    if(props.value==undefined && props.pinned!="right"){

                        return "-"
                    }
                    return originalRenderer(props);
                }

                // Default fallback
                return props.value;
            };
        }


        return colDef;
    }));

    const searchText = useRef("");
    const [show,setShow] = useState(false);
    // Server-side datasource
    const datasource = (productFilterArray) => {
        return {
            getRows: async (params) => {
                const { startRow, endRow, sortModel, filterModel, groupKeys, rowGroupCols } = params.request;


                const adjustedSortModel = uniqBy(map(reject(sortModel, (x)=>(x.colId==selectedGroup || isEmpty(x?.sort))), (sortItem) => {

                    if (sortItem.colId === 'ag-Grid-AutoColumn') {
                        var field = filter(colDef,(x)=>(x?.headerCheckboxSelection!=true))[0].field

                        return assign({}, sortItem, { colId: field });
                    }
                    return sortItem;
                }).concat(reject(extraSortModel, (x)=>(isEmpty(x?.sort)))), 'colId');

                setLastSortModel(adjustedSortModel);


                // 🔹 Call your filter update method
                // 🔁 Build and return the new filters array
                var modified = filters.current.concat((productFilterArray??filterArray)
                    .filter(f => f.checked?.length > 0)
                    .map(f => ({
                        field: f.key,
                        operator: 'in',
                        value: [...f.checked],
                    })));


                const requestPayload = {
                    startRow,
                    endRow,
                    filters:modified,
                    sortModel:adjustedSortModel,
                    filterModel,
                    summaryFields: summary,
                    rowGroupCols,
                    extraAggregationOfGroup:filter(colDef,(x)=>x?.aggregate=="Sum")?.map(x=>x.field),
                    searchText:searchText.current,
                    groupKeys,
                };
                console.log(requestPayload)
                setLoading(true)

                try {
                    const response = await api.post(loadURL, requestPayload);

                    const result = response.data;

                    // Modify the rowData so that "Category" contains the correct value for grouping
                    const level = requestPayload.groupKeys?.length || 0;
                    debugger
                    const isGrouping = requestPayload.rowGroupCols?.length > level;

                    const groupField = requestPayload.rowGroupCols?.[level]?.field;

                    const rowData = isGrouping
                        ? result.rows.map(item => {
                            // Extract group field
                            const row = {
                                [groupField]: item.group,
                                group: true,
                                aggData: {}
                            };

                            // Dynamically move all other keys into aggData except `group`
                            for (const key in item) {
                                if (key !== "group") {
                                    row.aggData[key] = item[key];
                                }
                            }

                            return row;
                        })
                        : result.rows;
                    debugger;
                    if(rowData.length == 0){
                        params.api.showNoRowsOverlay();
                        // gridRef.current.setGridOption('serverSideDatasource', datasource);

                    }else{
                        params.api.hideOverlay()
                    }

                    params.success({
                        rowData,
                        rowCount: result.lastRow,
                    });



                    if (true) {
                        // if (data.summary) {
                        const summaryRow = {};

                        requestPayload.summaryFields.forEach(({ field, positionColumn, cellTemplate }) => {
                            summaryRow[positionColumn??field] = cellTemplate(result.summary);
                        });

// Set the pinned bottom row
                        params.api.setGridOption('pinnedBottomRowData', [summaryRow]);
                    } else {
                        params.api.setGridOption('pinnedBottomRowData', []);
                    }

                    setTimeout(()=>{
                        setLoading(false)
                    },200)
                    // params.api.setPinnedBottomRowData([{
                    //     "ProductName": "Grand Total",
                    //     "Price": 34567.89
                    //     // other summary fields
                    // }]);

                    // if (!isGrouping && result.totalSummary != null) {
                    //     gridRef.current?.setPinnedBottomRowData([
                    //         {
                    //             Category: 'Grand Total',
                    //             Price: result.totalSummary,
                    //         }
                    //     ]);
                    // }

                    // if (!isGrouping && result.totalSummary) {
                    //     gridRef.current?.setPinnedBottomRowData([
                    //         {
                    //             Name: 'Grand Total',
                    //             Price: '',
                    //             groupSummary: result.totalSummary,
                    //         }
                    //     ]);
                    // }

                    // const rowData = isGrouping
                    //     ? result.rows.map(item => ({
                    //         Category: item.group,
                    //         group: true,
                    //         aggData: { groupSummary: item.groupSummary },
                    //     }))
                    //     : result.rows;  // At leaf level, return the actual rows without transforming
                    //
                    //
                    // params.success({
                    //     rowData,
                    //     rowCount: result.lastRow,  // Ensure lastRow is properly passed
                    // });

                } catch (error) {
                    console.error(error);
                    params.fail();
                }
            }
        }
    };

    class CustomLoadingCellRenderer {
        init(params) {
            this.eGui = document.createElement('div');
            this.eGui.innerHTML = `
      <div class="skeleton-row">
        <div class="skeleton-cell"></div>
        <div class="skeleton-cell"></div>
        <div class="skeleton-cell"></div>
      </div>
      <div class="skeleton-row">
        <div class="skeleton-cell"></div>
        <div class="skeleton-cell"></div>
        <div class="skeleton-cell"></div>
      </div>
      <!-- Add more rows as needed -->
    `;
        }

        getGui() {
            return this.eGui;
        }
    }




    // When grid is ready
    const onGridReady = (params) => {
        gridRef.current = params.api;

        setGridApi(params.api);
        params.api.setGridOption('serverSideDatasource', datasource());
    };

    const gridOptions = {
        loadingCellRenderer: 'customLoadingCellRenderer',
        rowSelection: 'multiple',
        headerHeight: 42,
        theme: themeQuartz,
        components: {
            customLoadingCellRenderer: CustomLoadingCellRenderer,
        },
    };

    const handleExport =async({mobile}) => {
        if (!gridRef.current) return;

        // Extract current grid state
        const filterModel = gridRef.current.getFilterModel();

        const sortModel = lastSortModel;

        const adjustedSortModel = uniqBy(map(reject(sortModel, { colId: selectedGroup }), (sortItem) => {

            if (sortItem.colId === 'ag-Grid-AutoColumn') {
                var field = filter(colDef,(x)=>(x?.headerCheckboxSelection!=true))[0].field

                return assign({}, sortItem, { colId: field });
            }
            return sortItem;
        }), 'colId');




        const rowGroupCols = gridApi.getRowGroupColumns().map(col => ({ field: col.getColId() }));
        const groupKeys = []; // If you have specific group keys in your UI, pass them here

        var modified = filters.current.concat((filterArray)
            .filter(f => f.checked?.length > 0)
            .map(f => ({
                field: f.key,
                operator: 'in',
                value: [...f.checked],
            })));

        const requestPayload = {
            FilterModel: filterModel??{},
            exportColDef:colDef.map(x=>x.field),
            filters:modified,
            SortModel: adjustedSortModel,
            RowGroupCols: rowGroupCols,
            summaryFields: summary,
            GroupKeys: groupKeys,
            extraAggregationOfGroup:filter(colDef,(x)=>x?.aggregate=="Sum")?.map(x=>x.field),
            SearchText: searchText.current,
            StartRow: 0,    // ignore paging
            EndRow: 1000000 // large number to request all rows (though your server will ignore paging)
        };


        var toastId = null;

        if(mobile==undefined){
            toastId= showToastProgress("Document downloading","Your document is being generated, please wait...");
        }


        try {
            const response = await api.post(
                loadURL + '?excel=true&mobile=' + (mobile == undefined ? '' : mobile),
                requestPayload,
                {
                    responseType: 'blob', // important for file download
                    timeout: 0            // disables timeout
                }
            );
            if (response.status == 200) {
                if (mobile != undefined) {
                    showToast('Report sent successfully', 'Your report is sent successfully to mobile.');
                    return
                }

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;

            // Use a filename from response header or fallback
            const contentDisposition = response.headers['content-disposition'];
            let fileName = 'export.xlsx';
            if (contentDisposition) {
                const match = contentDisposition.match(/filename="?(.+)"?/);
                if (match?.length === 2) fileName = match[1];
            }

            link.setAttribute('download', fileName);
            document.body.appendChild(link);
            link.click();
            link.remove();

            try {
                toast.dismiss(toastId);
            } catch {
            }


                showToast('Document downloaded', 'Your document is downloaded successfully');


        }else{
                throw new Error(response.data);
            }
            // toast('Document downloaded successfully!');
        } catch (error) {
            console.error('Export failed', error);
            alert('Export failed. See console for details.');
        }
    }


    const debouncedSearch = useCallback(
        debounce((text) => {
            searchText.current = text;
            console.log(searchText.current)
            if (gridRef.current) {
                gridRef.current.setGridOption('serverSideDatasource', datasource());
            }
        }, 500),
        []
    );

    // const autoGroupColumnDef = useMemo(() => {
    //     return {
    //         minWidth: 200,
    //     };
    // }, []);


    //
    // const autoGroupColumnDef = useMemo(() => {
    //     debugger
    //     if (!gridApi) return {};
    //
    //     // Find the column that's currently being used for grouping
    //     const rowGroupColumns = gridApi.getRowGroupColumns();
    //     const groupCol = rowGroupColumns.length > 0 ? rowGroupColumns[0] : null;
    //     const groupField = groupCol?.getColId() || columnDefs[0].field || 'Group';
    //     const headerName = groupCol?.getColDef()?.headerName || columnDefs[0].headerName || groupField;
    //
    //     // Get the cellRenderer from the original column definition if it exists
    //     const originalColDef = columnDefs.find(col => col.field === groupField);
    //     const originalCellRenderer = originalColDef?.cellRenderer;
    //
    //     return {
    //         field: groupField,
    //         headerName: headerName,
    //         checkboxSelection: true,
    //         headerCheckboxSelection: true,
    //         minWidth: 250,
    //         cellRenderer: originalCellRenderer, // Use the original column's cellRenderer if available
    //         cellRendererParams: {
    //             suppressCount: true,
    //             innerRenderer: (params) => {
    //                 const groupName = params.value;
    //                 const groupValue = params.node?.data?.aggData?.groupSummary;
    //
    //                 // If this is a group row and we have a summary value
    //                 if (params.node.group && groupValue != null) {
    //                     return `${groupName} (${groupValue})`;
    //                 }
    //
    //                 // If we have an original cell renderer and this is not a group row
    //                 if (!params.node.group && originalCellRenderer) {
    //                     // Return the value to be processed by the original cell renderer
    //                     return params.value;
    //                 }
    //
    //                 // Default case
    //                 return groupName;
    //             }
    //         }
    //     };
    // }, [gridApi, columnDefs]);
    //
    //
    const autoGroupColumnDef = useMemo(() => {
        debugger
        if (!gridApi) return {};

        // const rowGroupColumns = gridApi.getRowGroupColumns();
        // const groupCol = rowGroupColumns.length > 0 ? rowGroupColumns[0] : null;
        // const groupField = groupCol?.getColId() || 'Group';
        // const headerName = groupCol?.getColDef()?.headerName || groupField;

        // const firstCol = columnDefs.find(col => !col.rowGroup && !col.hide) || columnDefs[0];
        const firstCol = columnDefs[0];
        const groupField = firstCol.field || 'Group';
        const headerName = firstCol.headerName || groupField;


        return {

            field: groupField,
            pinned:'left',
            width: find(colDef, {field: groupField})?.width??null,
            headerName: <span>
      {firstCol.icon &&
          <i className={(firstCol.icon.startsWith('#') ? 'fas' : 'far') + ` fa-${firstCol.icon.replace('#', '')} text-slate-500`}
             style={{fontSize: 15, marginRight: 7}}></i>}
                {headerName}
    </span>,
            checkboxSelection: true, // ✅ This ensures checkbox stays in the first (group) column
            headerCheckboxSelection: true,
            minWidth: 250,

            cellRendererParams: {
                innerRenderer: (params) => {
                    if (params.node?.rowPinned) {
                        return params.valueFormatted || params.value;
                    }
                    if (!params.node.group) {
                        const colCellRenderer = params.colDef.field;
                        var cellRendererFromFiel = find(colDef, {field: colCellRenderer})?.cellRenderer;
                        if (cellRendererFromFiel) {
                            return cellRendererFromFiel(params); // or return 'jigar' for testing
                        }

                        return params.valueFormatted || params.value;
                    }
                    const colDef2 = find(colDef, {field: window.groupCol});
// alert(colDef2.field)
                    const colCellRenderer = colDef2?.cellRenderer;

                    if (typeof colCellRenderer === 'function') {
                        // return colCellRenderer(params);
                        return colCellRenderer({...params, value: params.valueFormatted || params.value});
                    }

                    return params.valueFormatted || params.value;
                }
            }

            // cellRenderer: (params) => {
            //
            //     const colCellRenderer = colDef[1].cellRenderer;
            //
            //     if (typeof colCellRenderer === 'function') {
            //         // If grouped row, wrap renderer with group cell
            //         return colCellRenderer({ ...params, value: params.valueFormatted || params.value });
            //     }
            //
            //     // Default fallback
            //     return params.valueFormatted || params.value;
            // }

            // cellRendererParams: {
            //     suppressCount: true,
            //     innerRenderer: (params) => {
            //         debugger
            //         const groupName = params.value;
            //         const groupValue = params.node?.data?.aggData?.Count;
            //
            //         if (!params.node.group || groupValue == null) return groupName;
            //
            //         return `${groupName} (${groupValue})`;
            //     }
            // }
        };
    }, [gridApi]);

    const setGroupByField = (fieldName) => {
        debugger
        // Clone the columnDefs (to avoid mutating state directly)
        const newColDefs = columnDefs.map(col => {
            if (col.field === fieldName) {
                return { ...col, rowGroup: true, hide: true }; // group and hide grouped column
            } else {
                if(col.field === columnDefs[0].field) {

                    return { ...col, rowGroup: false, hide: false }; // ungroup and show others
                    // return { ...col, rowGroup: false, hide: fieldName==null?false: true }; // ungroup and show others
                }
                return { ...col, rowGroup: false, hide: false }; // ungroup and show others
            }
        });

        // Set new colDefs to grid via API
        // gridRef.current.api.setColumnDefs(newColDefs);
        setColDef(newColDefs);
        window.groupCol = fieldName
        gridApi.setGridOption("columnDefs", newColDefs);

    };
    const ref = useRef(null);

    const toggleFullscreen = () => {
        const el = ref.current;
        if (!document.fullscreenElement) {
            el.style.padding = '30px';
            el?.requestFullscreen?.();
        } else {
            el.style.padding = '0px';
            document.exitFullscreen?.();
        }
    };

    return (

        <motion.div
            ref={ref}
            className="h-full flex flex-col bg-white"
            initial={{scale: 1}}
            animate={{scale: 1}}
        >
            <Dialog saveButtonText={'Send'} validate={async(value)=>{
                await handleExport({mobile:value?.mobile})
            }} open={whatsAppOpen} setOpen={setWhatsappOpen}>
                <img className={'h-[120px] w-full object-cover'}
                     src={'https://jira-frontend-bifrost.prod-east.frontend.public.atl-paas.net/assets/HeaderBannerLight.0c11896b.png'}/>
                <div className={'p-7 space-y-5'}>
                    <h4>Send report via whatsapp</h4>
                    <p className={'mt-3'}>
                        Use this section to send the generated report directly to your team via WhatsApp. You can search
                        for your team members, compose the message.
                    </p>
                    <div>
                        <Form.Item className={'!mb-0'} rules={[{required: true}]} name={'mobile'}>
                            <Input prefix={<i className={'fa fa-search text-[14px] -ml-1 opacity-50'}/>}
                                   placeholder={'Enter or search mobile number'} className={''}/>
                        </Form.Item>

                    </div>

                </div>
            </Dialog>

            <div className={'flex-c'}>

                <div className={''}>
                    <p className={'capitalize text-base flex-c gap-2.5'}>{Capitalize(window.location.pathname.slice(1).replace("/", "/").split("/")[0])}
                        <span>/</span>
                        <span>{Capitalize(window.location.pathname.slice(1).replace("/", "/").split("/")[1])}</span>
                    </p>
                    <h4 className={'mt-1.5'}>{Capitalize(window.location.pathname.slice(1).replace("/", "/").split("/")[1])}</h4>
                </div>
                <div className={'ms-auto flex-c gap-1'}>
                    <Dropdown trigger={['click']}
                              menu={{
                                  items: [{
                                      onClick: () => {
                                          setTimeout(() => {
                                              handleExport({})
                                          }, 10)
                                      },
                                      icon: <img className={'w-7.5 !mr-3'} src={'/images/microsoft_excel_2019.svg'}/>,
                                      label: <div className={'flex-c'}>
                                          <div className={'w-full flex-c'}>
                                          <span className={'mr-[50px]'}>Excel

                                          </span>
                                              <Tag bordered={false} color={'purple'}
                                                   className={'small !ms-auto'}><i className={'fa fa-bolt'}></i> FAST</Tag>
                                          </div>
                                      </div>
                                  }, {
                                      type: 'divider',
                                  }, {
                                      label: 'Send whatsapp',icon: <img className={'w-7.5 !mr-3'} src={'/images/whatsapp.svg'} />, onClick: () => {
                                          setWhatsappOpen(true)
                                      }
                                  }]
                              }}>
                        <Button type="text"
                                icon={<i className='far text-lg fa-folder-arrow-down'/>}>Download<ArrowDown/></Button>

                    </Dropdown>
                    <Tooltip title={'Toggle Fullscreen'}>
                        <Button onClick={toggleFullscreen} type="text" icon={<i className='fa text-lg fa-expand'/>}/>
                    </Tooltip>
                </div>

            </div>

            <div className={'flex-c my-5  !mb-7.5 gap-2'}>
                <div className={'w-[135px]'}>
                    <Input size={'small'} prefix={<i className={'fa fa-search text-[14px] -ml-1 opacity-50'}/>}
                           onInput={(e) => debouncedSearch(e.target.value)}
                           placeholder="Search..."/>


                </div>
                <div className="flex items-center ml-2 mr-3.5 -space-x-2.5">
                    {/* First avatar - highest z-index so it is foremost */}
                    <img
                        className="w-8.5 h-8.5 rounded-full border-2 border-white z-30"
                        src="/images/default-avatar (1).png"
                        alt="Avatar 1"
                    />

                    {/* Other avatars with lower z-index */}
                    <Tooltip title={'Fodoyqueen Spider'}>
                        <img
                            className="w-8.5 h-8.5 rounded-full border-2 border-white z-20"
                            src="/images/icon.png"
                            alt="Avatar 2"
                        />
                    </Tooltip>

                    <div
                        className="w-8.5 h-8.5 flex items-center justify-center rounded-full border-2 border-white bg-slate-200 z-10">
                        <span className="font-bold text-xl opacity-60">+</span>
                    </div>
                </div>

                <Dropdown
                    trigger={['click']}
                    overlayClassName={'selectable'}
                    menu={{
                        style: {minWidth: '145px'},
                        selectable: true,
                        defaultSelectedKeys: [selectedGroup],
                        selectedKeys: [selectedGroup],
                        items: [{
                            className: 'no-hover',
                            onClick: (e) => {
                            },
                            label: <div className="flex-c">
                                Group By
                                {selectedGroup && <h6 onClick={() => {
                                    setSelectedGroup(null);
                                    setGroupByField(null);
                                }} className={'link text-sm ms-auto'}>Clear</h6>}
                            </div>
                        }].concat(group.map((item) => {
                            var icon = colDef.find(x => x.field == item.label)?.icon
                            return {
                                key: item.label,
                                icon: icon ?
                                    <i className={(icon.startsWith('#') ? 'fas' : 'far') + ` fa-${icon.replace('#', '')} `}
                                       style={{fontSize: 15, marginRight: 7}}></i> : null,
                                label: item.label.replace(/(?!^)([A-Z])/g, ' $1'),
                                onClick: () => {
                                    setSelectedGroup(item.label);
                                    setGroupByField(item.label);
                                }
                            }
                        })),
                    }}
                >
                    <Button className={(selectedGroup ? 'ant-dropdown-open' : '')} type="text">
                        <div className={'flex-c gap-1.5'}><i
                            className={'mt-[1px] fas fa-bars-sort '}/> Group{selectedGroup ? ":" : ""}{selectedGroup && <>
                            <Tag color={'#1868db'} bordered={false}
                                 className={'small !text-xs !text-white !-mr-[0px]'}>{selectedGroup}</Tag></>}<ArrowDown/></div>
                    </Button>
                </Dropdown>

                <Dropdown open={openSort}
                          overlayClassName={'selectable'}
                          onOpenChange={(open) => {
                              setOpenSort(open)
                          }}
                          popupRender={(menu) => (
                              <div
                                  className="w-[200px] max-h-[550px] ant-dropdown-menu ant-dropdown-menu-root ant-dropdown-menu-vertical ant-dropdown"
                                  style={{
                                      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                                      borderRadius: 6,
                                      background: '#fff',
                                  }}
                              >
                                  <div>
                                      <div className="border-r border-gray-200">
                                          <div mode="wait">
                                              <div className="ant-dropdown-menu-item no-hover">
                                                  Sort By
                                              </div>
                                          </div>
                                      </div>
                                  </div>

                                  {extraSortModel.map((item) => (
                                      <div
                                          className={"ant-dropdown-menu-item relative " + (!isEmpty(item?.sort) ? 'ant-dropdown-menu-item-selected' : '')}
                                          onClick={(e) => {
                                              // e.currentTarget.classList.add('ant-dropdown-menu-item-selected');
                                              //setOpen(false)
                                              addSort(item.colId, 'asc');
                                          }}>
                                          <span>{Capitalize(item.colId)}</span> {item.sort &&
                                          <div className={'ms-auto   do-not-hide'}>

                                              <Tooltip
                                                  title={(item.sort == 'asc' ? 'Mark Descending' : 'Marl Ascending')}>
                                                  <div onClick={(e) => {
                                                      e.stopPropagation()

                                                      if (item.sort == "desc") {
                                                          addSort(item.colId, 'asc', true);
                                                      } else {
                                                          addSort(item.colId, 'desc', true);
                                                      }
                                                      return
                                                  }} className={' p-3 right-10 top-0 flex-c justify-center  absolute'}>
                                                      <Tag bordered={false}
                                                           className={'text-sm !mr-0 opacity-50 bg-primary-btn'}>
                                                          {item.sort == 'asc' && <i
                                                              className={'fa fa-arrow-down mr-1 text-xs'}></i>}
                                                          {item.sort == 'desc' && <i
                                                              className={'fa fa-arrow-up mr-1 text-xs'}></i>}
                                                          {item.sort}</Tag>
                                                  </div>
                                              </Tooltip>

                                          </div>}
                                      </div>
                                  ))}


                              </div>


                          )}

                          trigger={['click']}
                          menu={{items: [{label: 'Pay By'}, {label: 'Order Type'}, {label: 'Cashier'}]}}>
                    <Button type="text">
                        <div className={'flex-c gap-2'}><i className={'mt-[1px] fa fa-shuffle'}/> Sort</div>
                        <ArrowDown/></Button>
                </Dropdown>

                <CustomDropdown defaultDate={defaultDate} range={range} setRange={setRange} addFilter={addFilter} removeFilter={removeFilter}/>


                <div className={'ms-auto gap-3 flex-c'}>

                    <Dropdown
                        trigger={['click']}
                        open={openFilter}
                        onOpenChange={(open)=>{
                            setOpenFilter(open)
                        }}
                        popupRender={(menu) => (
                            <div
                                className="w-[310px] max-h-[550px] ant-dropdown-menu ant-dropdown-menu-root ant-dropdown-menu-vertical ant-dropdown"
                                style={{
                                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                                    borderRadius: 6,
                                    background: '#fff',
                                }}
                            >
                                <div className="p-5.5 py-3 pb-0 no-hover">
                                    Advanced Filter
                                </div>
                                {filterArray.map((item, filterIndex) => (
                                    <div className={'border-b border-gray-200 last:border-b-0 '} key={filterIndex}>
                                        <h6 className="font-bold text-sm py-3 px-5.5">{item.key}</h6>
                                        <div className="flex-c flex-wrap gap-2 space-y-1 p-5.5 py-4 pt-0">
                                            {item.operator=="TagBox" && item.value.map((val, i) => {
                                                    const isChecked = item.checked.includes(val.value);
                                                    return (
                                                        <div
                                                            key={i}
                                                            onClick={() => toggleValue(item.key, val.value)}
                                                            className={
                                                                (isChecked ? 'bg-primary-light/50 text-primary outline-primary/60' : 'outline-gray-300 hover:bg-gray-100 ') +
                                                                ' outline-1  rounded-full px-2.5  transition-all text-sm font-semibold py-1'
                                                            }
                                                        >
                                                            {val.label}
                                                        </div>
                                                    );
                                                })}

                                            {item.operator=="NumberBox" && <>
                                            <InputNumber  />
                                            </>}
                                        </div>
                                    </div>
                                ))}

                            </div>


                        )}>
                        <Button disabled={filterArray.length==0} color="default" variant="filled"><i
                            className={'fa fa-filter-list'}/> Filter {totalCheckedCount > 0 && <Tag color={'#1868db'} className={'small !mr-0'}>{totalCheckedCount}</Tag>}<ArrowDown/></Button>
                    </Dropdown>


                    <Button onClick={() => {

                        gridRef.current.setGridOption('serverSideDatasource', datasource());
                        showToast('Data refreshed successfully', 'Your data is refreshed successfully.');


                    }} color="default" variant="filled"><i
                        className={'fa fa-refresh'}/></Button>

                    {/*className={'fa fa-folder-arrow-down'}/> Filter <ArrowDown/></Button>*/}

                </div>
            </div>
            <div className={'flex flex-1'}>
                <div className="ag-theme-alpine  h-full " style={{width: "100%"}}>
                    <div className={'relative'} style={{height: "100%", width: "100%"}}>
                        <AnimatePresence>
                            {loading && (
                                <motion.div
                                    initial={{y: 10, opacity: 0}}
                                    animate={{y: 0, opacity: 1}}
                                    exit={{y: 10, opacity: 0}}
                                    className="absolute z-50 bottom-16 left-0 w-full flex justify-center"
                                >
                                    <div
                                        className="bg-[#5E4DB2] text-white flex-c p-2.5 py-[7px] rounded-lg shadow-xl text-sm">
                                        <Spin
                                            size="small"
                                            indicator={<LoadingOutlined className="!text-white !mr-2" spin/>}
                                        />
                                        Loading..
                                    </div>
                                </motion.div>
                            )}
                        </AnimatePresence>
                        <AgGridReact
                            ref={gridRef}

                            noRowsOverlayComponent={CustomNoRowsOverlay}

                            rowData={null}
                            animateRows={true}
                            gridOptions={gridOptions}
                            groupDisplayType="singleColumn"


                            autoGroupColumnDef={autoGroupColumnDef}
                            suppressServerSideFullWidthLoadingRow={true}
                            getRowStyle={(params) => {

                                if (params.node.rowPinned) {
                                    return {
                                        fontWeight: 'bold',
                                        fontsize: '1 px',
                                        color: 'gray',
                                        backgroundColor: 'white'
                                    };
                                }
                                return null;

                            }}
                            rowGroupPanelShow="never"
                            rowModelType="serverSide"
                            paginationAutoPageSize={10}
                            suppressColumnResizeWhileDragging:false
                            cacheBlockSize={100}
                            // serverSideStoreType="partial"
                            columnDefs={columnDefs}
                            onGridReady={onGridReady}

                            defaultColDef={{
                                headerComponentParams: {
                                    innerHeaderComponent: CustomInnerHeader,
                                },
                                resizable: true, width: 140,
                                sortable: true
                            }}
                        />
                    </div>
                </div>
                {children}
            </div>

        </motion.div>
    )
        ;
}
