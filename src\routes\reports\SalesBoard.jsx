import { createFileRoute } from '@tanstack/react-router'
import <PERSON><PERSON> from "lottie-react";
import animationData from "../../../public/images/lottie/safe-shield.json";
import React from "react";
import {But<PERSON>} from "antd";

export const Route = createFileRoute('/reports/SalesBoard')({
  component: RouteComponent,
})

function RouteComponent() {
  return <div className={'flex justify-center items-center flex-col w-full h-full'}>
    <Lottie className="w-[270px]" animationData={animationData} loop/>

    <h5 className={'!mb-2'}>You do not have access to this report.</h5>
    <p>Please contact your administrator for more information, or check back later.</p>
   <div className={'flex-c gap-3'}>
     <Button className={'mt-8'} type={'primary'}>Contact Admin</Button>
     <Button color="default" variant="filled" className={'mt-8'}>Send Request</Button>
   </div>
  </div>
}
