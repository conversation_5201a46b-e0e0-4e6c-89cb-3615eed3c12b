import { createFileRoute } from '@tanstack/react-router'
import {EnumToArray, OrderStatusEnum, SourceEnum} from "../../utils/util.jsx";
import {Tag} from "antd";
import {AGGridBase} from "../../component/AGGridBase.jsx";

export const Route = createFileRoute('/reports/DuePayment')({
  component: RouteComponent,
})

function RouteComponent() {

  const summary = [
    { field: "Taxable", type: "sum", positionColumn:'Taxable',cellTemplate: (summary) => (summary.Taxable) },
    { field: "SettleAmount", type: "sum", cellTemplate: (summary) => (summary.SettleAmount) },
    { field: "ID", type: "count",positionColumn: 'BillNo',cellTemplate: (summary) => (summary.ID+' COUNT') },
  ]

  const filterArray = [
    {key:'Status',operator:'TagBox', value:EnumToArray(OrderStatusEnum),checked:[OrderStatusEnum.Due]},
  ]

  const colDef = [
    {
      field: "BillNo", headerName:'Bill No #', hide: true, cellRenderer: params => {
        return (<div className={'flex-c gap-2 font-semibold'}>
          {params.data.Type === "DineIn" &&
              <img src={'/images/home.svg?q=5'} width={14} alt="Dine In"/>}
          {params.data.Type === "Parcel" &&
              <img src={'/images/Group 2192.svg?q=5'} width={14} alt="Dine In"/>}
          <span className={''}>{'#' + params.data.BillNo}</span>
        </div>);
      }
    },
    {
      field: "Type",  icon: 'box-taped', enableRowGroup: true, rowGroup:true,hide:true, cellRenderer: (params) => {
        if (params.value == 'Parcel') {
          return <Tag bordered={false} className={'small'}>{params.value}</Tag>
        } else if (params.value == 'DineIn') {
          return <Tag color={'purple'} bordered={false} className={'small'}>{params.value}</Tag>
        } else if (params.value == 'Delivery') {
          return <Tag color={'blue'} bordered={false} className={'small'}>{params.value}</Tag>
        }else{
          return <Tag color={'warning'} bordered={false} className={'small'}>{params.value}</Tag>
        }
      }
    },
    {field: "Taxable",sortable: true,icon:'indian-rupee-sign',money:true,headerName: 'Taxable'},
    {field: "CreatedOn",sortable: true,icon:'clock'},
    {field: "BillBy",headerName:'User',icon:'light clipboard-user',sortable: true,cellRenderer: (params) => (
          <div className={'flex-c gap-2'}>
            <img className="w-6" src="/images/default-avatar (1).png"/>
            {params.value}
          </div>
      )},
    {field: "TotalTax",icon:'building-columns',money:true,headerName: 'Tax'},
    {
      field: "SettleAmount",

      money: true,
      aggregate:'Sum',
      icon: '#check-circle',
      headerName: 'Settle',
    },
    {field: "PayBy",icon:'credit-card-blank',headerName: 'Mode'},
    {field: "Source",icon:'code-pull-request-draft',cellRenderer: params => {
        return <span className={'opacity-50 font-semibold'}>{ SourceEnum[params.value]}</span>
      }},
    {
      field: "Status",icon:'circle-exclamation-check', cellRenderer: params => {
        if(params.value==2){

          return  <Tag color={'green'} bordered={false} className="small">Paid</Tag>
        }else{
          return params.value
        }

      }
    },
    // {
    //     field: "groupSummary", headerName: "SettleAmount", valueGetter: params => params.node.group
    //         ? params.data?.aggData?.groupSummary
    //         : params.data?.groupSummary }
  ]



  return <AGGridBase sortArray={[{colId: 'CreatedOn', sort: 'asc'}, {colId: 'BillNo'}]}
                     group={[{label: 'Type'}, {label: 'Status'}, {label: 'Source'}, {label: 'PayBy'}]}
                     filterList={filterArray}
                     title={"O"}
                     summary={summary}
                     column={colDef}
                     loadURL={'/AGOrder/Get'}/>
}
