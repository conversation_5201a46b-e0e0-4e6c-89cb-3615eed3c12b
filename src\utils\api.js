import axios from 'axios';
// Create a custom Axios instance
const api = axios.create({
  baseURL: (location.href.includes('5174')  || location.href.includes('foodyqueen') ) ? 'https://business.foodyqueen.com/' : 'http://localhost:5268/', // Replace with your API base URL
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Add a request interceptor to automatically add JWT from localStorage
api.interceptors.request.use(
  (config) => {
    // Get the token from localStorage
    const token = localStorage.getItem('jwt');
    
    // If token exists, add it to the Authorization header
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// // Add a response interceptor to handle common response scenarios
// api.interceptors.response.use(
//   (response) => {
//     return response;
//   },
//   (error) => {
//     // Handle 401 Unauthorized errors (expired token, etc.)
//     if (error.response && error.response.status === 401) {
//       // Clear invalid token
//       localStorage.removeItem('jwt_token');
//       // Redirect to login page
//       window.location.href = '/auth/login';
//     }
//
//     return Promise.reject(error);
//   }
// );

export default api;