import React from 'react';

/**
 * RatingCard Component - A pixel-perfect rating display component
 *
 * @param {Object} props - Component props
 * @param {string} props.platform - Platform name (e.g., 'G2', 'Google')
 * @param {number} props.rating - Rating value (e.g., 4.6, 4.8)
 * @param {number} props.reviewCount - Number of reviews (e.g., 127, 932)
 * @param {string} props.description - Description text (e.g., 'Rating by G2 users', 'on Google Review')
 * @param {React.ReactNode} props.logo - Logo component or element
 * @param {string} props.className - Additional CSS classes
 */
const RatingCard = ({
  platform,
  rating,
  reviewCount,
  description,
  logo,
  className = ''
}) => {
  return (
    <div className={`bg-white border border-gray-300 rounded-2xl shadow-sm p-1 ${className}`}>
      <div className="flex rounded-lg border border-gray-300 overflow-hidden">
        {/* Left section - White background for logo */}
        <div className="bg-white px-4 py-4 flex items-center">
          <div className="flex-shrink-0">
            {logo}
          </div>
        </div>

        {/* Divider */}
        <div className="w-px bg-gray-300"></div>

        {/* Right section - Gray background for rating */}
        <div className="bg-gray-100 px-4 py-4 flex-1">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-1">
              <span className="text-yellow-400 text-lg">★</span>
              <span className="font-bold text-gray-900 text-lg">{rating}</span>
            </div>
            <span className="text-gray-500 text-sm">{reviewCount}</span>
          </div>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      </div>
    </div>
  );
};

/**
 * G2Logo Component - G2 platform logo
 */
export const G2Logo = () => (
  <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
    <span className="text-white font-bold text-lg">G2</span>
  </div>
);

/**
 * GoogleLogo Component - Google platform logo
 */
export const GoogleLogo = () => (
  <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center border border-gray-200">
    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 via-green-500 via-yellow-500 to-red-500 rounded-full flex items-center justify-center">
      <span className="text-white font-bold text-sm">G</span>
    </div>
  </div>
);

/**
 * RatingCardGroup Component - Container for multiple rating cards
 */
export const RatingCardGroup = ({ children, className = '' }) => (
  <div className={`flex justify-center items-center space-x-8 px-8 ${className}`}>
    {children}
  </div>
);

export default RatingCard;
