import React from 'react';

/**
 * RatingCard Component - A pixel-perfect rating display component
 *
 * @param {Object} props - Component props
 * @param {string} props.platform - Platform name (e.g., 'G2', 'Google')
 * @param {number} props.rating - Rating value (e.g., 4.6, 4.8)
 * @param {number} props.reviewCount - Number of reviews (e.g., 127, 932)
 * @param {string} props.description - Description text (e.g., 'Rating by G2 users', 'on Google Review')
 * @param {React.ReactNode} props.logo - Logo component or element
 * @param {string} props.className - Additional CSS classes
 */
const RatingCard = ({
  platform,
  rating,
  reviewCount,
  description,
  logo,
  className = ''
}) => {
  return (
    <div className={`bg-white  rounded-2xl shadow-sm p-1.5 ${className}`}>
      <div className="flex rounded-xl border bg-neutral-50 border-gray-400/30 overflow-hidden p-3">
        {/* Left section - White background for logo */}
        <div className="bg-white border-gray-400/30 border px-5 py-2 flex items-center rounded-3xl">
          <div className="flex-shrink-0">
            {logo}
          </div>
        </div>

        {/* Divider */}

        {/* Right section - Light gray background for rating */}
        <div className=" px-5 py-5 flex-1 rounded-r-xl">
          <div className="flex items-center justify-between mb-1.5">
            <div className="flex items-center space-x-1.5">
              <span className="text-yellow-400 text-xl">★</span>
              <span className="font-bold text-gray-900 text-xl">{rating}</span>
            </div>
            <span className="text-gray-600 text-sm font-medium">{reviewCount}</span>
          </div>
          <p className="text-sm text-gray-600 font-medium">{description}</p>
        </div>
      </div>
    </div>
  );
};

/**
 * G2Logo Component - G2 platform logo
 */
export const G2Logo = () => (
  <div className="w-14 h-14 bg-gradient-to-br from-orange-400 to-red-500 rounded-xl flex items-center justify-center shadow-sm">
    <span className="text-white font-bold text-xl">G2</span>
  </div>
);

/**
 * GoogleLogo Component - Google platform logo
 */
export const GoogleLogo = () => (
  <div className="w-14 h-14 bg-white rounded-xl flex items-center justify-center border border-gray-300 shadow-sm">
    <div className="w-9 h-9 bg-gradient-to-r from-blue-500 via-green-500 via-yellow-500 to-red-500 rounded-full flex items-center justify-center">
      <span className="text-white font-bold text-base">G</span>
    </div>
  </div>
);

/**
 * RatingCardGroup Component - Container for multiple rating cards
 */
export const RatingCardGroup = ({ children, className = '' }) => (
  <div className={`flex justify-center items-center space-x-8 px-8 ${className}`}>
    {children}
  </div>
);

export default RatingCard;
