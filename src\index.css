@import "tailwindcss";


:root {
    --primary: #1868db;
    --text-color: #44546F;
    --text-color-shadow: rgba(68, 84, 111, 0.35);
    --grid-border: rgba(229, 231, 235, 0.9) !important;
    --text-size:13.3px;
    --text-bold-color: #292a2e;
    --text-button-hover:theme('colors.gray.100');
    --ag-row-border: 1px solid theme('colors.slate.300') !important;
    --input-border: rgba(128, 128, 128, 0.52);
    --ag-row-hover-color:theme('colors.gray.50');
--ag-header-background-color:transparent;
--ag-header-row-border:1px solid var(--grid-border) !important;
--ag-row-border:1px solid var(--grid-border) !important;
--ag-icon-size:13px !important;
--ag-data-font-size:var(--text-size);
--ag-pinned-row-border:1px solid var(--grid-border) !important;
}
.ag-pinned-right-header{
    border-left: 1px solid var(--border-color) !important;
}
.ag-cell-first-right-pinned.ag-column-last {
    border-left: 1px solid theme('colors.gray.50') !important;
}
.ag-group-value{
    width:100%
}


/* Change focus border to white for AG Grid header cells */
.flex-c {
    display: flex !important;
    align-items: center !important;
    flex-direction: row !important;
}


.ant-tag:not(.small) {
    padding-inline: 2px;
    line-height: 19px;
}


.ant-picker-small{
    padding:7px
}

.ag-header{
    @apply !text-sm opacity-90;
}

/*.ag-pinned-left-cols-container{*/
/*    width: 54px !important;*/
/*    max-width: 54px !important;*/
/*    min-width: 54px !important;*/
/*}*/
.ag-horizontal-left-spacer:not(.ag-scroller-corner) {
    display: none;
}
.ag-row-pinned .ag-row-group-leaf-indent{
    height:25px;
}
.ag-row-position-absolute:not(.ag-row-group){
    border-right: 1px solid var(--grid-border) !important;
}

.ag-root-wrapper{
    border:1px solid var(--grid-border);
}
.text-base{
    font-size:var(--text-size)
}
.font-semibold,.ant-btn,h4{
    text-shadow:0.0px 0.0px 0 var(--text-color-shadow)
}
.rdp-day_button{
    margin:auto !important;
}

.rdp-range_end .rdp-day_button {
    background-color: var(--color-violet) !important;
}

.rdp-range_start .rdp-day_button {
    background-color: var(--color-violet);
    border:unset !important;
}

.rdp-root{
    --rdp-day_button-height: 33px;
    --rdp-day_button-width: 33px;
    --rdp-day-height: 40px;
    --rdp-day-width:42px;
    --rdp-day_button-border-radius: 6px;
}

.rdp-root[data-nav-layout="around"] .rdp-button_previous {
    left: 7px;
    transform: scale(0.7);
}

.rdp-root[data-nav-layout="around"] .rdp-button_next {
    right: 5px;
    transform: scale(0.7);
}
.ant-dropdown-menu-item-selected .ms-auto:not(.do-not-hide) {
    display: none;
}

.rdp-week.rdp-day{
}
.rdp-range_middle .rdp-day_button{
    margin:auto;
}

.ant-picker-calendar-mode-switch{
 display: none;
}
:where(.ag-ltr) .ag-cell-wrapper {
    padding-left: 4px !important;
}
.ant-picker-footer{
 display:none
}
.ant-dropdown,.ant-dropdown-menu,.ant-picker-dropdown {
    margin-top: 5px !important;
}

.ant-dropdown-open {
    background-color: var(--color-primary-light) !important;
    color: var(--color-primary) !important;
}


.ant-picker-body {
    padding-top: 16px;
}

.ag-cell-value, .ag-group-value{
    margin-left:-4px !important;
}

.no-hover {
    @apply !bg-white !font-semibold !text-sm !mb-1;
}


.ag-row-group-expanded:before{
    background : white !important;
}

.ag-row-pinned.ag-row-hover:before{
    background:white !important;
}


.ag-theme-alpine .ag-cell:focus,
.ag-theme-alpine .ag-header-cell:focus,
.ag-theme-alpine .ag-cell-focus,
.ag-theme-alpine .ag-header-cell-focus,
.ag-theme-alpine .ag-row:focus,
.ag-theme-quartz .ag-cell:focus,
.ag-theme-quartz .ag-header-cell:focus,
.ag-theme-quartz .ag-cell-focus,
.ag-theme-quartz .ag-header-cell-focus,
.ag-theme-quartz .ag-row:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Remove focus ring from the entire grid */
.ag-theme-alpine:focus,
.ag-theme-quartz:focus {
    outline: none !important;
}

/* Remove cell border highlight on focus */
.ag-theme-alpine .ag-cell-focus:not(.ag-cell-range-selected),
.ag-theme-quartz .ag-cell-focus:not(.ag-cell-range-selected) {
    border: 1px solid transparent !important;
    border-right: 1px solid theme('colors.gray.300') !important;
}

/* Ensure no focus styles are applied during keyboard navigation */
.ag-keyboard-focus .ag-cell:focus,
.ag-keyboard-focus .ag-header-cell:focus {
    outline: none !important;
}


.ag-row-selected:before {
    background-color: transparent;

}
.center{
    @apply flex items-center justify-center
}
.ant-dropdown-menu-item-icon {
    min-width: 35px;
    width: 35px;
    margin-inline-end: 0px !important;
    margin-inline-start: 5px !important;
    font-size: 17px !important;
}
.ant-btn-icon.ant-btn-loading-icon{
    position: absolute;
}
.ant-btn.ant-btn-loading {
    opacity: 1 !important;
}
.ant-btn-loading > span:last-of-type {
    visibility: hidden !important;
}



.ant-form-item-explain-error {
    color: theme('colors.red.700') !important;
    font-size: 13px !important;
    margin-top:5px;
}
a[href*="amcharts.com"] {
    display: none !important;
}
p{
    @apply text-sm opacity-90;
    line-height: 1.2 !important;
    font-weight:400;
}
.ant-tabs.no-margin .ant-tabs-nav{
    margin-bottom:0px!important;
}
.card-header{
    @apply flex items-center border-b border-gray-200 p-3.5 !px-5 font-bold
}
.ant-input-outlined.ant-input-status-error:not(.ant-input-disabled) {
    border-width: 2px;
    border-color: theme('colors.red.700');
}

.ant-input-outlined.ant-input-status-error:not(.ant-input-disabled):hover {
    border-color: theme('colors.red.700');
}
.ant-input-outlined.ant-input-status-error:not(.ant-input-disabled):focus-within{
    box-shadow: unset !important;
}

h4{
    font-size: 21px;
    font-weight: 600;
    margin-bottom: 0px;
    color:var(--text-bold-color) !important;
}

.card{
    @apply rounded-2xl shadow-xs border border-gray-200/80
}
.shadow-xs{
        --tw-shadow: 0 1px 2px 0 var(#0000000d);
        box-shadow: 0 0 #0000, 0 0 #0000, 0 0 #0000, 0 0 #0000, 0 1px 2px 0 #0000000d;
}


h2{
    font-size: 30px;
    font-weight: 600;
    margin-bottom: 0px;
}

h3{
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 0px;
}

h5{
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 0px;
    color:var(--text-bold-color) !important;
}


.ant-dropdown-menu-item.ant-dropdown-menu-item-selected{
    font-weight: 600 !important;
    background: var(--color-primary-light) !important;
    box-shadow: inset 2px 0 0 0 var(--primary);
}
.ant-dropdown-menu-item.ant-dropdown-menu-item-selected:after {
    content: "\f00c";  /* Font Awesome check icon unicode */
    font-family: 'Font Awesome 6 Pro';
    font-weight: 900;
    font-size: var(--text-size);
    color: #1868db;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
}

.ant-tag.ant-tag-green {
    color: #216E4E;
    background: #DCFFF1;
}
.g2-tooltip{
    background: white !important;
}
.text-green{
    color: #216E4E;
}

.ant-dropdown .ant-dropdown-menu, :where(.css-dev-only-do-not-override-taezlw).ant-dropdown-menu-submenu .ant-dropdown-menu {
    padding: 8px 0px;
    outline:1px solid theme('colors.gray-200');
    outline-offset: -1px;
}



@keyframes antFadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes antFadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
.simplebar-content{
    height:100%
}
/* Apply fade animations */
.ant-fade-enter,
.ant-fade-appear {
    opacity: 0;
    animation-timing-function: linear;
    animation-duration: 0.2s;
}

.ant-fade-enter-active,
.ant-fade-appear-active {
    opacity: 1;
    animation-name: antFadeIn;
}

.ant-fade-exit {
    opacity: 1;
    animation-timing-function: linear;
    animation-duration: 0.2s;
}

.ant-fade-exit-active {
    opacity: 0;
    animation-name: antFadeOut;
}

/* Override any transform animations for dropdowns */
.ant-dropdown {
    transform: none !important;
}

@theme {
    --color-primary: #1868db;
    --color-violet: #5E4DB2;
    --color-primary-light: #dcebff96;
    --color-primary-hover: #0751ba;
    --text-sm: 12.5px;
    --text-xs: 11.5px;
    --text-xxs: 10.5px;
}

.ant-tag.ant-tag-magenta {
    color: white;
    background: var(--color-violet);
}

.ant-btn-color-default.ant-btn-variant-filled{
    background: theme('colors.gray.100');
}

.ant-btn-color-default.ant-btn-variant-filled:not([disabled]):hover {
    background: theme('colors.gray.200') !important;
}


.selected-category{
    background: var(--color-primary-light);
    /*outline:2px solid var(--primary);*/
    /*outline-offset: -2px;*/
    color: var(--primary);
}

.ant-btn-color-primary.ant-btn-variant-filled {
    background: var(--color-primary-light);
}


/* Remove the default underline separator */
.ant-tabs-nav::before {
    border-bottom: none !important;
}

h6{
    @apply flex items-center gap-1.5
}
.ant-collapse-header-text{
    @apply font-semibold
}
.rdp-month_caption {
    padding: 0px 13px;
    font-size: var(--text-size);
    font-weight: 700;
}
.rdp-selected{
    font-weight: 600 !important;
    font-size: var(--text-size) !important;
}
.rdp-range_end .rdp-day_button {
    background-color: var(--color-violet) !important;
    border:unset !important;
}
.text-red{
    color: theme('colors.red.700')
}

.rdp-today:not(.rdp-outside) {
    color: var(--color-violet);
}

.rdp-nav{
    transform: scale(0.7);
}

.rdp-chevron {
    fill: gray !important;
}

.link{
    font-weight: 700;
    color: var(--primary);
    cursor: pointer;
}
.ant-btn-variant-link {
    font-weight: 600;
}

.bg-primary-btn{
    background: var(--color-primary) !important;
    color:white !important;
    transition: all 0.3s ease-in-out;
}

.bg-default-btn{
    background: theme('colors.gray.200') !important;
    transition: all 0.3s ease-in-out;
}

.bg-default-btn:hover{
    background: theme('colors.gray.300') !important;
}

.bg-primary-btn:hover{
    background: var(--color-primary-hover) !important;
}

/*.ant-btn-default:hover {*/
/*  background-color: theme('colors.slate.50') !important;*/
/*  border-color: #d9d9d9 !important;*/
/*}*/

html {
    font-size: var(--text-size);
}
.ag-row{
    color: var(--text-color);
}
/* Allow text selection and default cursor for input elements */



/* Custom styles can be added here */
body {

    /* Disable text selection for all elements on the document except input fields */
        cursor: default; /* Change to normal cursor */
        user-select: none; /* Disable text selection */


    font-family: "OpenSans" !important;
    font-weight: 500 !important;
    margin: 0;
    /* overflow: hidden; */
    height:100vh;
    color: var(--text-color) !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}



.ag-theme-quartz .ag-cell:focus,
.ag-theme-quartz .ag-header-cell:focus,
.ag-theme-quartz .ag-row:focus {
    outline: none !important;
    border: none !important;
}

.ag-header-cell-resize {
    &:after {
        background-color: transparent;
        content: "";
        height: 100%;
        position: absolute;
        top: 0;
        width: 1px;
        z-index: 1;
    }
}

.ant-tag-borderless {
    background: theme('colors.slate.100');
    text-transform: uppercase;
    font-weight: 700;
    font-size: 11.5px;
}
.ag-header-cell.ag-column-first{
    left: 5px !important;
}

.ant-tag {
    padding-inline: 3px;
    line-height: 15px;
}


/* Optional: Adjust header text color for better contrast */
.ag-theme-alpine .ag-header-cell {
    color: var(--text-color) !important; /* Use a darker text color for better visibility */
}

.ag-column-drop-horizontal {
    background: white;
}

:where(.ag-ltr) .ag-row-group-leaf-indent {
    margin-left: 9px;
    border-left: 1px solid rgba(204, 204, 204, 0.63);
    padding-left: 20px !important;
}

.ag-floating-bottom-container .ag-row-group-leaf-indent {
    margin-left: 15px !important;

    border-left: 0px solid rgba(204, 204, 204, 0.63) !important;
    padding-left: 0px !important;
}

.ag-row-pinned {
    font-size: 12px !important;
    opacity:0.8;
}



:where(.ag-ltr) .ag-cell-wrapper {
    padding-left: 0px;
}

/*.ag-theme-alpine .ag-cell {*/
/*  border-right: 1px solid theme('colors.slate.300');*/
/*}*/

/*.ag-theme-alpine .ag-row-group .ag-cell {*/
/*  border-right: none !important;*/
/*  border-bottom: none !important;*/
/*}*/

/*.ag-theme-alpine .ag-row-pinned .ag-cell {*/
/*  border-right: none !important;*/
/*  border-bottom: none !important;*/
/*}*/
/* Column borders for cells */
.ag-theme-alpine .ag-cell {
    border-right: 1px solid var(--grid-border); /* Column line color */
}

.ag-row-group .ag-cell {
    border-right: 1px solid transparent !important; /* Column line color */
}

.ag-row-pinned .ag-cell{
    border-right: none !important;
    padding:0px 11px;
}
.ant-tooltip-arrow {
    display: none !important;
}

/*.ag-theme-alpine .ag-cell {*/
/*    padding-left: 12px !important; !* or 0px *!*/
/*}*/

.ag-floating-bottom-container .ag-row-group-leaf-indent{
    height:25px;
    margin-left: 8px !important;
}
.ag-floating-bottom{
    overflow: unset !important;
}
.auto-hide-scrollbar .ScrollbarsCustom-TrackY {
    opacity: 0;
    transition: opacity 0.3s;
}

.auto-hide-scrollbar:hover .ScrollbarsCustom-TrackY {
    opacity: 1;
}

.ScrollbarsCustom-Track{
    background: transparent !important;
    padding: 3px;
}
.ant-tooltip-inner {
    border-radius: 3px !important;
    background:#44546f !important;
    font-weight: 600 !important;
    min-height: 19px !important;
    padding: 1px 4px !important;  /* Adjust padding */
    font-size: 11px !important;     /* Adjust font size */
}

/* Remove the border line after the last column (optional for cleaner look) */
.ag-theme-alpine .ag-cell:last-child {
    border-right: none !important;
}

/* Column borders for header cells */
.ag-theme-alpine .ag-header-cell {
    border-right: 1px solid var(--grid-border); /* Column line color */
}

/* Remove the border line after the last header column (optional) */
.ag-theme-alpine .ag-header-cell:last-child {
    border-right: none !important;
}

@font-face {
    font-family: "OpenSans";
    src: url("/font/OpenSans-Light.woff2") format("truetype");
    font-weight: 300;
}

@font-face {
    font-family: "OpenSans";
    src: url("/font/OpenSans-Regular.woff2") format("truetype");
    font-weight: 400;
}

@font-face {
    font-family: "OpenSans";
    src: url("/font/OpenSans-Medium.ttf") format("truetype");
    font-weight: 500;
}

@font-face {
    font-family: "OpenSans";
    src: url("/font/OpenSans-SemiBold.woff2") format("truetype");
    font-weight: 600;
}

@font-face {
    font-family: "OpenSans";
    src: url("/font/OpenSans-Bold.woff2") format("truetype");
    font-weight: 700;
}

@font-face {
    font-family: "OpenSans";
    src: url("/font/OpenSans-ExtraBold.woff2") format("truetype");
    font-weight: 800;
}


.ag-center-cols-container {
    min-width: 100% !important;
}

.ant-dropdown-menu-item{
box-shadow: inset 2px 0 0 0 white;
border-radius: 0px !important;
}

.selectable .ant-dropdown-menu-item:hover:not(.no-hover) {
    box-shadow: inset 2px 0 0 0 var(--primary);
}

.ant-input-number-input {
    height: 38px !important;
}

/* Placeholder styling */
::placeholder {
    color: rgba(45, 46, 49, 0.33) !important;
    font-weight: 600 !important;
    opacity: 1 !important; /* Firefox */
}

:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: rgba(45, 46, 49, 0.45) !important;
    font-weight: 500 !important;
    opacity: 1 !important; /* Firefox */
}

::-ms-input-placeholder { /* Microsoft Edge */
    color: rgba(45, 46, 49, 0.45) !important;
    font-weight: 500 !important;
    opacity: 1 !important; /* Firefox */
}


.ant-input-outlined:hover,.ant-picker-outlined:hover{
    background-color: theme('colors.slate.50');
}


.ant-input-outlined,.ant-picker-outlined {
    border-width: 1px;
}

.ant-input-outlined {
    outline: 1px solid transparent;
    outline-offset: -2px;
}

.ant-input-outlined:focus-within {
    background-color: white;
    outline: 1px solid var(--primary);
    outline-offset: -2px;
}

.ag-cell{
    display: flex;
    align-items: center;
}

.ag-theme-alpine .ag-cell,.ag-header-cell {
    padding-left: 12px !important; /* or 0px */
}

.ag-floating-bottom{
    height: 28px !important;
    min-height: 28px !important;
}

.ag-floating-bottom .ag-cell, .ag-full-width-row .ag-cell-wrapper.ag-row-group {
    line-height: 25px !important;
}

/*!* Custom focus styles for Ant Design inputs *!*/
/*.ant-input:focus,*/
/*.ant-input-focused,*/
/*.ant-input-affix-wrapper:focus,*/
/*.ant-input-affix-wrapper-focused {*/
/*  outline: 2px solid var(--primary) !important;*/
/*  outline-offset: -1px !important;*/
/*  background: white;*/
/*}*/

.ag-row-group .ag-cell-focus:not(.ag-cell-range-selected), .ag-theme-quartz .ag-cell-focus:not(.ag-cell-range-selected){
    border-right: 1px solid transparent !important;
}

.ant-dropdown-menu-item:not(.no-hover) {
    font-weight: 600 !important;
}