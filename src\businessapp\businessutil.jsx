import React from "react";
import Chart from "react-apexcharts";

export const ApexChart = () => {
    const [state, setState] = React.useState({

        series: [{
            data: [
                [1327359600000,30.95],
                [1327446000000,31.34],
                [1327532400000,31.18],
                [1327618800000,31.05],
                [1327878000000,31.00],
                [1327964400000,30.95],
                [1328050800000,31.24],
                [1328137200000,31.29],
                [1328223600000,31.85],
                [1328482800000,31.86],
                [1328569200000,32.28],
                [1328655600000,32.10],
                [1328742000000,32.65],
                [1328828400000,32.21],
                [1329087600000,32.35],
                [1329174000000,32.44],
                [1329260400000,32.46],
                [1329346800000,32.86],
                [1329433200000,32.75],
                [1329778800000,32.54],
                [1329865200000,32.33],
                [1329951600000,32.97],
                [1330038000000,33.41],
                [1330297200000,33.27],
                [1330383600000,33.27],
                [1330470000000,32.89],
                [1330556400000,33.10],
                [1330642800000,33.73],
                [1330902000000,33.22],
                [1330988400000,31.99],
                [1331074800000,32.41],
                [1331161200000,33.05],
                [1331247600000,33.64],
                [1331506800000,33.56],
                [1331593200000,34.22],
                [1331679600000,33.77],
                [1331766000000,34.17],
                [1331852400000,33.82],
                [1332111600000,34.51],
                [1332198000000,33.16],
                [1332284400000,33.56],
                [1332370800000,33.71],
                [1332457200000,33.81],
                [1332712800000,34.40],
                [1332799200000,34.63],
                [1332885600000,34.46],
                [1332972000000,34.48],
                [1333058400000,34.31],
                [1333317600000,34.70],
                [1333404000000,34.31],
                [1333490400000,33.46],
                [1333576800000,33.59],
                [1333922400000,33.22],
                [1334008800000,32.61],
                [1334095200000,33.01],
                [1334181600000,33.55],
                [1334268000000,33.18],
                [1334527200000,32.84],
                [1334613600000,33.84],
                [1334700000000,33.39],
                [1334786400000,32.91],
                [1334872800000,33.06],
                [1335132000000,32.62],
                [1335218400000,32.40],
                [1335304800000,33.13],
                [1335391200000,33.26],
                [1335477600000,33.58],
                [1335736800000,33.55],
                [1335823200000,33.77],
                [1335909600000,33.76],
                [1335996000000,33.32],
                [1336082400000,32.61],
                [1336341600000,32.52],
                [1336428000000,32.67],
                [1336514400000,32.52],
                [1336600800000,31.92],
                [1336687200000,32.20],
                [1336946400000,32.23],
                [1337032800000,32.33],
                [1337119200000,32.36],
                [1337205600000,32.01],
                [1337292000000,31.31],
                [1337551200000,32.01],
                [1337637600000,32.01],
                [1337724000000,32.18],
                [1337810400000,31.54],
                [1337896800000,31.60],
                [1338242400000,32.05],
                [1338328800000,31.29],
                [1338415200000,31.05],
                [1338501600000,29.82],
                [1338760800000,30.31],
                [1338847200000,30.70],
                [1338933600000,31.69],
                [1339020000000,31.32],
                [1339106400000,31.65],
                [1339365600000,31.13],
                [1339452000000,31.77],
                [1339538400000,31.79],
                [1339624800000,31.67],
                [1339711200000,32.39],
                [1339970400000,32.63],
                [1340056800000,32.89],
                [1340143200000,31.99],
                [1340229600000,31.23],
                [1340316000000,31.57],
                [1340575200000,30.84],
                [1340661600000,31.07],
                [1340748000000,31.41],
                [1340834400000,31.17],
                [1340920800000,32.37],
                [1341180000000,32.19],
                [1341266400000,32.51],
                [1341439200000,32.53],
                [1341525600000,31.37],
                [1341784800000,30.43],
                [1341871200000,30.44],
                [1341957600000,30.20],
                [1342044000000,30.14],
                [1342130400000,30.65],
                [1342389600000,30.40],
                [1342476000000,30.65],
                [1342562400000,31.43],
                [1342648800000,31.89],


            ]
        }],
        options: {
            chart: {
                background: 'rgba(255,255,255,0)',
                id: 'area-datetime',
                type: 'area',

                zoom: {
                    enabled: false,
                    autoScaleYaxis: false
                },
                toolbar: {
                    show: false
                }

            },
            annotations: {
                yaxis: [{
                    y: 31,
                    axisBorder: {
                        show: false,
                        color: '#78909C',
                        offsetX: 0,
                        offsetY: 0
                    },
                    borderColor: 'rgba(153,153,153,0)',
                    // label: {
                    //     show: false,
                    //     text: 'Support',
                    //     style: {
                    //         color: "#fff",
                    //         background: '#00E396'
                    //     }
                    // }
                }],
                xaxis: [{

                    x: new Date('14 Nov 2012').getTime(),
                    borderColor: 'transparent',
                    fillColor: 'transparent',
                    yAxisIndex: 0,
                    label: {
                        show: true,
                        text: 'Rally',
                        style: {
                            color: "#fff",
                            background: '#775DD0'
                        }
                    }
                }]
            },

            dataLabels: {
                enabled: false
            },
            markers: {
                size: 0,
                style: 'hollow',
            },
            "grid": {
                "show": false,
                "borderColor": "#6e7eaa",
                "padding": {
                    "right": 25,
                    "left": 15
                }
            },
            stroke: {
                show: true,
                curve: 'straight',
                lineCap: 'round',
                colors: undefined,
                width: 2,
                dashArray: 0,
            },

            xaxis: {
                type: 'datetime',
                "axisBorder": {
                    "show": false
                },
                "axisTicks": {
                    "show": false
                },
                min: new Date('01 Mar 2012').getTime(),
                tickAmount: 1000,
                "labels": {
                    "show": false,
                    "trim": true,
                    "style": {}
                },
            },
            "yaxis": {
                "show": false,
                "tickAmount": 5,
                "labels": {
                    "showDuplicates": false,
                    "style": {}
                },
                "title": {
                    "style": {
                        "fontWeight": 700
                    }
                }
            },
            tooltip: {
                x: {
                    format: 'dd MMM yyyy'
                }
            },

            "theme": {
                "mode": "dark",
                "palette": "palette4"
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'dark',
                    shadeIntensity: 1,
                    opacityFrom: 0.9,
                    opacityTo: 0,
                    stops: [0, 100]
                },
                colors: ['#2B9B94']
            },
        },


        selection: 'one_year',

    });




    return (

            <Chart options={state.options} series={state.series} type="area" height={150} />

    );
}



export const SemiDoughnutChart = ({ series, labels }) => {
    const options = {
        chart: {
            type: 'donut',
        },
        labels: labels,
        plotOptions: {
            pie: {
                startAngle: -90,
                endAngle: 90,
                donut: {
                    size: '70%',
                },
            },
        },
        dataLabels: {
            enabled: true,
        },
        legend: {
            show: true,
            position: 'bottom',
        },
    };

    return (
        <Chart
            options={options}
            series={series}
            type="donut"
            width={120}
            height={200}
        />
    );
};


export const ApexChart2 = () => {
    const [state, setState] = React.useState({

        series: [100],
        options: {
            chart: {
                height:350,
                type: 'radialBar',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                radialBar: {
                    startAngle: -135,
                    endAngle: 225,
                    hollow: {
                        margin: 0,
                        size: '70%',
                        background: '#fff',
                        image: undefined,
                        imageOffsetX: 0,
                        imageOffsetY: 0,
                        position: 'front',
                        dropShadow: {
                            enabled: true,
                            top: 3,
                            left: 0,
                            blur: 4,
                            opacity: 0.5
                        }
                    },
                    track: {
                        background: '#fff',
                        strokeWidth: '67%',
                        margin: 0, // margin is in pixels
                        dropShadow: {
                            enabled: true,
                            top: -3,
                            left: 0,
                            blur: 4,
                            opacity: 0.1
                        }
                    },

                    dataLabels: {
                        show: true,
                        name: {
                            offsetY: 0,
                            show: false,
                            color: '#888',
                            fontSize: '17px'
                        },
                        value: {
                            offsetY: 5,
                            formatter: function(val) {
                                return parseInt(val)+'%';
                            },
                            color: '#111',
                            fontSize: '16px',
                            show: true,
                        }
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'dark',
                    type: 'horizontal',
                    shadeIntensity: 0.5,
                    gradientToColors: ['#ABE5A1'],
                    inverseColors: true,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100]
                }
            },
            stroke: {
                lineCap: 'round'
            },
            labels: ['Percent'],
        },


    });



    return (
        <div>
            <div id="card">
                <div className="w-30">
                    <Chart options={state.options} series={state.series} type="radialBar"  height={120} />
                </div>
            </div>
            <div id="html-dist"></div>
        </div>
    );
}