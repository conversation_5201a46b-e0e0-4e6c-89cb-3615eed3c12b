import { createFileRoute } from "@tanstack/react-router";
import { motion } from "framer-motion";
import PromoBanner from "./-homepage/PromoBanner";
import Navbar from "./-homepage/Navbar";
import HeroSection from "./-homepage/HeroSection";
import TrustedSection from "./-homepage/TrustedSection";
import VideoSection from "./-homepage/VideoSection";
import FeaturesSection from "./-homepage/FeaturesSection";
import "./-homepage/homepage.css";

export const Route = createFileRoute("/")({
  component: Home,
});

function Home() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className=""
    >
      <PromoBanner />
      <Navbar />
      <HeroSection />
      <TrustedSection />
      <VideoSection />
      <FeaturesSection />
    </motion.div>
  );
}
