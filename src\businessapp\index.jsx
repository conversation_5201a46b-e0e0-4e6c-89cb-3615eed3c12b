import React, {createContext, useContext, useEffect, useRef, useState} from 'react';
import ReactDOM from 'react-dom/client';
import Framework7 from 'framework7/lite-bundle';
import Framework7React, {
    App, Block,
    BlockTitle, Button,
    f7,
    f7ready, List, ListItem,
    Page,
    Preloader,
    Progressbar,
    Sheet,
    View
} from 'framework7-react';
import {motion, AnimatePresence, useAnimation, useMotionValue} from "framer-motion";
import _ from 'lodash';
import '/node_modules/framework7/framework7-bundle.css';
import Lottie from "lottie-react";
import dataservers from "../../public/images/lottie/data-servers (1).json";
import shield from "../../public/images/lottie/Shield.json";
import api from "../utils/api.js";
import {Media, Money, setupAccount} from "../utils/util.jsx";
import {ApexChart, ApexChart2, SemiDoughnutChart} from "./businessutil.jsx";
import SimpleBar from "simplebar-react";
import 'simplebar-react/dist/simplebar.min.css';
import {filter, map, sumBy} from "lodash";
import moment from "moment";
import Chart from "react-apexcharts";

Framework7.use(Framework7React);

const SharedContext = createContext();
const useShared = () => useContext(SharedContext);


const vibrate = (pattern = 100) => {
    if ('vibrate' in navigator && typeof navigator.vibrate === 'function') {
        navigator.vibrate(pattern);
    }
};

const error = (msg) => {
    f7.toast.create({
        text: msg,
        icon: "<img class='mr-1 bg-black-500' src='/images/high_importance.svg' width=23 />",
        position: 'bottom', // 'top', 'center', or 'bottom'
        closeTimeout: 2000, // auto close after 2 seconds
    }).open();
}
const info = (msg) => {
    f7.toast.create({
        text: msg,
        icon: "<img class='mr-1 bg-black-500' src='/images/6ok.svg' width=23 />",
        position: 'bottom', // 'top', 'center', or 'bottom'
        closeTimeout: 2000, // auto close after 2 seconds
    }).open();
}

const LoginScreen = () => {
    const [phone, setPhone] = useState('');
    const [installing, setInstalling] = useState(false)
    return (<Page className="p-5">
        <div className={'flex flex-col h-full'}>
            <div className={'flex-1 relative flex flex-col justify-end items-center'}>
                <div
                    style={{
                        background: "linear-gradient(182deg, rgba(0, 0, 0, 0.1) 0%, rgb(0, 0, 0) 100%)",
                        width: "100%",
                        height: "200px",
                        position: "absolute",
                        bottom: '00px',
                        zIndex: 9
                    }}
                ></div>
                {/*<Lottie className={'-mb-15'} style={{width: 400, height: 350}} animationData={dataservers} loop={true}/>*/}
                <img className={'w-[200px]'} src={'/images/www.png'}/>

            </div>
            <div>
                <div className={'text-center'}>
                    <h1 className={'leading-tight mb-10'}>Track all your <br/>
                        restaurant <br/>
                        sales in one place</h1>
                </div>
                <div
                    className="border mt-3 mb-2 border-white/40 rounded-xl p-3 py-2.5 transition-colors duration-300 focus-within:border-white/70">
                    <div className="opacity-70 mb-1.5">Enter Phone Number</div>
                    <div className="text-sm flex-c gap-2 font-semibold">
                        +91 <input value={phone} onChange={(e) => {
                        setPhone(e.target.value);
                        window.PhoneNumber = e.target.value
                    }} type={'number'} className="outline-none w-full bg-transparent"/>
                    </div>
                </div>
                <button onClick={async () => {
                   try {
                       setInstalling(true)
                       var data = await api.get('admin/Step1_VerifyMobileNumber?mobileApp=true&phone=' + phone)
                       setInstalling(false)
                       f7.views.main.router.navigate('/otp')

                   } catch (e) {
                       setInstalling(false)
                       error('There is no account found: ' + (e?.message || e))
                   }

                }} className="button button-fill mt-6 button-large !py-2 color-white flex-c gap-1.5">
                    {installing && <span style={{transform: 'scale(0.7)'}}><Preloader color={'black'}/></span>}
                    {!installing && <> <span className={'flex-c gap-1.5'}>Get Started <i
                        className={'fa fa-arrow-right'}/></span></>}

                </button>
                <div className={'w-full flex-c justify-center'}>
                    <a className={'text-center border-b mt-5 pb-0.5'}>See how it works</a>
                </div>
            </div>
        </div>
    </Page>)
};

const OTPScreen = () => {
    const [otp, setOtp] = useState('')
    const [varify, setVarify] = useState(false)
    return (<Page className="p-5">
        <div className={'flex flex-col h-full'}>
            <div className={'flex-1 relative flex flex-col justify-end items-center'}>
                <div
                    style={{
                        background: "linear-gradient(182deg, rgba(0, 0, 0, 0.1) 0%, rgb(0, 0, 0) 100%)",
                        width: "100%",
                        height: "200px",
                        position: "absolute",
                        bottom: '00px',
                        zIndex: 9
                    }}
                ></div>


            </div>
            <div>
                <div className={'text-center'}>
                    <h1 className={'leading-tight mb-3'}>Verify your phone</h1>
                    <div>Enter the 4-digit OTP sent to your phone</div>
                </div>

                <div
                    className="border mt-3 mb-2 border-white/40 rounded-xl p-3 py-2.5 transition-colors duration-300 focus-within:border-white/70">
                    <div className="opacity-70 mb-1.5">Enter OTP</div>
                    <div className="text-sm flex-c gap-2 font-semibold">
                        <input value={otp} onChange={(e) => {
                            setOtp(e.target.value)
                        }} type={'number'} className="outline-none w-full bg-transparent"/>
                    </div>
                </div>
                <div className={'flex-c gap-2'}>
                    <button onClick={async () => {
                        var data = await api.get('admin/Step1_VerifyMobileNumber?mobileApp=true&phone=' + window.PhoneNumber)
                        info('OTP sent to your phone')
                    }}
                            className="button button-fill !w-[50%] mt-6 button-large !py-2 color-white !bg-white/10 !text-white flex-c gap-1.5">

                        <span className={'flex-c gap-1.5'}>Resend</span>
                    </button>
                    <button onClick={async () => {
                        try {

                            var data = await api.get('admin/Step2_VerifyOTP?mobileApp=true&phone=' + window.PhoneNumber + '&otp=' + otp)
                            if (data.data.result == "ACCOUNT_EXIST_PULL_EVERYTHING_NEW") {
                                localStorage.setItem('jwt', data.data.JWT)
                                localStorage.setItem('area', data.data.outlet.Area)
                                localStorage.setItem('phone', data.data.phone)

                                location.reload()
                            }else {
                                error('OTP is invalid')
                            }
                        } catch {
                            error('There is no account found')

                        }

                    }} className="button button-fill !w-[50%] mt-6 button-large !py-2 color-white flex-c gap-1.5">

                        {!varify && <> <span className={'flex-c gap-1.5'}>Verify OTP</span></>}
                    </button>
                </div>

                <div className={'w-full flex-c justify-center'}>
                    <a className={'text-center border-b mt-5 pb-0.5'}>See how it works</a>
                </div>
            </div>
        </div>
    </Page>)
}

const SplashScreen = () => {
    const {data, setData} = useShared()
    useEffect(() => {
        if (localStorage.getItem('jwt')) {
            api.get('/admin/GetDataForMobile').then(res => {
                setData(res.data)
                f7.views.main.router.navigate('/home')
            })
        } else {
            f7.views.main.router.navigate('/login')
        }
    }, []);
    return (<Page className="p-5"
                  style={{background: 'linear-gradient(182deg,rgba(0, 0, 0, 1) 0%, rgba(43, 39, 21, 1) 100%)'}}>
        <div className={'flex flex-col h-full'}>
            <div className={'flex flex-col items-center justify-center'}>
                <div className={'text-xxs mb-1.5 opacity-50'}>Powered By</div>
                <img src={'/images/Microsoft-Azure-Logo-2018-300x300.png.webp'} className={'object-contain w-30'}/>
            </div>
            <div className={'flex-1 flex flex-col items-center justify-center'}>
                <div className={'text-center'}>
                    <h1 className={'!text-[35px] leading-tight !font-extrabold'}>Business <br/> Analytics</h1>
                    <div className={'mx-auto mt-8 w-[140px]'}><Progressbar infinite/></div>
                </div>
            </div>
            <div className={'flex flex-col items-center justify-center'}>
                <div className={'text-xxs mb-1.5 opacity-50'}>Secured by</div>
                <div
                    className={'flex-c gap-0 rounded-full px-2.5 py-0.5 border border-white/10 bg-gradient-to-b from-white/15 to-white/2'}>
                    <Lottie className={'!-mr-2 -ml-4'} style={{width: 45}} animationData={shield} loop={true}/> AES 137
                </div>

            </div>

        </div>
    </Page>)
}

const MIN_HEIGHT = 222;
const MAX_HEIGHT = window.innerHeight * 0.9;
const EXPAND_OFFSET = MAX_HEIGHT - MIN_HEIGHT;

export function SwipeableSheet({data}) {
    const controls = useAnimation();
    const y = useMotionValue(EXPAND_OFFSET); // start at 300px visible
    const scrollRef = useRef(null);
    const [canDrag, setCanDrag] = useState(true);
    const [isExpanded, setIsExpanded] = useState(false);
    const [showAll,setShowAll] = useState(false)
    const [showCategory,setShowCategory] = useState(false)


    const items = _(data.settled)
        .flatMap(order => order.Items.filter(item => item.KdsItemStatus !== 'Deleted'))
        .groupBy('Name')
        .map((group, name) => ({
            Name: name,
            TotalAmount: _.sumBy(group, 'TotalAmount'),
            Quntity: _.sumBy(group, 'Quntity'),
            Avtar: _.get(group, '[0].Avtar')
        }))
        .value();

    var periodic = [
        {
            name: 'Morning',
            amount: _.sumBy(
                data.settled.filter(x => {
                    const hour = new Date(x.CreatedOn).getHours();
                    return hour >= 5 && hour < 12;
                }),
                'SettleAmount'
            )
        },
        {
            name: 'Evening',
            amount: _.sumBy(
                data.settled.filter(x => {
                    const hour = new Date(x.CreatedOn).getHours();
                    return hour >= 12 && hour < 18;
                }),
                'SettleAmount'
            )
        },
        {
            name: 'Night',
            amount: _.sumBy(
                data.settled.filter(x => {
                    const hour = new Date(x.CreatedOn).getHours();
                    return hour >= 18 && hour < 24;
                }),
                'SettleAmount'
            )
        },
        {
            name: 'Midnight',
            amount: _.sumBy(
                data.settled.filter(x => {
                    const hour = new Date(x.CreatedOn).getHours();
                    const min = new Date(x.CreatedOn).getMinutes();
                    return (hour === 23 && min >= 58) || hour === 0;
                }),
                'SettleAmount'
            )
        }
    ]



    const [state, setState] = React.useState({

        series: [{
            name: 'Inflation',
            data: [2.3, 3.1, 4.0, 10.1, 4.0, 3.6, 3.2, 2.3, 1.4, 0.8]
        }],
        options: {
            chart: {
                height: 350,
                type: 'bar',
            },
            plotOptions: {
                bar: {
                    borderRadius: 10,
                    dataLabels: {
                        position: 'top', // top, center, bottom
                    },
                }
            },
            dataLabels: {
                enabled: false,
                formatter: function (val) {
                    return val + "%";
                },
                offsetY: -20,
                style: {
                    fontSize: '12px',
                    colors: ["#304758"]
                }
            },

            xaxis: {
                categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep"],
                position: 'top',
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
                crosshairs: {
                    fill: {
                        type: 'gradient',
                        gradient: {
                            colorFrom: '#D8E3F0',
                            colorTo: '#BED1E6',
                            stops: [0, 100],
                            opacityFrom: 0.4,
                            opacityTo: 0.5,
                        }
                    }
                },
                tooltip: {
                    enabled: true,
                }
            },
            yaxis: {
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false,
                },
                labels: {
                    show: false,
                    formatter: function (val) {
                        return val + "%";
                    }
                }

            },
            // title: {
            //     text: 'Monthly Inflation in Argentina, 2002',
            //     floating: true,
            //     offsetY: 330,
            //     align: 'center',
            //     style: {
            //         color: '#444'
            //     }
            // }
        },


    });


    const categories = _(data.settled)
        .flatMap(order => order.Items.filter(item => item.KdsItemStatus !== 'Deleted'))
        .groupBy('Category')
        .map((group, name) => ({
            Name: name,
            TotalAmount: _.sumBy(group, 'TotalAmount'),
            Quntity: _.sumBy(group, 'Quntity'),
            Avtar: Math.floor(Math.random() * 10) + 1
        }))
        .value();


    useEffect(() => {
        if (isExpanded) {
            y.set(0); // lock y to 0
        }
    }, [isExpanded]);

    useEffect(() => {
        controls.set(EXPAND_OFFSET); // show default at 300px
    }, []);

    // Detect if scroll is at top
    useEffect(() => {
        const el = scrollRef.current;
        let startY = 0;

        const onTouchStart = (e) => {
            startY = e.touches[0].clientY;
        };

        const onTouchMove = (e) => {
            const currentY = e.touches[0].clientY;
            const diffY = currentY - startY;

            // At top, user is pulling down → collapse
            if (el.scrollTop <= 0 && diffY > 10) {
                controls.start({ y: EXPAND_OFFSET });
                setIsExpanded(false);
            }
        };

        if (el) {
            el.addEventListener('touchstart', onTouchStart);
            el.addEventListener('touchmove', onTouchMove);
        }

        return () => {
            el?.removeEventListener('touchstart', onTouchStart);
            el?.removeEventListener('touchmove', onTouchMove);
        };
    }, [controls]);

    const handleDragEnd = (_, info) => {
        const offset = info.offset.y;
        const velocity = info.velocity.y;

        if (offset > 100 || velocity > 500) {
            // Collapse
            controls.start({ y: EXPAND_OFFSET });
            setIsExpanded(false);
        } else {
            // Expand
            controls.start({ y: 0 });
            setIsExpanded(true);
        }
    };

    return (
        <>
            {/* Backdrop */}
            <div
                // style={{
                //     position: 'fixed',
                //     inset: 0,
                //     background: 'rgba(0,0,0,0.3)',
                //     zIndex: 99,
                // }}
            />

            {/* Sheet */}
            <motion.div
                drag={isExpanded ? false : 'y'}  // 👈 drag only when collapsed
                dragConstraints={{ top: 0, bottom: EXPAND_OFFSET }}
                onDragEnd={handleDragEnd}

                className={'text-black bg-gray-100'}
                style={{
                    position: 'fixed',
                    bottom: 0,
                    left: 0,
                    width: '100%',
                    height: MAX_HEIGHT,
                    borderTopLeftRadius: 16,
                    borderTopRightRadius: 16,
                    clipPath: `path("M0,0 
                  H50 
                  A16,16 0 0 1 66,0 
                  H100 
                  V100 
                  H0 
                  Z")`,
                    WebkitClipPath: `path("M0,0 
                  H50 
                  A16,16 0 0 1 66,0 
                  H100 
                  V100 
                  H0 
                  Z")`,
                    zIndex: 100,
                    y: y,
                    touchAction: 'none',

                }}
                animate={controls}
                transition={{type: 'spring', stiffness: 300, damping: 30}}
            >
                {/* Drag handle */}
                {/*<div*/}
                {/*    style={{*/}
                {/*        height: 24,*/}
                {/*        display: 'flex',*/}
                {/*        justifyContent: 'center',*/}
                {/*        alignItems: 'center',*/}
                {/*    }}*/}
                {/*>*/}
                {/*    <div*/}
                {/*        style={{*/}
                {/*            width: 40,*/}
                {/*            height: 4,*/}
                {/*            background: '#ccc',*/}
                {/*            borderRadius: 2,*/}
                {/*        }}*/}
                {/*    />*/}
                {/*</div>*/}

                {/* Scrollable content */}
                <div
                    ref={scrollRef}
                    // onTouchMove={(e) => {
                    //     if (!isExpanded) e.preventDefault();
                    // }}
                    style={{
                        height: `calc(100% - 24px)`,
                        overflowY: isExpanded ? 'auto' : 'hidden',  // 👈 only scroll if expanded
                        WebkitOverflowScrolling: 'touch',
                        pointerEvents: isExpanded ? 'auto' : 'none', // 👈 prevent tap/click while collapsing
                    }}
                >
                    <div className="">
                        <div className={'absolute left-0 w-full -top-1.5 center'}>
                            <div className={'bg-white/20 w-9 h-3.5 border-4 border-black rounded-full '}>

                            </div>
                        </div>
                        <div className="sheet-modal-swipe-step p-6 px-0 space-y-5" style={{height: '222px'}}>

                            <div className={'flex-c px-6 !gap-3'}>
                                <h6 className={'text-sm bg-[#010103] center rounded-lg text-white font-bold w-[50px] h-[35px]'}>{sumBy(data.settled, (x) => (x.Person))}</h6>
                                <div>
                                    <h6 className={'text-xs text-muted'}>Total visited customer</h6>
                                    <h6 className={' font-bold'}>Average</h6>
                                </div>
                            </div>
                            <div className={'px-6'}>
                                <div className={'card-white relative h-[115px]   !px-0'}>
                                    <div className={'flex-c px-4'}>
                                        <div className={' flex flex-col '}>
                                            <h6 className={'font-bold'}>Service agent</h6>
                                            <h6 className={'text-xxs mb-1'}>Agent check 3m ago</h6>

                                            <div className={'absolute left-4 bottom-4 flex-c'}>
                                                <h6 className={'green-badge !text-sm !px-1.5 !font-bold  w-fit'}>5/5</h6>
                                            </div>
                                        </div>
                                        <div className={'absolute right-1 -top-0.5'}>
                                            <ApexChart2/>
                                        </div>
                                    </div>
                                    {/*<h6 className={'border-t border-gray-100 px-3 pt-2 text-xs text-muted !gap-1.5 flex-c'}><i className={'fa fa-check-circle text-green'}></i> All services is up-to-date</h6>*/}
                                </div>

                                {/*<SemiDoughnutChart*/}
                                {/*    series={[44, 55, 41, 17]}*/}
                                {/*    labels={['A', 'B', 'C', 'D']}*/}
                                {/*/>*/}

                            </div>
                            <div className={'px-6'}>
                                <h5>Payments</h5>
                            </div>
                            <div className={'px-6 pr-0'}>
                                <swiper-container
                                    className="demo-swiper-multiple demo-swiper-multiple-auto"
                                    space-between="15"
                                    slides-per-view="auto"
                                >
                                    {data.todayPaymentSummary.map((item, idx) => (
                                        <swiper-slide key={item.type} className="!w-[150px]">
                                            <div className={'card-white w-[150px]'}>
                                                <div className={'flex-c'}>
                                                    <img className={'w-[30px] mr-1'} src={item.img}/>
                                                    <div>
                                                        <h6 className={'font-semibold'}>{item.type}</h6>
                                                        <h6 className={'text-xs text-muted'}>{item.orders} Orders</h6>
                                                    </div>
                                                </div>
                                                <div className={'bg-gray-50 rounded-xl p-1.5 px-3 mt-4'}>
                                                    <h6 className={'font-semibold opacity-100'}>{Money(item.amount)}</h6>
                                                    <h6 className={'text-green text-xxs font-semibold'}>
                                                        <i className={'fa fa-arrow-trend-up'}></i> {item.change}
                                                    </h6>
                                                </div>
                                            </div>
                                        </swiper-slide>
                                    ))}
                                </swiper-container>

                            </div>

                            <div className={'px-6'}>
                                <h5>Items</h5>
                            </div>

                            <div className={'px-6'}>
                                <div className={''}>
                                    <div className={'card-white !px-0 space-y-5'}>
                                        {/*<div style={{display: 'flex', justifyContent: 'center'}}>*/}
                                        {/*    <Chart options={state.options} series={state.series} type="bar" height={350}/>*/}
                                        {/*</div>*/}
                                        {_.take(_.orderBy(items, ['TotalAmount'], ['desc']), showAll ? items.length : 5).map((i, idx) => (
                                            <div className={'px-3'} key={idx}>
                                                <div className={'flex-c'}>
                                                    <img className={'w-[38px] mr-1'}
                                                         src={'/images/dishavtar/' + i.Avtar + '.svg'}/>
                                                    <div>
                                                        <h6 className={'font-semibold'}>{i.Name}</h6>
                                                        <h6 className={'text-xs text-muted'}>{i.Quntity} Quntity</h6>
                                                    </div>
                                                    <div className={'ms-auto'}>
                                                        <h6 className={'font-semibold'}>{Money(i.TotalAmount)}</h6>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                        {!showAll && <h6 onClick={() => setShowAll(true)}
                                                         className={'border-t font-semibold px-6 border-gray-200 pt-3 pb-1 px-3 flex-c'}>Show
                                            All <i className={'fa ms-auto fa-chevron-down'}></i></h6>
                                        }
                                    </div>

                                </div>
                            </div>


                            <div className={'px-6'}>
                                <h5>Category</h5>
                            </div>

                            <div className={'px-6'}>
                                <div className={''}>
                                    <div className={'card-white !px-0 space-y-5'}>

                                        {_.take(_.orderBy(categories, ['TotalAmount'], ['desc']), showCategory ? categories.length : 5).map((i, idx) => (
                                            <div className={'px-3'} key={idx}>
                                                <div className={'flex-c'}>
                                                    <img
                                                        className={'w-[30px] h-[30px] bg-gray-200 p-1 rounded-full mr-1'}
                                                        src={'/images/categoryavtar/' + i.Avtar + '.svg'}/>
                                                    <div>
                                                        <h6 className={'font-semibold'}>{i.Name}</h6>
                                                        <h6 className={'text-xs text-muted'}>{i.Quntity} Quntity</h6>
                                                    </div>
                                                    <div className={'ms-auto'}>
                                                        <h6 className={'font-semibold'}>{Money(i.TotalAmount)}</h6>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                        {!showCategory && <h6 onClick={() => setShowCategory(true)}
                                                              className={'border-t font-semibold px-6 border-gray-200 pt-3 pb-1 px-3 flex-c'}>Show
                                            All <i className={'fa ms-auto fa-chevron-down'}></i></h6>
                                        }
                                    </div>

                                </div>
                            </div>


                            <div className={'px-6'}>
                                <h5>Periodic</h5>
                            </div>
                            <div className={'px-6'}>
                                <div className={''}>
                                    <div className={'card-white !px-0 space-y-5'}>

                                        {periodic.map((i, idx) => (
                                            <div className={'px-3'} key={idx}>
                                                <div className={'flex-c'}>
                                                    {i.name == "Morning" && <img
                                                        className={'w-[30px] h-[30px] bg-green-400 p-1 rounded-full mr-1'}
                                                        src={'/images/alarm_add.svg'}/>}
                                                    {i.name == "Evening" && <img
                                                        className={'w-[30px] bg-red-300 h-[30px] bg-gray-200 p-1 rounded-full mr-1'}
                                                        src={'/images/evening.svg'}/>}

                                                    {i.name == "Night" && <img
                                                        className={'w-[30px] h-[30px] bg-slate-600 p-1 rounded-full mr-1'}
                                                        src={'/images/night.svg'}/>}

                                                    {i.name == "Midnight" && <img
                                                        className={'w-[30px] h-[30px] bg-blue-400 p-1 rounded-full mr-1'}
                                                        src={'/images/midnight.svg'}/>}


                                                    <div>
                                                        <h6 className={'font-semibold text-xs text-muted'}>{i.name}</h6>
                                                        <h6 className={''}>{Money(i.amount)}</h6>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}

                                    </div>

                                </div>
                            </div>


                        </div>

                    </div>
                </div>
            </motion.div>
        </>
    );
}

const CustomSheet = () => {
    const [sheetTop, setSheetTop] = useState('300px');
    const [dragStartY, setDragStartY] = useState(null);

    const handleTouchStart = (e) => {
        setDragStartY(e.touches[0].clientY);
    };

    const handleTouchMove = (e) => {
        if (dragStartY === null) return;
        const deltaY = e.touches[0].clientY - dragStartY;
        // If dragged up more than 30px, move to 10% from top
        if (sheetTop === '300px' && deltaY < 0) {
            setSheetTop('10%');
            setDragStartY(null);
        }
    };

    return (
        <AnimatePresence>
            <motion.div
                className={'text-black'}
                style={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: sheetTop,
                    background: '#fff',
                    borderTopLeftRadius: 16,
                    borderTopRightRadius: 16,
                    overflow: 'hidden',
                    zIndex: 1000,
                    boxShadow: '0 -2px 16px rgba(0,0,0,0.1)'
                }}
                initial={{top: sheetTop}}
                animate={{top: sheetTop}}
                transition={{type: "spring", stiffness: 400, damping: 32, duration: 0.25}}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
            >
                {sheetTop}
                <div
                    style={{
                        height: '100%',
                        overflowY: 'auto',
                        padding: 16
                    }}
                >
                    <div className={'h-[1000px] overflow-scroll bg-white w-full'}>
                        {Array.from({length: 300}, (_, i) => (
                            <h6 className={'text-black'} key={i}>Item {i + 1}</h6>
                        ))}
                    </div>
                </div>
            </motion.div>
        </AnimatePresence>
    );
}


export function MyApp() {
    const sheetRef = useRef(null);
    const sheetInstance = useRef(null);

    useEffect(() => {
        f7ready(() => {
            const el = sheetRef.current?.el;
            if (el) {
                sheetInstance.current = f7.sheet.create({
                    el,
                    swipeToStep: true,
                    swipeToClose: false, // prevent accidental closing
                    backdrop: true,
                });

                // Open and step to 300px after DOM ready
                setTimeout(() => {
                    sheetInstance.current.open(false); // show without animation
                    sheetInstance.current.stepOpen(false); // go to step (300px)
                }, 50);
            }
        });
    }, []);

    return (
        <App>
            <View main>
                <Page>
                    {/* Sheet */}
                    <Sheet
                        ref={sheetRef}
                        className="my-swipe-sheet"
                        style={{height: '80vh'}} // Final expanded height
                    >
                        {/* Step height handler */}
                        <div
                            className="sheet-modal-swipe-step"
                            style={{height: '300px'}}
                        ></div>

                        {/* Scrollable Content */}
                        <div
                            style={{
                                height: '100%',
                                overflowY: 'auto',
                                padding: '16px',
                            }}
                        >
                            <BlockTitle>Scrollable Content</BlockTitle>
                            <Block strong>
                                <p>Swipe up to expand, swipe down to collapse.</p>
                                {[...Array(30)].map((_, i) => (
                                    <p key={i}>This is line {i + 1}</p>
                                ))}
                            </Block>
                        </div>
                    </Sheet>
                </Page>
            </View>
        </App>
    );
}

// App component
const HomeScreen = () => {
    const [sheetOpened, setSheetOpened] = useState(false);
    const [calander,setCalander] = useState(false)
    const [outletSheet,setOutletSheet] = useState(false)
    const[selectedDate,setSelectedData] = useState(null)
    const [outletList,setOutletList] = useState([])
    const {data,setData} = useShared()
    const calendarInline = useRef(null);
const [selected,setSelected] = useState(null)
    const onPageInit = () => {
        const $ = f7.$;

        // Inline with custom toolbar
        const monthNames = [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December',
        ];
        calendarInline.current = f7.calendar.create({
            containerEl: '#demo-calendar-inline-container',
            value: [new Date()],
            renderToolbar() {
                return `
          <div class="toolbar calendar-custom-toolbar">
            <div class="toolbar-inner">
              <div class="left">
                <a  class="link icon-only"><i class="icon icon-back"></i></a>
              </div>
              <div class="center"></div>
              <div class="right">
                <a  class="link icon-only"><i class="icon icon-forward"></i></a>
              </div>
            </div>
          </div>
        `.trim();
            },
            on: {
                calendarDayClick:(calendar, dayEl, year, month, day)=>{
                    var mainDate = moment({ year, month, day }).toDate();
                    setCalander(false)

                    setSelectedData(moment(mainDate).format('DD MMM'))
var iso =  moment(mainDate).format('YYYY-MM-DD')
                    api.get('/admin/GetDataForMobile?date='+iso).then(res => {
                        setData(res.data)
                    })
                },
                init(c) {
                    $('.calendar-custom-toolbar .center').text(
                        `${monthNames[c.currentMonth]}, ${c.currentYear}`,
                    );
                    $('.calendar-custom-toolbar .left .link').on('click', () => {
                        calendarInline.current.prevMonth();
                    });
                    $('.calendar-custom-toolbar .right .link').on('click', () => {
                        calendarInline.current.nextMonth();
                    });
                },
                monthYearChangeStart(c) {
                    alert('jigar')
                    $('.calendar-custom-toolbar .center').text(
                        `${monthNames[c.currentMonth]}, ${c.currentYear}`,
                    );
                },
            },
        });
    };
    const onPageBeforeRemove = () => {
        calendarInline.current.destroy();
    };


    // useEffect(()=>{
    //    setTimeout(()=>{
    //        setSheetOpened(true)
    //    },0)
    // },[])
    return (
        <Page onPageInit={onPageInit} onPageBeforeRemove={onPageBeforeRemove} style={{background: 'URL("/images/Mask group.png")',backgroundSize:'cover'}} className="">
        {/*<img*/}
        {/*    className="absolute opacity-20"*/}
        {/*    src="/images/blur.png?8"*/}
        {/*    style={{*/}
        {/*        zIndex: -1,*/}
        {/*        right: '-32px',*/}
        {/*        width: '436px',*/}
        {/*        objectFit: 'contain',*/}
        {/*        top: '90px'*/}
        {/*    }}*/}
        {/*/>*/}
        <div className={'flex flex-col h-full'}>
            <div className={' flex-col flex flex-1 p-5'}>

                <div className={'flex-c'}>
                    <h6 onClick={async()=>{
                        f7.preloader.show();

                        api.get('/admin/FindDIrectorOutlet?date='+(selectedDate==null?'':selectedDate?.toISOString())+'&getcollectiontoo=true').then((res) => {
                            f7.preloader.hide();
                            setOutletList(res.data)
                            setOutletSheet(true)
                        })

                    }} className={'flex-c !gap-2 capitalize'}>
                        <img className={'w-7'}
                             src={'https://cdn.myportfolio.com/17be4dd08c5417027a544816a909fcf8/1edf1f42-afdc-4e07-bfa5-5af4e03ba899_rw_600.gif?h=28de286a7048153f9990a720048e841f'}/>
                        {localStorage.getItem('area')} <i className={'fa fa-chevron-down'}></i></h6>
                    <div onClick={()=>setCalander(true)} className={'ms-auto flex-c'}>
                        <h6 className={'white-badge flex-c ms-auto'}>{(selectedDate==null ? 'Today' : selectedDate)}
                        </h6>
                        <i className={'far opacity-70 ml-2 text-sm fa-calendar-star'}></i>
                    </div>
                </div>
                <div className="flex-grow"></div>
                <div>
                    <h6 className={'-mb-1 flex-c '}>Earnings
                        {/*<h6 className={'white-badge-rounded flex-c  ms-auto'}>Ahmedabad<i*/}
                        {/*    className={'far opacity-70 fa-chevron-down'}></i>*/}
                        {/*</h6>*/}
                    </h6>
                    <div className={'flex items-end'}><h2>{Money(sumBy(data.settled,(x)=>x.SettleAmount))}</h2><span
                        className={'mb-4.5 ml-2 blue-badge-on-dark'}>{data.percentageString}</span></div>
                    <h6 className={'text-xs flex-c'}><span className={'text-green-on-dark flex-c'}><i
                        className={'far fa-clock'}></i>{data.settled && data.settled.length > 0 ? moment(data.settled.sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn))[0].CreatedOn).fromNow().replace("a few seconds ago","Just now") : ''}</span> <span className={'text-muted-dark'}>sync</span>
                    </h6>
                </div>
                <div className="flex-grow"></div>
                <div className={'grid grid-cols-3 gap-2'}>
                    <div className={'title-widget w-full'}>
                        <div>
                            <h5>{data.settled.length}</h5>
                            <h6>Orders</h6>
                        </div>
                        <div className={'ms-auto icon-bg-white'}><i className={'far fa-money-bill'}></i></div>
                    </div>
                    <div className={'title-widget w-full'}>
                        <div>
                            <h5>{data.deleted.length}</h5>
                            <h6>Deleted</h6>
                        </div>
                        <div className={'ms-auto icon-bg-white'}><i className={'far fa-money-bill'}></i></div>
                    </div>
                    <div className={'title-widget w-full'}>
                        <div>
                            <h5>{filter(map(data.settled.includes(data.deleted),(x)=>(x.Items)),(x)=>(x.Type==8)).length}</h5>

                            <h6>Modified</h6>
                        </div>
                        <div className={'ms-auto icon-bg-white'}><i className={'far fa-money-bill'}></i></div>
                    </div>
                </div>
                <div className="flex-grow"></div>
                <div className={'relative'}>
                    <div className={'absolute w-[125%] -left-8'}><ApexChart/></div>
                    <div className={'flex-c mt-35 !gap-8'}>
                        <div className={'flex-c'}>
                            <div style={{borderLeft: '2px dashed white'}} className={'h-7 opacity-50'}></div>
                            <div>
                                <h6 className={'!opacity-100 font-semibold text-green-on-dark'}>{Money(sumBy(data.settled,(x)=>x.TotalTax))}</h6>
                                <h6 className={'text-xs'}>Tax</h6>
                            </div>
                        </div>
                        <div className={'flex-c'}>
                            <div style={{borderLeft: '2px dashed white'}} className={'h-7 opacity-50'}></div>
                            <div>
                                <h6 className={'!opacity-100 font-semibold '}>{Money(sumBy(data.settled,(x)=>x.FlatDiscount))}</h6>
                                <h6 className={'text-xs'}>Discount</h6>
                            </div>
                        </div>


                        <div className={'ms-auto text-end text-xxs'}>
                            <span className={'opacity-50'}>Revenue {data.monthPercentageString.includes('+') ? 'up' : 'loss'} <br/>this month by</span> <span
                            className={'font-bold'}>{data.monthPercentageString.replace('-','').replace('+','')}</span>
                        </div>
                    </div>
                </div>
                {/*<div fill onClick={() => setSheetOpened(true)}>*/}
                {/*    Open Swipe Sheet*/}
                {/*</div>*/}
                <div>

                    <Sheet
                        style={{height: '60vh', maxHeight: '60vh'}}
                        swipeToClose={true}
                        backdrop={true}
                        opened={outletSheet}
                        onSheetClosed={() => setOutletSheet(false)}
                    >
                        <div className="p-4">
                            <div className={'px-2 py-2 pb-0'}>
                                <h5>Select Outlet</h5>
                            </div>
                            <List className={'!py-2 !my-2'}>
                                {outletList.map((item)=>(
                                    <ListItem
                                        radio
                                        title={item.Area}
                                        name="my-radio"
                                        value={item.Area}
                                        checked={item.Area === localStorage.getItem('Area')}
                                        onChange={() => {
                                            localStorage.setItem('area',item.Area)

                                            api.get('/admin/Step2_VerifyOTP?Phone='+localStorage.getItem('phone')+'&redirectOutletId='+item.ID).then((_res) => {
                                                localStorage.setItem('jwt', data.data.JWT)
                                                localStorage.setItem('phone', data.data.phone)
                                                window.location.reload()
                                            })
                                        }}
                                    />
                                ))}
                            </List>
                        </div>
                    </Sheet>


                    <Sheet
                        className="calendar-sheet"
                        style={{height: '60vh', maxHeight: '60vh'}}
                        swipeToClose={true}
                        backdrop={true}
                        opened={calander}
                        onSheetClosed={() => setCalander(false)}
                    >
                        <div className="p-4">
                            <div className={'px-3 py-2'}>
                                <h5>Select Date</h5>
                            </div>
                            <div id="demo-calendar-inline-container"/>
                        </div>
                    </Sheet>

                    <SwipeableSheet data={data}/>
                    {/*<CustomSheet/>*/}
                    {/*<Sheet*/}
                    {/*    opened={true}*/}
                    {/*    className="demo-sheet-swipe-to-step text-black"*/}
                    {/*    style={{ height: 'auto' }}*/}
                    {/*    swipeToClose*/}
                    {/*    swipeToStep*/}
                    {/*    push*/}
                    {/*    backdrop*/}
                    {/*>*/}
                    {/*    <div className="swipe-handler" onClick={() => toggleSwipeStep()}></div>*/}
                    {/*    <div className="sheet-modal-swipe-step">*/}
                    {/*        <div className="display-flex padding justify-content-space-between align-items-center">*/}
                    {/*            <div style={{ fontSize: '18px' }}>*/}
                    {/*                <b>Total:</b>*/}
                    {/*            </div>*/}
                    {/*            <div style={{ fontSize: '22px' }}>*/}
                    {/*                <b>$500</b>*/}
                    {/*            </div>*/}
                    {/*        </div>*/}
                    {/*        <div className="padding-horizontal padding-bottom">*/}

                    {/*            <div className="margin-top text-align-center">Swipe up for more details</div>*/}
                    {/*        </div>*/}
                    {/*    </div>*/}
                    {/*    <BlockTitle medium className="margin-top">*/}
                    {/*        Your order:*/}
                    {/*    </BlockTitle>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}
                    {/*<div>jigar</div>*/}

                    {/*</Sheet>*/}
                </div>
            </div>
            <div className={'h-[230px] w-full px-3'}>
                <div className={'w-full h-full bg-white/20 rounded-2xl'}></div>
            </div>
        </div>
        {/*<video muted autoPlay*/}
        {/*            className="w-full rounded-lg mt-6"*/}
        {/*            src="https://cdn.dribbble.com/userupload/15363754/file/large-49b5bc9b98681df8ad6e42969f60935f.mp4"*/}
        {/*            poster="/images/video-poster.png"*/}
        {/*            style={{maxWidth: 400}}*/}
        {/*        >*/}
        {/*            Your browser does not support the video tag.*/}
        {/*        </video>*/}
    </Page>);
}

// App component
const MainScreen = () => {

    const [currentPage, setCurrentPage] = useState('');
    const [data, setData] = useState(null)

    useEffect(() => {
        try {
            window.handleBackButton = function () {
                const openedSheets = document.querySelectorAll('.modal-in');

                if (openedSheets.length == 0) {
                    if (currentPage == 'cart') {
                        f7.views.main.router.back({transition: 'f7-dive'})
                    }
                } else {
                    openedSheets.forEach(sheetEl => {
                        const sheetInstance = f7.sheet.get(sheetEl);
                        if (sheetInstance) sheetInstance.close();
                    });
                }
            };
        } catch {
        }
    }, [currentPage]);


    useEffect(() => {
        f7ready(() => {
            const handler = (page) => {
                setCurrentPage(page.name || page.route.name || '');
                console.log('Current Page:', page.name);
            };
            f7.on('pageAfterIn', handler);

            return () => {
                f7.off('pageAfterIn', handler);
            };
        });
    }, []);


    return (
        <SharedContext.Provider value={{
            currentPage, data, setData,
        }}>
            <App
                theme="md" // auto theme to let Framework7 decide based on platform
                pushState={true}
                iosTranslucentBars={false} // Optional: controls the appearance of the navigation bar
                routes={[
                    {path: '/', component: SplashScreen},
                    {path: '/login', component: LoginScreen},
                    {path: '/otp', component: OTPScreen},
                    {path: '/home', component: HomeScreen},
                    // { path: '/', component: Login },
                    // {path: '/tables', component: Tables, transition: 'f7-dive'},
                ]}
            >
                <View id="main-view" main url="/" options={{transition: 'f7-slide'}}/>
            </App>
        </SharedContext.Provider>
    )
};

const container = document.getElementById("app");
let root = container._rootContainer;
if (!root) {
    root = ReactDOM.createRoot(container);
    container._rootContainer = root;
}
root.render(<MainScreen/>);

if (import.meta.hot) {
    import.meta.hot.accept(() => {
        root.render(<MainScreen/>);
    });
}

// if (import.meta.hot) {
//     import.meta.hot.accept((newModule) => {
//         const NewApp = newModule.default;
//         root.render(<NewApp />);
//     });
// }

export default MainScreen;