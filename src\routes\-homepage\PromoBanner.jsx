import { motion } from "framer-motion";
import { CloseOutlined } from "@ant-design/icons";
import { useState } from "react";

const PromoBanner = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ y: -50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: -50, opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 text-white py-3 px-4 relative overflow-hidden"
    >
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-20">
        <motion.div
          animate={{ x: [-100, 100] }}
          transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transform skew-x-12"
        />
      </div>

      <div className="relative z-10 flex items-center justify-center text-center">
        <motion.p
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
          className="text-sm font-medium"
        >
          🎉 Limited time offer! Get 20% off on yearly plans.{" "}
          <motion.span
            whileHover={{ scale: 1.05 }}
            className="underline cursor-pointer font-semibold"
          >
            Learn more →
          </motion.span>
        </motion.p>

        <motion.button
          whileHover={{ scale: 1.1, rotate: 90 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setIsVisible(false)}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/80 hover:text-white transition-colors"
        >
          <CloseOutlined className="text-xs" />
        </motion.button>
      </div>
    </motion.div>
  );
};

export default PromoBanner;
