import { createFileRoute } from '@tanstack/react-router';
import RatingCard, { G2Logo, GoogleLogo, RatingCardGroup } from '../../component/RatingCard.jsx';

export const Route = createFileRoute('/demo/rating')({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Rating Component Demo</h1>
          <p className="text-lg text-gray-600">
            Pixel-perfect rating cards matching the reference design
          </p>
        </div>

        {/* Main Rating Cards - Matching Reference Image */}
        <div className="mb-16">
          <h2 className="text-2xl font-semibold text-gray-800 mb-8 text-center">
            Reference Design Implementation
          </h2>
          <RatingCardGroup>
            <RatingCard
              platform="G2"
              rating={4.6}
              reviewCount={127}
              description="Rating by G2 users"
              logo={<G2Logo />}
            />
            <RatingCard
              platform="Google"
              rating={4.8}
              reviewCount={932}
              description="on Google Review"
              logo={<GoogleLogo />}
            />
          </RatingCardGroup>
        </div>

        {/* Component Variations */}
        <div className="mb-16">
          <h2 className="text-2xl font-semibold text-gray-800 mb-8 text-center">
            Component Variations
          </h2>
          
          {/* Single Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <RatingCard
              platform="G2"
              rating={4.6}
              reviewCount={127}
              description="Rating by G2 users"
              logo={<G2Logo />}
            />
            <RatingCard
              platform="Google"
              rating={4.8}
              reviewCount={932}
              description="on Google Review"
              logo={<GoogleLogo />}
            />
            <RatingCard
              platform="Custom"
              rating={4.9}
              reviewCount={1250}
              description="Customer satisfaction"
              logo={
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">★</span>
                </div>
              }
            />
          </div>

          {/* Different Rating Values */}
          <div className="grid md:grid-cols-2 gap-8">
            <RatingCard
              platform="Trustpilot"
              rating={4.2}
              reviewCount={89}
              description="Trustpilot reviews"
              logo={
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">TP</span>
                </div>
              }
            />
            <RatingCard
              platform="App Store"
              rating={4.7}
              reviewCount={2456}
              description="App Store rating"
              logo={
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AS</span>
                </div>
              }
            />
          </div>
        </div>

        {/* Usage Examples */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Usage Examples</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Basic Usage</h3>
              <div className="bg-gray-100 rounded-lg p-4 font-mono text-sm">
                <pre>{`<RatingCard
  platform="G2"
  rating={4.6}
  reviewCount={127}
  description="Rating by G2 users"
  logo={<G2Logo />}
/>`}</pre>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">With Custom Logo</h3>
              <div className="bg-gray-100 rounded-lg p-4 font-mono text-sm">
                <pre>{`<RatingCard
  platform="Custom"
  rating={4.9}
  reviewCount={1250}
  description="Customer satisfaction"
  logo={
    <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
      <span className="text-white font-bold">★</span>
    </div>
  }
/>`}</pre>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-3">Grouped Cards</h3>
              <div className="bg-gray-100 rounded-lg p-4 font-mono text-sm">
                <pre>{`<RatingCardGroup>
  <RatingCard {...g2Props} />
  <RatingCard {...googleProps} />
</RatingCardGroup>`}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
