import { createFileRoute } from '@tanstack/react-router'
import {Money, showToastProgress} from "../../utils/util.jsx";
import api from "../../utils/api.js";
import {toast} from "sonner";
import {Button, Dropdown, Tag, Tooltip} from "antd";
import moment from "moment/moment.js";
import {AGGridBase} from "../../component/AGGridBase.jsx";

export const Route = createFileRoute('/reports/MonthlySale')({
  component: RouteComponent,
})


function RouteComponent() {

  const summary = [
    { field: "Taxable", type: "sum", positionColumn:'Taxable',cellTemplate: (summary) => (summary.Taxable) },
    { field: "SettleAmount", type: "sum", cellTemplate: (summary) => (summary.SettleAmount) },
    { field: "ID", type: "count",positionColumn: 'BillNo',cellTemplate: (summary) => (summary.ID+' COUNT') },
  ]

  const downloadReport = (date) =>{
    var t = showToastProgress('Downloading report..')
    api.get('/admin/DownloadDaySummaryReport?export=true&localDate='+date, {responseType: 'arraybuffer'}).then((res) => {
      // Create a new Blob from the response data
      const blob = new Blob([res.data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});

      // Create a temporary link element
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);

      // Set the download file name (adjust "DaySummary.xlsx" or make it dynamic based on the response if necessary)
      link.download = 'DaySummary.xlsx';

      // Append the link to the body, trigger click, and remove it afterward
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.dismiss(t)
    }).catch((error) => {
      console.error('Error while downloading the report:', error);
      toast.dismiss(t)
    });
  }

  const filterArray = []

  const colDef = [
    {
      field: "Month",
      icon: 'calendar',
      width:250,
      hide: true,
      cellRenderer: (params) => {
        return <div>{moment(params.data.CreatedOn).format('MMM YYYY')}</div>
      }
    },
    {
      field: "Quarter",
      icon: 'calendar-range',
      enableRowGroup: true,
      rowGroup: true,
      hide: true,
      cellRenderer: (params) => {
        return <Tag bordered={false} className={'!small'}>{params.value}</Tag>
      }
    },
    {field: "Taxable", icon: 'indian-rupee-sign', aggregate: 'Sum', money: true},
    {field: "Tax", icon: 'building-columns', aggregate: 'Sum',money:true},
    {field: "SettleAmount",headerName:'Settle',icon: '#check-circle',aggregate:'Sum',cellRenderer: (params) => {
        return <div className={'w-full flex-c'}><i className={'fa mr-3  text-green fa-check-circle text-green-500'} /> {Money(params.value)}
        </div>
      }
    },

    {
      field: "Orders", icon: 'clipboard-list-check',cellRenderer: (params) => {
        return <Tag  bordered={false} className={'!small'}>{params.data.Orders} Orders</Tag>
      } },
    {field: "Cash",icon:'money-bill-1-wave',aggregate:'Sum',money:true},
    {field: "Card",icon:'credit-card-blank',aggregate:'Sum',money:true},
    {field: "Online",aggregate:'Sum',money:true,icon:'laptop-mobile'},
    {field: "Wallet",aggregate:'Sum',money:true,icon:'wallet'},
    {field: "Discount",aggregate:'Sum',money:true,icon:'percent'},

    {field: "Person",aggregate:'Sum', cellRenderer: (params) => {
        return <span  bordered={false} className={'text-slate-500'}><i className={'fa fa-user'}></i> {params.value} Person</span>
      } },
    {field: "RePrinted",icon:'print'},
    {field: "ByCaptain",icon:'poll-people'},
    {width:260, field: "Started",headerName:'Business Hours',cellRenderer: (params) => (
          <div className={'flex-c gap-2'}><span className={'text-slate-500'}>{moment(params.value).format('hh:mm A')}</span><i className={'fa text-xs fa-arrow-right'}></i><span
              className={'text-slate-500'}>{moment(params.data.End).format('h:mm A')}</span> <span className={'flex-c gap-1 text-slate-400  ms-auto'}><i className={'fa text-xs fa-business-time'}></i> <span className={'ml-1 w-[40px]'}>{moment(params.data.End).diff(moment(params.value), 'hours', true).toFixed(1)+' hr'}</span></span> </div>)
      ,icon:'business-time'},

    {field: "DeletedBill",icon:'trash-list'},
    {field: "GST",money:true,icon:'building-columns'},
    {field: "Cess",money:true},
    {field: "VAT",money:true},
    {field: "DineInCount",icon:'house'},
    {field: "DineInAmount",money:true},
    {field: "ParcelCount",icon:'box-taped'},
    {field: "DeliveryCount"},
    {field: "DeliveryAmount",money:true},
    {field: "ZomatoInCount"},
    {field: "ZomatoAmount",money:true},
    {field: "SwiggyCount"},
    {field: "SwiggyAmount",money:true},

    {width:65,pinned: 'right',cellRenderer: (params) => (<div className={'flex items-center justify-end'}>
        <Dropdown trigger={['click']} menu={{items:[{onClick:()=>downloadReport(params.data.CreatedOn), label:'Download detail report',icon:<i className={'fa fa-download'}></i>}]}}>
          <Button className={'!bg-gray-100 hover:!bg-gray-300 mt-1.5'} icon={<i className={'fa fa-ellipsis '} />} type={'text'} size={'small'}></Button>

        </Dropdown>

      </div>)},

  ]



  return <AGGridBase sortArray={[{colId: 'CreatedOn', sort: 'asc'}, {colId: 'SettleAmount'}, {colId: 'Tax'}]}
                     group={[{label: 'SettleAmount'}, {label: 'Tax'}, {label: 'Quarter'}]}
                     filterList={filterArray}
                     title={"O"}
                     summary={summary}
                     column={colDef}
                     loadURL={'/AGOrder/GetDaySummary?monthly=true'}/>
}
