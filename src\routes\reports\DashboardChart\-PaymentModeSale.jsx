import {useEffect, useState} from "react";
import api from "../../../utils/api.js";
import {Button, Dropdown, Progress, Tag} from "antd";
import {ArrowDown, Money} from "../../../utils/util.jsx";
import {maxBy} from "lodash";
import moment from "moment";
import SimpleBar from "simplebar-react";

const PaymentModeSale = () => {
    const [data,setData] = useState([])
    const [dateRange, setDateRange] = useState([
        moment().startOf('year').toISOString(),
        moment().endOf('year').toISOString()
    ])
    useEffect(()=>{
        api.get('/dashboard/GetAllPaymentModes?from='+dateRange[0]+'&to='+dateRange[1]).then((res) => {
                setData(res.data)
            }
        )
    },[])
    return (<div className={'lg:col-span-1 card h-[447px]'}>
        <h6 className={'card-header font-bold'}>Payment Modes
            <div className={'ms-auto'}>
                <Dropdown>
                    <Button size={'small'} type={'text'}>This year <ArrowDown/></Button>
                </Dropdown>
                <Button size={'small'} type={'text'}
                                               icon={<i className={'fa fa-refresh'}></i>}></Button></div>
        </h6>
        <div className={'relative space-y-3.5 h-full'}>

            <SimpleBar
                style={{
                    maxHeight: '90%',
                    position: 'absolute', // <- Absolutely positioned
                    inset: 0, // top: 0; right: 0; bottom: 0; left: 0;
                }}
                autoHide={true} // optional
            >
                <div className={'space-y-6 p-6'}>
                    {data?.map((i) => (
                        <div className={'ring-2 rounded-lg ring-gray-200/50 p-4'}>
                            <h5 className={'flex-c'}>{Money(i.value)}
                                <div className={'ms-auto'}><Progress percent={parseInt(i.percentage)} steps={5}
                                                                     strokeColor={['#5DB182', '#5DB182', '#5DB182']}/>
                                </div>
                            </h5>
                            <div className={'mt-3 flex-c'}>
                                <h6 className={'!mb-0 font-semibold text-slate-500'}>
                                    {i.key == "Cash" && <i className={'fa fa-money-bill'}></i>}
                                    {i.key == "Card" && <i className={'fa fa-credit-card'}></i>}
                                    {i.key == "Online" && <i className={'fa fa-earth'}></i>}
                                    {i.key == "Wallet" && <i className={'fa fa-wallet'}></i>}

                                    {i.key}</h6>
                                <h6 className={'text-sm text-slate-400 ms-auto'}>
                                    <i className={'fa fa-eye'}></i> 6</h6>

                            </div>
                        </div>
                    ))}


                </div>

            </SimpleBar>


        </div>
    </div>)
}

export default PaymentModeSale;