import { motion } from "framer-motion";
import RatingCard, { G2<PERSON><PERSON>, GoogleLogo, RatingCardGroup } from '../../component/RatingCard.jsx';

const TrustedSection = () => {
  // Restaurant/business images
  const restaurantImages = [
    "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=300&h=200&fit=crop&auto=format", // Restaurant interior
    "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=300&h=200&fit=crop&auto=format", // Restaurant table
    "https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=300&h=200&fit=crop&auto=format", // Restaurant dining
    "https://images.unsplash.com/photo-1590846406792-0adc7f938f1d?w=300&h=200&fit=crop&auto=format", // Modern restaurant
    "https://images.unsplash.com/photo-1552566626-52f8b828add9?w=300&h=200&fit=crop&auto=format", // Restaurant kitchen
    "https://images.unsplash.com/photo-1578474846511-04ba529f0b88?w=300&h=200&fit=crop&auto=format", // Cafe interior
  ];

  // Brand logos (using placeholder brand names)
  const brandLogos = [
    { name: "Lumina", logo: "🔆" },
    { name: "Vortex", logo: "🌀" },
    { name: "Velocity", logo: "⚡" },
    { name: "Synergy", logo: "🔗" },
    { name: "Enigma", logo: "🔮" },
    { name: "Spectrum", logo: "🌈" },
    { name: "Nexus", logo: "🔷" },
    { name: "Quantum", logo: "⚛️" },
  ];

  return (
    <section className="relative py-16 bg-gradient-to-b from-white via-gray-50 to-gray-100">
      <div className="w-full">
        {/* Restaurant Images Marquee */}
        <div className="overflow-hidden mb-12">
          <motion.div
            className="flex space-x-8"
            animate={{ x: [0, -3200] }}
            transition={{
              duration: 80,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            {[...restaurantImages, ...restaurantImages].map((image, index) => (
              <div
                key={index}
                className="flex-shrink-0 w-[500px] h-80 rounded-xl overflow-hidden shadow-lg"
              >
                <img
                  src={image}
                  alt={`Restaurant ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </motion.div>
        </div>

        {/* Trust Text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12 px-8"
        >
          <h5 className="text-lg font-semibold text-slate-500 mb-4">
            Trusted by over 14,540 businesses to enhance learning and drive
            educational growth.
          </h5>
        </motion.div>

        {/* Brand Logos Marquee */}
        <div className="max-w-4xl mx-auto overflow-hidden mb-12">
          <motion.div
            className="flex items-center space-x-12"
            animate={{ x: [0, -1600] }}
            transition={{
              duration: 60,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            {[...brandLogos, ...brandLogos, ...brandLogos].map(
              (brand, index) => (
                <div
                  key={index}
                  className="flex-shrink-0 flex items-center space-x-3 text-gray-600"
                >
                  <span className="text-2xl">{brand.logo}</span>
                  <span className="text-lg font-medium">{brand.name}</span>
                </div>
              )
            )}
          </motion.div>
        </div>

        {/* Ratings - Using the new RatingCard component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <RatingCardGroup>
            <RatingCard
              platform="G2"
              rating={4.6}
              reviewCount={127}
              description="Rating by G2 users"
              logo={<G2Logo />}
            />
            <RatingCard
              platform="Google"
              rating={4.8}
              reviewCount={932}
              description="on Google Review"
              logo={<GoogleLogo />}
            />
          </RatingCardGroup>
        </motion.div>
      </div>
    </section>
  );
};

export default TrustedSection;
