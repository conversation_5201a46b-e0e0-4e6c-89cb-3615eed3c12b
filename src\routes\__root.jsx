// import {createRootRoute, Outlet, useMatch, useNavigate, useRouter, useRouterState} from '@tanstack/react-router';
// import React, {useEffect, useRef, useState} from 'react';
// import {Button, Divider, Dropdown, Input, Layout, Menu, Tag, Tooltip} from 'antd';
// import {ArrowDown, Dialog, Loading, loadingList, Media, setupAccount, SourceEnum} from "../utils/util.jsx";
// import {AGGridBase} from "../component/AGGridBase.jsx";
// import SimpleBar from "simplebar-react";
// import 'simplebar-react/dist/simplebar.min.css';
// import api from "../utils/api.js";
// const { Header, Content } = Layout;

import { createRootRoute, Outlet } from "@tanstack/react-router";

// // ✅ Your root layout component
// const RootLayout = () => {

//     const [hideSideBar,setHideSideBar] = useState(false);
//     const [upgrade, setUpgrade] = useState(false);
//     const [outletName,setOutletName] = useState(localStorage.getItem('outletName'));
// const [outletAvtar,setOutletAvtar] = useState(localStorage.getItem('outletAvtar'));
// const [outletArea,setOutletArea] = useState(localStorage.getItem('outletArea'));
// const [outletCity,setOutletCity] = useState(localStorage.getItem('outletCity'));
// const [outletAddress,setOutletAddress] = useState(localStorage.getItem('outletAddress'));
// const [hideNavbars,setHideNavbars] = useState(false)

// const [outlets,setOutlets] = useState(loadingList());
// const [phone,setOutletPhone] = useState(localStorage.getItem('phone'));
// const [loading,setLoading] = useState(false)
// const [deletedOrder,setDeletedOrder] = useState(0)

// const check = (path) =>{
//     if(path.includes('login')){
//         setHideNavbars(true)
//     }else{
//         setHideNavbars(false)
//     }

//     setHideSideBar(false)
//     if(path.includes('menu')){
//         setHideSideBar(true)
//     }

// }

//     const location2 = useRouterState({
//         select: (state) => state.location.pathname,
//     });

//     const previousPath = useRef(null);

//     useEffect(() => {
//         if (previousPath.current !== null && previousPath.current !== location2) {
//             check(location2)
//             // 🔥 Fire your logic here (this runs after first load)
//         }

//         previousPath.current = location2;
//     }, [location2]);

//     useEffect(() => {
//         api.get('/agorder/GetDeletedOrders').then((res) => {
//          setDeletedOrder(res.data)
//         })
//       check(location.href)
//     }, []);

// const fetchOutlets = () =>{
//     setOutlets(loadingList())
//     api.get('/admin/FindDIrectorOutlet').then((res) => {
//         var data =[{label:'Outlet',className:'no-hover'}].concat(res.data.map((item) => ({selected:true,label: <Media badge={item.Area==outletArea ? <i className={'fa text-primary fa-check'}></i> : '' } title={item.Area} description={item.Address} image={'/images/copy/o'+(item.Avtar==0?'1':item.Avtar)+'.svg'} />, value: item.ID,onClick:()=>{
//             setLoading(true)
//             api.get('/admin/Step2_VerifyOTP?Phone='+phone+'&redirectOutletId='+item.ID).then((_res) => {
//                 setupAccount(_res)
//                 window.location.reload()
//             })
//             }}))).concat([{type:'divider'},{icon:<i className={'fa fa-plus'}></i>,label:'Add new outlet',value:'add'}])
//         setOutlets(data);

//     })
// }

//   const pathname = window.location.pathname;
// const pagePath = window.location.pathname.slice(1).replace("/","/").split("/")[0].toLowerCase()
//     const navigate = useNavigate();

//     const rawCategory = location.pathname.split('/')[2];
//     const formattedCategory = rawCategory?.replace(/(?!^)([A-Z])/g, ' $1');
//     const [selectedCategory, setSelectedCategory] = useState(formattedCategory);

//   const productMenuItems = [{label:'Important',className: 'no-hover' },
//       { onClick:()=>navigate({ to: '/product/menu'}), label: <Media image={'/images/Group 3.svg'} description={'Manage restaurant catalog'} title={'Menu'} />
//   }]
//   const [hoverMenu, setHoverMenu] = useState(null)
//   const dropdownMenus = {
//     Product: [...productMenuItems],
//      // Reports: [...reportMenuItems],
//     // Employees: [...employeeMenuItems],
//     // Inventory: [...inventoryMenuItems],
//   };

//   const NavItem = ({item,blueBackgound}) => {
//     return <div onClick={()=>
//         {
//           setTimeout(()=>{
//             navigate({ to: '/reports/'+item.title.replace(' ','') });
//           },50)
//           setSelectedCategory(item.title)
//         }
//     }

//                 className={(selectedCategory?.toLowerCase() == item.title.toLowerCase() ? 'selected-category font-semibold ' : (blueBackgound ? '  hover:bg-primary/10 ' : '  hover:bg-gray-100 ')) + ' group transition-all relative flex-c active:outline active:outline-3 active:-outline-offset-2 active:outline-primary  p-2 py-2.5 rounded-lg '}>
//       {selectedCategory == item.title &&
//           <div className={'bg-primary rounded-r-full w-[4px] h-[16px] left-0 absolute'}></div>}

//       {(selectedCategory != item.title && !blueBackgound) &&
//           <div className={'group-hover:opacity-100 opacity-0 bg-gray-300 rounded-r w-[6px] h-[19px] left-0 absolute'}></div>}

//       {(selectedCategory != item.title && blueBackgound) &&
//           <div className={'group-hover:opacity-100 opacity-0 bg-primary/15 rounded-r w-[6px] h-[19px] left-0 absolute'}></div>}

//       <div className={'min-w-[32px] flex-c justify-center'}><span className={(blueBackgound?'-ml-1 ':'ml-0.5 ')+' text-[16px]'}>{item.icon}</span></div> <span className={'ml-1'}>{item.title}</span>{item.badge}</div>
//   }
//   const items = [
//     {
//       key: '1',
//       label: (
//           <a target="_blank" rel="noopener noreferrer" href="https://www.antgroup.com">
//             1st menu item
//           </a>
//       ),
//     },
//     {
//       key: '2',
//       label: (
//           <a target="_blank" rel="noopener noreferrer" href="https://www.aliyun.com">
//             2nd menu item (disabled)
//           </a>
//       ),
//       disabled: true,
//     },
//     {
//       key: '3',
//       label: (
//           <a target="_blank" rel="noopener noreferrer" href="https://www.luohanacademy.com">
//             3rd menu item (disabled)
//           </a>
//       ),
//       disabled: true,
//     },
//     {
//       key: '4',
//       danger: true,
//       label: 'a danger item',
//     },
//   ];

//   return (

//       <div className={'h-screen flex flex-col'}>

//           <Dialog width={800} footer={<></>} open={upgrade} setOpen={setUpgrade} >
//               <div className={'relative flex-c h-full'}>
//                   <div className={'bg-gray-50  h-[525px] center'}>
//                       <div>
//                           <img className={'w-full'} src={'/images/face-unlock-security.png'}/>
//                       </div>
//                   </div>

//                   <div className={'p-14 space-y-7 min-w-[60%] '}>
//                       <img className="w-45" src="/images/logo.svg"/>
//                       <h4>Download Business App</h4>
//                       <div className={'py-2'}>
//                           <p>Download the business app to review sales</p>

//                           <div className={'my-10 mb-0'}>
//                               <h6 className={'font-bold flex-c gap-1'}><Tag
//                                   className={'small !mx-0 !font-bold text-uppercase'}
//                                   color={'#5E4DB2FF'}>Step 1</Tag> Scan QR code</h6>
//                               <p className={'mt-2'}>Scan the QR code below or send link to mobile </p>
//                               <div className={' flex-c p-3 gap-5 bg-gray-200/50 mt-6 rounded-lg'}>
//                                   <img className="shadow rounded-lg  bg-white p-1.5 w-[140px]"
//                                        src="/images/Untitled 1 (63).png"/>
//                                   <div className={'space-y-1.5'}>
//                                       <h6 className={'font-bold'}>Can't scan QR code?</h6>
//                                       <p>Enter your mobile number to send link</p>
//                                       <div>
//                                           <Input size={'small'} placeholder={'Enter your mobile number'}/>
//                                       </div>
//                                       <div>
//                                           <Button>Send</Button>
//                                       </div>
//                                   </div>
//                               </div>
//                               <h6 className={'font-semibold text-slate-500 text-xs mt-13'}><i
//                                   className={'fa fa-circle-question'}></i> App secured by AES256</h6>

//                           </div>

//                       </div>
//                   </div>
//               </div>
//           </Dialog>

//           {!hideNavbars && !localStorage.getItem('hideAdsStudio') && (
//               <div
//                   className={'bg-gray-100/80  p-2.5 text-sm px-4 border-b border-gray-300 text-base flex items-center'}>
//                   {/*<Tag color="#1868db">New</Tag>*/}
//                   <span className={'mr-1.5'}> Introducing Ads Studio: AI-powered calls, WhatsApp marketing, and one-click brochure design with instant background removal.
//       </span> <span className={'link'}>Check it <i className={'fa fa-arrow-right'}></i></span>
//                   <Button onClick={() => {
//                       localStorage.setItem('hideAdsStudio', true)
//                       window.location.reload()
//                   }} color="default" variant="filled" className={'ml-10 !ms-auto'}>Close</Button>
//               </div>
//           )}

//           {hideNavbars==false && <div className={' min-h-[55px] px-3 flex-c border-b-2 gap-3 border-gray-300/40 px-5 pr-6'}>

//               <div
//                   className={'h-[33px] w-[33px] hover:bg-gray-200 cursor-default rounded-full transition-all flex-c justify-center'}>
//                   <i className={'fa fa-external-link text-lg'}></i>
//               </div>
//               <div className={'mx-2 -ml-1 h-[33px] cursor-default rounded-full transition-all flex-c justify-center'}>
//                   <img className={'w-35'} src={'/images/logo.svg'}/>
//               </div>

//               {['Product', 'Reports', 'Employees', 'Inventory'].map((item, index) => (
//                   <div
//                       className={'border-b-[3.5px] flex-c  h-full  ' + (pagePath == item.toLowerCase() ? 'border-primary text-primary' : 'border-transparent')}>
//                       <div
//                           className={(pagePath == item.toLowerCase() ? 'hover:bg-primary-light' : 'hover:bg-gray-100') + '  overflow-hidden cursor-default transition-all rounded-md flex-c h-[33px] mt-1'}>
//                           <div
//                               className={(pagePath == item.toLowerCase() ? ' hover:bg-primary-light' : ' hover:bg-gray-200') + ' transition-all p-2 px-0 px-1.5 py-0 h-[33px] flex-c font-semibold'}>{item}</div>
//                           <Dropdown onOpenChange={(open) => {
//                               if (open) {
//                                   setHoverMenu(item)
//                               } else {
//                                   setHoverMenu('nh')
//                               }
//                           }} trigger={['click']} menu={{style: {minWidth: '245px',left:'-63px'}, items: dropdownMenus[item]}}>
//                               <div
//                                   className={(pagePath == item?.toLowerCase() ? ' hover:bg-primary-light' : ' hover:bg-gray-200') + ' transition-all p-2 h-full px-1.5 py-0 h-[33px] border-l-2 flex-c border-white'}>
//                                   <ArrowDown/>
//                               </div>
//                           </Dropdown>

//                       </div>

//                   </div>
//               ))}

//               <div
//                   className={'bg-gray-100 hover:bg-gray-200 ml-2  cursor-default transition-all rounded-md flex-c gap-2 h-[33px]'}>
//                   <div className={'p-2 font-semibold px-3'}>Help</div>
//               </div>

//               <div
//                   className={'bg-primary hover:bg-primary-hover text-white   cursor-default transition-all rounded-md flex-c gap-2 h-[33px]'}>
//                   <div className={'p-2 px-3 font-semibold flex-c gap-1.5'}>Apps <ArrowDown/></div>
//               </div>

//               <div className={'ms-auto flex-c gap-3'}>
//                   <Dropdown overlayClassName={'w-[310px]'} trigger={['click']} placement={'topRight'}
//                             menu={{items: outlets}}>
//                       <Button onClick={() => fetchOutlets()} color="primary" className={'!px-3 '} variant="filled"> <i
//                           className={'fa fa-earth -ml-2.5 -mr-0.5 '}/> <span
//                           className={'truncate max-w-[80px] capitalize'}>{outletArea?.toLowerCase()} </span><ArrowDown/>
//                       </Button>
//                   </Dropdown>

//                   <div
//                       className={'h-[33px] w-[33px] hover:bg-gray-200 cursor-default rounded-full transition-all flex-c justify-center'}>
//                       <i className={'far text-xl fa-bell'}/>
//                   </div>

//                   <Dropdown menu={{
//                       items: [
//                           {label: 'ACCOUNT', className: 'no-hover'},
//                           {
//                               className: 'no-hover', label: <div className={'flex-c -mt-2 gap-2'}>
//                                   <img
//                                       className={'w-11 hover:bg-gray-300 transition-all cursor-pointer rounded-full p-[4px]'}
//                                       src={'/images/default-avatar (1).png'}/>
//                                   <div>
//                                       <h6 className={''}>Jigar Prajapati <i
//                                           className={'fa fa-check-circle text-primary'}></i></h6>
//                                       <p className={'!text-xxs mt-0.5 text-muted'}>+91 9499 7670 14</p>
//                                   </div>
//                                   <Tag className={'small !ml-4 ms-auto'} bordered={false} color={'purple'}>PRO</Tag>
//                               </div>
//                           },
//                           {label: 'Settings', icon: <i className={'far fa-wrench'}></i>},
//                           {type: 'divider'},
//                           {label: 'Integrations', icon: <i className={'far fa-puzzle-piece'}></i>},
//                           {label: 'Referral bonus', icon: <i className={'far fa-handshake-simple'}></i>},
//                           {type: 'divider'},
//                           {label: 'SUPPORT', className: 'no-hover'},
//                           {label: 'Guide', icon: <i className={'far fa-book'}></i>},
//                           {label: 'Help center', icon: <i className={'far fa-circle-question'}></i>},
//                           {
//                               label: <span className={'flex-c'}>Video tutorials <Tag color={'purple'} bordered={false}
//                                                                                      className={'!ml-9 small'}>BETA</Tag></span>,
//                               icon: <i className={'far fa-clapperboard-play'}></i>
//                           },

//                           {type: 'divider'},
//                           {
//                               label: 'Log out', icon: <i className={'far fa-right-from-bracket'}></i>, onClick: () => {
//                                   localStorage.removeItem('jwt')
//                                   navigate({ to: '/login', search: { t: Date.now() } });
//                               }
//                           }
//                       ]
//                   }} trigger={['click']}>
//                       <img className={'w-9 hover:bg-gray-300 transition-all cursor-pointer rounded-full p-[4px]'}
//                            src={'/images/default-avatar (1).png'}/>
//                   </Dropdown>
//                   {/*<div*/}
//                   {/*    className={'bg-primary-light hover:bg-gray-300  cursor-default transition-all rounded-md flex-c gap-2 h-[33px]'}>*/}
//                   {/*    <div className={'p-2 px-3 flex-c gap-1.5'}><FontAwesomeIcon*/}
//                   {/*        icon={faMobileAndroidAlt}/> Get mobile app</div>*/}
//                   {/*</div>*/}
//               </div>
//           </div>}
//           <div className={'flex-c flex-1'}>
//               {!hideSideBar && (!hideNavbars && <div
//                   className={'border-r-3  flex flex-col  max-w-[245px] min-w-[245px] border-gray-200/60 h-full bg-gray-50/30'}>
//               <div className={'p-3 py-6 pb-8 '}>
//                   <Tooltip overlayInnerStyle={{marginLeft: 10}} placement={'topLeft'} title={outletAddress}>

//                       <div className={'flex items-start  px-4 mt-2 mb-0'}>
//                           <img className={'w-8 mt-1 mr-3 rounded'}
//                                src={'/images/copy/o' + (outletAvtar == 0 ? '1' : outletAvtar) + '.svg'}/>

//                           <div className={'w-full'}>
//                               <h6 className={'font-semibold capitalize'}>{outletName?.toLowerCase()}</h6>
//                               <p className={'-mt-0.5 text-sm truncate overflow-hidden whitespace-nowrap w-[80%] capitalize'}>{outletCity?.toLowerCase()}</p>
//                           </div>
//                       </div>
//                   </Tooltip>

//               </div>
//               <div className={'relative flex-1'}>
//                   <SimpleBar
//                       style={{
//                           maxHeight: '100%',
//                           position: 'absolute', // <- Absolutely positioned
//                           inset: 0, // top: 0; right: 0; bottom: 0; left: 0;
//                       }}
//                       autoHide={true} // optional
//                   >
//                       <div className={'p-3 py-6 pt-0'}>

//                           <h6 style={{fontWeight: 600}} className={'text-[12px] px-4.5'}>PLANNING</h6>
//                           <div
//                               className={'bg-[var(--color-primary-light)]/80 p-1.5 px-2 mt-3 rounded-xl w-full mb-2'}>
//                               <div className={'px-2.5'}>
//                                   <h6 className={'text-primary px-0.5 font-semibold my-2'}>CMS EA Scrum</h6>
//                               </div>
//                               <div className={'space-y-0'}>
//                                   {[
//                                       {
//                                           title: 'Dashboard',
//                                           icon: <i className={'far fa-tachometer-alt'}></i>
//                                       },
//                                       {
//                                           title: 'Day Summary',
//                                           icon: <i className={'  fa fa-sun-dust'}></i>
//                                       },
//                                       {
//                                           title: 'Orders',
//                                           icon: <i className={' fa fa-list'}></i>
//                                       },
//                                       {
//                                           title: 'Monthly Sale',
//                                           icon: <i className={'  far fa-calendar-star'}></i>
//                                       },
//                                       {
//                                           title: 'Items',
//                                           icon: <i className={' far fa-salad'}></i>
//                                       }].map((item, index) => (
//                                       <NavItem blueBackgound={true} item={item}/>
//                                   ))}
//                               </div>

//                           </div>
//                           <div className={'px-1.5 space-y-1'}>
//                               {[
//                                   {
//                                       title: 'Sales Board',
//                                       icon: <i className={' far fa-calendar-lines-pen'}></i>
//                                   },].map((item, index) => (
//                                   <NavItem item={item}/>
//                               ))}
//                           </div>
//                           <h6 className={'text-sm font-bold my-4 px-4.5'}>IMPORTANT</h6>

//                           <div className={'px-1.5 space-y-1'}>

//                               {[{
//                                   title: 'Deleted Bill',
//                                   icon: <i className={' far fa-trash-list'}></i>,
//                                   badge: (deletedOrder && <Tag color={'purple'} bordered={false}
//                                                                className={'!ms-auto !rounded-full small'}>{deletedOrder}</Tag>)
//                               },
//                                   {
//                                       title: 'Due Payment',
//                                       icon: <i className={'  fa fa-clock-rotate-left'}></i>
//                                   }].map((item, index) => (
//                                   <NavItem item={item}/>
//                               ))}
//                           </div>

//                       </div>

//                   </SimpleBar>
//               </div>

//               <div className={'p-6 pt-1 pb-3'}>
//                   <div className={'w-full pt-4  border-t-2  border-gray-300/70'}></div>
//                   {/*<Button onClick={()=>setUpgrade(true)} variant={'filled'} color={'default'} className={'w-full'}>Upgrade to Pro</Button>*/}

//                   <h6 className={'w-full text-center text-xs mt-3 text-slate-400 font-semibold'}>© 2025 Jetgorilla
//                       INC.</h6>
//               </div>
//           </div>)}

//               <div className={'h-full flex-1 relative'}>
//                   <SimpleBar
//                       style={{
//                           maxHeight: '100%',
//                           height: '1000%',
//                           position: 'absolute', // <- Absolutely positioned
//                           inset: 0, // top: 0; right: 0; bottom: 0; left: 0;
//                       }}
//                   >
//                       <div className={hideNavbars ? '' : 'p-12 py-9 pr-7 w-full h-full flex flex-col'}>
//                           <Outlet/>
//                           {loading && <Loading/>}
//                       </div>
//                   </SimpleBar>
//               </div>

//           </div>
//       </div>

//   )
//       ;
// };

const RootLayout = () => {
  return <Outlet />;
};

// ✅ Create the root route with the component
export const Route = createRootRoute({
  component: () => <RootLayout />,
});
