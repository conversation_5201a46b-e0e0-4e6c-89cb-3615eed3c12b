import { motion } from "framer-motion";

const VideoSection = () => {
  return (
    <section className="relative py-20 bg-gradient-to-b from-gray-100 to-white overflow-hidden">
      {/* Background Video - Full Screen */}
      <div className=" z-0">
        {/* <div className="absolute inset-0 bg-white/70"></div> */}
      </div>

      <div className="relative z-10 mx-auto px-8">
        {/* Background Video - Full Screen */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-screen h-screen object-cover"
        >
          <source src="/videos/waves.mp4" type="video/mp4" />
        </video>

        {/* Heading and Description - Positioned over video */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          viewport={{ once: true }}
          className="absolute top-0 left-18 text-left  z-20 ml-[10%]"
        >
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="!text-4xl md:!text-5xl lg:!text-7xl !font-bold text-gray-900 mb-6 leading-tight"
          >
            Brand new era
            <br />
            for restaurant.
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-lg text-gray-600 leading-relaxed max-w-lg"
          >
            Create customized virtual office spaces for any department or
            event with high-quality audio and video conferencing.
          </motion.p>
        </motion.div>
        {/* Video Content Area */}
        <motion.div
          initial={{ opacity: 0, y: 80, scale: 0.9 }}
          whileInView={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 1.2, delay: 0.6, ease: "easeOut" }}
          viewport={{ once: true }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 ml-16 -mt-4 -translate-y-1/2 z-20"
        >
          {/* Main Video Container */}
          <div className="relative  rounded-3xl shadow-2xl overflow-hidden  border-gray-200">
            {/* Video Meeting Interface */}
            <div className="relative aspect-video">
              <video
                autoPlay
                loop
                muted
                playsInline
                className="w-[640px] m-3 rounded-2xl h-full object-cover"
              >
                <source
                  src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                  type="video/mp4"
                />
              </video>

              {/* Video Meeting Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent">
                {/* Meeting Title */}
                <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
                  Onboarding Meeting
                </div>

                {/* Participant Grid Overlay */}
                <div className="absolute top-4 right-4 grid grid-cols-1 gap-2">
                  <div className="w-24 h-16 bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face&auto=format"
                      alt="Participant 1"
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <div className="w-24 h-16 bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format"
                      alt="Participant 3"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Meeting Controls */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-3">
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    🎤
                  </button>
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    📹
                  </button>
                  <button className="w-12 h-12 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center text-white transition-colors">
                    ❌
                  </button>
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    💬
                  </button>
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    📱
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Description */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            viewport={{ once: true }}
            className="absolute -bottom-40 left-1/2  transform -translate-x-1/2 text-left w-3xl"
          >
            <p className="!text-2xl !font-semibold !text-neutral-800 leading-relaxed">
              Collaborating with remote teams is easy in your virtual office
              environment. Enjoy real-time communication within your workspace
              without additional software hassle.
            </p>
          </motion.div>
        </motion.div>

        {/* Feature Icons Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          viewport={{ once: true }}
          className="absolute -bottom-28 left-1/2 transform -translate-x-1/2 z-20 w-4xl"
        >
          <div className="flex items-center justify-center space-x-16">
            {/* Customize workspace */}
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-300 rounded-lg flex items-center justify-center mb-3 mx-auto">
                <span className="text-white text-xl">🏢</span>
              </div>
              <h4 className="font-semibold !text-3xl text-gray-900  mb-1">Customize
            workspace</h4>
              <p className="text-xs text-gray-600 max-w-24 leading-tight">
                Create customized virtual meeting rooms to suit your team's needs.
              </p>
            </div>

            {/* Audio and video calls */}
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-3 mx-auto">
                <span className="text-white text-xl">📹</span>
              </div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Audio and</h4>
              <h4 className="font-semibold text-gray-900 text-sm mb-2">video calls</h4>
              <p className="text-xs text-gray-600 max-w-24 leading-tight">
                Connect seamlessly with high-quality virtual conferencing.
              </p>
            </div>

            {/* Invite guests */}
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-3 mx-auto">
                <span className="text-white text-xl">👥</span>
              </div>
              <h4 className="font-semibold text-gray-900 text-sm mb-1">Invite</h4>
              <h4 className="font-semibold text-gray-900 text-sm mb-2">guests</h4>
              <p className="text-xs text-gray-600 max-w-24 leading-tight">
                Easily invite external participants to share your workspace.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default VideoSection;
