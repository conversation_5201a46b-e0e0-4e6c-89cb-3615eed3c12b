import { motion } from "framer-motion";

const VideoSection = () => {
  return (
    <section className="relative py-20 bg-gradient-to-b from-gray-100 to-white overflow-hidden">
      {/* Background Video - Full Screen */}
      <div className=" z-0">
        {/* <div className="absolute inset-0 bg-white/70"></div> */}
      </div>

      <div className="relative z-10  mx-auto px-8">
        {/* Heading and Description */}

        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-screen h-screen  object-cover "
        >
          <source src="/videos/waves.mp4" type="video/mp4" />
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            viewport={{ once: true }}
            className=" absolute top-0 left-0 text-left max-w-2xl mb-20 mt-16"
          >
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight"
            >
              Work together.
              <br />
              Like in the office.
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-lg text-gray-600 leading-relaxed max-w-lg"
            >
              Create customized virtual office spaces for any department or
              event with high-quality audio and video conferencing.
            </motion.p>
          </motion.div>
        </video>
        {/* Video Content Area */}
        <motion.div
          initial={{ opacity: 0, y: 80, scale: 0.9 }}
          whileInView={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 1.2, delay: 0.6, ease: "easeOut" }}
          viewport={{ once: true }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -mt-4 ml-18"
        >
          {/* Main Video Container */}
          <div className="relative  rounded-3xl shadow-2xl overflow-hidden  border-gray-200">
            {/* Video Meeting Interface */}
            <div className="relative aspect-video">
              <video
                autoPlay
                loop
                muted
                playsInline
                className="w-[640px] m-3 rounded-2xl h-full object-cover"
              >
                <source
                  src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                  type="video/mp4"
                />
              </video>

              {/* Video Meeting Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent">
                {/* Meeting Title */}
                <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
                  Onboarding Meeting
                </div>

                {/* Participant Grid Overlay */}
                <div className="absolute top-4 right-4 grid grid-cols-1 gap-2">
                  <div className="w-24 h-16 bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face&auto=format"
                      alt="Participant 1"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="w-24 h-16 bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face&auto=format"
                      alt="Participant 2"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="w-24 h-16 bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format"
                      alt="Participant 3"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Meeting Controls */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-3">
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    🎤
                  </button>
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    📹
                  </button>
                  <button className="w-12 h-12 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center text-white transition-colors">
                    ❌
                  </button>
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    💬
                  </button>
                  <button className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                    📱
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Description */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            viewport={{ once: true }}
            className="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-center max-w-2xl"
          >
            <p className="text-base text-gray-600 leading-relaxed">
              Collaborating with remote teams is easy in your virtual office
              environment. Enjoy real-time communication within your workspace
              without additional software hassle.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default VideoSection;
