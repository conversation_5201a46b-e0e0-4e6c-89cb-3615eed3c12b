import {Button, Dropdown} from "antd";
import {ArrowDown} from "../../../utils/util.jsx";

const WidgetHeaderButton = () => {
    return (
        <div className={'ms-auto flex-c gap-1'}>

            <Dropdown>
                <Button size={'small'} type={'text'}>This year <ArrowDown/></Button>
            </Dropdown>

            <Button
                size={'small'}
                type={'text'}
                icon={<i className={'fa fa-refresh'}></i>}
            />
            <Button
                size={'small'}
                type={'text'}
                icon={<i className={'fa fa-ellipsis'}></i>}
            />

        </div>
    );
};

export default WidgetHeaderButton;