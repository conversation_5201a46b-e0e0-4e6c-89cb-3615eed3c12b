// HalfPieChart.jsx
import React, {useEffect, useState} from "react";
import {<PERSON><PERSON>, Dropdown, Ta<PERSON>, Tooltip} from "antd";
import {ArrowDown, Money} from "../../../utils/util.jsx";
import {Pie} from "@ant-design/charts";
import moment from "moment";
import api from "../../../utils/api.js";
import {orderBy} from "lodash";

const StatasticWidget = () => {
    const [data,setData] = useState([])
    const [length,setLength] = useState(15)
    const [dateRange, setDateRange] = useState([
        moment().startOf('month').toDate(), // first day of month
        moment().endOf('month').toDate(),   // last day of month
    ]);
    const [selectedFilter,setSelectedFilter] = useState('This month')
    useEffect(()=>{
        api.get('/dashboard/GetStatastics?from='+dateRange[0].toISOString()+'&to='+dateRange[1].toISOString()).then(res => {
            setData(res.data)
        });
    },[])
    const config = {
        data:data.allSaleByEachWeekName,
        "scale": {
            "color": {
                "type": "identity"
            }
        },
        angleField: 'value',
        // onReady: ({chart}) => {
        //
        //     // Wait a short delay to ensure chart is rendered
        //     setTimeout(() => {
        //         const scale = chart.getScale().color;
        //         const { domain } = scale.getOptions();
        //         const items = domain.map(() => {});
        //     }, 100); // adjust delay if needed
        // },
        colorField: 'color',
        paddingBottom: 0,
        startAngle: Math.PI,
        endAngle: Math.PI * 2,
        pieStyle: {
            lineWidth: 2,           // gap width
            stroke: '#fff',         // gap color (usually white)
            radius: 1,              // keep full radius
            cornerRadius: 8,        // rounded corners
        },

        innerRadius: 0.6,
      label: {
          text: (datum) => moment().day(datum.type).format('ddd'),
          style: {
              fontWeight: 'bold',
          },
      },
        legend: false

    };

    return (
        <div
            className={'space-y-4    relative items-stretch text-card-foreground rounded-xl card border border-border shadow-xs black/5 h-full'}>
            <h6 className={'card-header !py-0'}>
                <span className={'font-bold py-3.5'}>Statistics</span>
                <div className={'ml-6 flex-c !font-normal justify-center w-full gap-1'}>
                    {/*<Tabs rootClassName={'no-margin'} items={[*/}
                    {/*    {*/}
                    {/*        key: '1',*/}
                    {/*        label: 'Calendar',*/}
                    {/*    },*/}
                    {/*    {*/}
                    {/*        key: '2',*/}
                    {/*        label: 'Pivot',*/}
                    {/*    }]}></Tabs>*/}

                </div>

                <div className={'ms-auto flex-c gap-1'}>
                    <Dropdown overlayClassName={'selectable'} trigger={['click']} menu={{
                        defaultSelectedKeys: [selectedFilter],
                        selectedKeys: [selectedFilter],
                        selectable: true,
                        items: [{label: 'This month'}, {label: 'Last month'}]
                    }}>
                        <Button size={'small'} type={'text'}>This Month <ArrowDown/></Button>

                    </Dropdown>
                    <Button size={'small'} type={'text'}
                            icon={<i className={'fa fa-refresh'}></i>}></Button></div>
            </h6>
            <div className={'flex items-start'}>
                <div className={'p-7 min-w-[350px]'}>
                    <div className={'relative'}>
                        <div className={'-mb-75 -mt-30'}>
                            <div className={''}><Pie {...config} /></div>
                        </div>
                    </div>
                    <div className={'px-4'}>
                        <div className={'space-y-3 !mt-6'}>
                            <h6 className={'!my-3 text-center'}>Top weeks</h6>
                           {orderBy(data?.allSaleByEachWeekName, ['value'], ['desc'])
                               ?.slice(0, 3)
                               ?.map((item, index) => (
                                   <div className={'flex-c bg-gray-50 rounded p-1 px-2'} key={index}>
                                       <h6 className={'!font-normal text-slate-500  gap-2.5 flex-c'}>
                                           <i style={{color:item.color}} className={'opacity-80 text-xxs fa fa-circle'}></i> {moment().day(item.type).format('dddd')}
                                       </h6>
                                       <div className={'flex-c gap-7.5 ms-auto'}>
                                           <h6 className={'!font-bold '}>{Money(item.value)}</h6>
                                           <h6 className={'gap-1.5 flex-c min-w-[40px]'}>
                                               <i className={'text-sm text-green fa fa-arrow-up'}></i>{item.percentage.toFixed(1)}
                                           </h6>
                                       </div>
                                   </div>
                           ))}
                        </div>
                    </div>

                </div>
                <div className={'flex p-7.5 w-full flex-wrap gap-2'}>
                    <div className={'flex-c gap-5 mb-5'}>
                        <p className={'flex-c gap-2'}><div className={'w-6 rounded h-4 bg-[var(--color-violet))]'}></div> Good</p>
                        <p className={'flex-c gap-2'}><div className={'w-6 rounded h-4 bg-[var(--color-violet))]/70'}></div> Normal</p>
                        <p className={'flex-c gap-2'}><div className={'w-6 rounded h-4 bg-[var(--color-violet))]/40'}></div> Sad</p>
                    </div>
                    <div className={'flex w-full flex-wrap gap-2'}>
                        {orderBy(data?.calenderSale, ['value'], ['desc'])
                            ?.slice(0, length)
                            ?.map((item, i) => (
                                <Tooltip title={item.type + ' | ' + Money(item.value)} key={i} placement={'top'}
                                         overlayClassName={'text-xs'}>
                                    <div style={{background: item.heatmap, color: item.textColor}}
                                         className={'w-14 center h-12 rounded-lg '}>{i + 1}</div>
                                </Tooltip>
                            ))}
                        {length == 15 && (
                            <div className={'w-full  mt-3'}>
                                <Button size={'small'} type={'text'} onClick={() => setLength(35)}>
                                    Show More
                                </Button>
                            </div>
                        )}
                    </div>

                </div>
            </div>
        </div>
    );
};

export default StatasticWidget;
