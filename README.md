# React Admin New

A modern React 19 application built with Tailwind CSS 4, Ant Design, and TanStack Router.

## Features

- **React 19** - Latest React with improved performance and features
- **Tailwind CSS 4** - Utility-first CSS framework for rapid UI development
- **Ant Design** - Enterprise-class UI design language and components
- **TanStack Router** - Type-safe file-based routing
- **Vite** - Fast build tool and development server
- **ESLint** - Code linting and quality assurance

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm, yarn, or pnpm

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run preview` - Preview the production build
- `npm run lint` - Run ESLint to check code quality

## Project Structure

```
src/
├── routes/           # TanStack Router file-based routes
│   ├── __root.jsx   # Root layout component
│   ├── index.jsx    # Home page
│   └── about.jsx    # About page
├── main.jsx         # Application entry point
└── index.css        # Global styles with Tailwind imports
```

## Routing

This project uses TanStack Router with file-based routing. Routes are automatically generated based on the file structure in the `src/routes` directory.

- `/` - Home page (`src/routes/index.jsx`)
- `/about` - About page (`src/routes/about.jsx`)

## Styling

The project uses Tailwind CSS 4 for utility-first styling combined with Ant Design components for a professional UI experience.

## License

This project is private and not licensed for public use.
