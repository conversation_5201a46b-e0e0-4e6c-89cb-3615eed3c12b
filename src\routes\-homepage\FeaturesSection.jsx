import { motion } from "framer-motion";
import CircularGallery from "./CircularGallery";
  const f1 = [
    {
      icon: <img src={'/images/home/<USER>'} alt="Customize workspace" className="w-12 h-12" />,
      title: "Customize workspace",
      description: "Create your own offices and meeting rooms to suit your team's needs.",
      bgColor: "bg-blue-400"
    },
    {
      icon: <img src={'/images/home/<USER>'} alt="Audio and video calls" className="w-12 h-12" />,
      title: "Audio and video calls", 
      description: "Collaborate efficiently and seamlessly with high quality virtual conferencing.",
      bgColor: "bg-blue-500"
    },
    {
      icon: <img src={'/images/home/<USER>'} alt="Invite guests" className="w-12 h-12" />,
      title: "Invite guests",
      description: "Meet with guests without ever needing to leave your workspace.",
      bgColor: "bg-blue-400"
    }
  ];
const FeaturesSection = () => {
  const features = [
    {
      id: 1,
      title: "Pay global suppliers & staff with ease",
      description: "Convert from GBP at point of payment, or hold funds.",
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=300&fit=crop&auto=format",
      gradient: "from-gray-900 to-gray-700"
    },
    {
      id: 2,
      title: "Unlock discounts with suppliers",
      description: "Access instant Payouts on outstanding invoices.",
      image: "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=300&fit=crop&auto=format",
      gradient: "from-red-500 to-orange-400"
    },
    {
      id: 3,
      title: "Uplift and incrementality measurement",
      description: "Pay suppliers in any currency effortlessly.",
      image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&auto=format",
      gradient: "from-amber-400 to-yellow-300"
    },
    {
      id: 4,
      title: "Pay later with credit line",
      description: "Pay your vendors up to 60 days later, with credit.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&auto=format",
      gradient: "from-blue-600 to-purple-600"
    }
  ];

  return (
    <section className="relative pb-20 bg-gradient-to-b from-gray-100 to-white -mt-40 z-10">
        <div className="flex items-center w-full mx-auto py-10 justify-center space-x-16 max-w-5xl mb-20">
        {f1.map((feature, index) => (
          <div key={index} className="text-center max-w-xs">
            {/* Icon */}
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 mx-auto shadow-lg`}>
              {feature.icon}
            </div>
            
            {/* Title */}
            <h3 className="font-semibold text-gray-900 text-lg mb-3 leading-tight max-w-40 text-center mx-auto">
              {feature.title}
            </h3>
            
            {/* Description */}
            <p className="text-sm text-gray-600 leading-relaxed">
              {feature.description}
            </p>
          </div>
        ))}
      </div>

      <div className=" mx-auto px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-left mb-16"
        >
          {/* Features Label */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="flex items-center mb-6"
          >
            <span className="text-sm font-semibold text-blue-600 uppercase tracking-wider">
              FEATURES
            </span>
            <div className="ml-4">
              <div className="w-16 h-12 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center transform rotate-12">
                <span className="text-white text-xl">💻</span>
              </div>
            </div>
          </motion.div>

          {/* Main Heading */}
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-4"
          >
            <span className="text-gray-400">More advanced capabilities.</span>
            <br />
            <span className="text-gray-900">Maximize your AI potential.</span>
          </motion.h2>
        </motion.div>

        {/* Circular Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="w-full h-96"
        >
          <CircularGallery
            items={features.map(feature => ({
              image: feature.image,
              text: feature.title
            }))}
            bend={2}
            textColor="#374151"
            borderRadius={0.1}
            font="bold 24px Figtree"
            scrollSpeed={1.5}
            scrollEase={0.08}
          />
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturesSection;
