import { createFileRoute } from '@tanstack/react-router'
import { Card, Typography, Descriptions, Tag, Space } from 'antd'
import { InfoCircleOutlined, CodeOutlined, ToolOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

export const Route = createFileRoute('/about')({
  component: About,
})

function About() {
  return (
    <div className="max-w-4xl mx-auto">
      <Card className="mb-6">
        <div className="text-center py-8">
          <InfoCircleOutlined className="text-6xl text-green-500 mb-4" />
          <Title level={1} className="mb-4">
            About This Project
          </Title>
          <Paragraph className="text-lg text-gray-600">
            Learn more about the technologies and architecture used in this application
          </Paragraph>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title={<><CodeOutlined className="mr-2" />Tech Stack</>}>
          <Space direction="vertical" className="w-full">
            <div>
              <Tag color="blue">React 19</Tag>
              <span>Latest React with improved performance</span>
            </div>
            <div>
              <Tag color="cyan">Tailwind CSS 4</Tag>
              <span>Utility-first CSS framework</span>
            </div>
            <div>
              <Tag color="purple">Ant Design</Tag>
              <span>Enterprise-class UI design language</span>
            </div>
            <div>
              <Tag color="orange">TanStack Router</Tag>
              <span>Type-safe file-based routing</span>
            </div>
            <div>
              <Tag color="green">Vite</Tag>
              <span>Fast build tool and dev server</span>
            </div>
          </Space>
        </Card>

        <Card title={<><ToolOutlined className="mr-2" />Features</>}>
          <Descriptions column={1} size="small">
            <Descriptions.Item label="Routing">
              File-based routing with TanStack Router
            </Descriptions.Item>
            <Descriptions.Item label="Styling">
              Tailwind CSS 4 with Ant Design components
            </Descriptions.Item>
            <Descriptions.Item label="Build Tool">
              Vite for fast development and building
            </Descriptions.Item>
            <Descriptions.Item label="Code Quality">
              ESLint for code linting and quality
            </Descriptions.Item>
            <Descriptions.Item label="Development">
              Hot reload and fast refresh
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>

      <Card className="mt-6" title="Project Structure">
        <Paragraph>
          This project follows a clean and organized structure:
        </Paragraph>
        <ul className="list-disc list-inside space-y-2 text-gray-700">
          <li><code className="bg-gray-100 px-2 py-1 rounded">/src/routes</code> - File-based routing with TanStack Router</li>
          <li><code className="bg-gray-100 px-2 py-1 rounded">/src/components</code> - Reusable React components</li>
          <li><code className="bg-gray-100 px-2 py-1 rounded">/src/styles</code> - CSS and styling files</li>
          <li><code className="bg-gray-100 px-2 py-1 rounded">tailwind.config.js</code> - Tailwind CSS configuration</li>
          <li><code className="bg-gray-100 px-2 py-1 rounded">vite.config.js</code> - Vite build configuration</li>
        </ul>
      </Card>
    </div>
  )
}
