/*!
 * Font Awesome Pro 6.6.0 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:root, :host {
  --fa-style-family-sharp-duotone: 'Font Awesome 6 Sharp Duotone';
  --fa-font-sharp-duotone-solid: normal 900 1em/1 'Font Awesome 6 Sharp Duotone'; }

@font-face {
  font-family: 'Font Awesome 6 Sharp Duotone';
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("../webfonts/fa-sharp-duotone-solid-900.woff2") format("woff2"), url("../webfonts/fa-sharp-duotone-solid-900.ttf") format("truetype"); }

.fasds,
.fa-sharp-duotone,
.fa-sharp-duotone.fa-solid {
  position: relative;
  font-weight: 900;
  letter-spacing: normal; }

.fasds::before,
.fa-sharp-duotone::before,
.fa-sharp-duotone.fa-solid::before {
  position: absolute;
  color: var(--fa-primary-color, inherit);
  opacity: var(--fa-primary-opacity, 1); }

.fasds::after,
.fa-sharp-duotone::after,
.fa-sharp-duotone.fa-solid::after {
  color: var(--fa-secondary-color, inherit);
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fasds::before,
.fa-swap-opacity .fa-sharp-duotone::before,
.fa-swap-opacity .fa-sharp-duotone.fa-solid::before,
.fasds.fa-swap-opacity::before,
.fa-sharp-duotone.fa-swap-opacity::before,
.fa-sharp-duotone.fa-solid.fa-swap-opacity::before {
  opacity: var(--fa-secondary-opacity, 0.4); }

.fa-swap-opacity .fasds::after,
.fa-swap-opacity .fa-sharp-duotone::after,
.fa-swap-opacity .fa-sharp-duotone.fa-solid::after,
.fasds.fa-swap-opacity::after,
.fa-sharp-duotone.fa-swap-opacity::after,
.fa-sharp-duotone.fa-solid.fa-swap-opacity::after {
  opacity: var(--fa-primary-opacity, 1); }

.fasds.fa-inverse,
.fa-sharp-duotone.fa-inverse,
.fa-sharp-duotone.fa-solid.fa-inverse {
  color: var(--fa-inverse, #fff); }

.fasds.fa-stack-1x,
.fasds.fa-stack-2x,
.fa-sharp-duotone.fa-stack-1x,
.fa-sharp-duotone.fa-solid.fa-stack-1x,
.fa-sharp-duotone.fa-stack-2x,
.fa-sharp-duotone.fa-solid.fa-stack-2x {
  position: absolute; }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen readers do not read off random characters that represent icons */

.fasds.fa-0::after, .fa-sharp-duotone.fa-0::after {
  content: "\30\30"; }

.fasds.fa-1::after, .fa-sharp-duotone.fa-1::after {
  content: "\31\31"; }

.fasds.fa-2::after, .fa-sharp-duotone.fa-2::after {
  content: "\32\32"; }

.fasds.fa-3::after, .fa-sharp-duotone.fa-3::after {
  content: "\33\33"; }

.fasds.fa-4::after, .fa-sharp-duotone.fa-4::after {
  content: "\34\34"; }

.fasds.fa-5::after, .fa-sharp-duotone.fa-5::after {
  content: "\35\35"; }

.fasds.fa-6::after, .fa-sharp-duotone.fa-6::after {
  content: "\36\36"; }

.fasds.fa-7::after, .fa-sharp-duotone.fa-7::after {
  content: "\37\37"; }

.fasds.fa-8::after, .fa-sharp-duotone.fa-8::after {
  content: "\38\38"; }

.fasds.fa-9::after, .fa-sharp-duotone.fa-9::after {
  content: "\39\39"; }

.fasds.fa-fill-drip::after, .fa-sharp-duotone.fa-fill-drip::after {
  content: "\f576\f576"; }

.fasds.fa-arrows-to-circle::after, .fa-sharp-duotone.fa-arrows-to-circle::after {
  content: "\e4bd\e4bd"; }

.fasds.fa-circle-chevron-right::after, .fa-sharp-duotone.fa-circle-chevron-right::after {
  content: "\f138\f138"; }

.fasds.fa-chevron-circle-right::after, .fa-sharp-duotone.fa-chevron-circle-right::after {
  content: "\f138\f138"; }

.fasds.fa-wagon-covered::after, .fa-sharp-duotone.fa-wagon-covered::after {
  content: "\f8ee\f8ee"; }

.fasds.fa-line-height::after, .fa-sharp-duotone.fa-line-height::after {
  content: "\f871\f871"; }

.fasds.fa-bagel::after, .fa-sharp-duotone.fa-bagel::after {
  content: "\e3d7\e3d7"; }

.fasds.fa-transporter-7::after, .fa-sharp-duotone.fa-transporter-7::after {
  content: "\e2a8\e2a8"; }

.fasds.fa-at::after, .fa-sharp-duotone.fa-at::after {
  content: "\40\40"; }

.fasds.fa-rectangles-mixed::after, .fa-sharp-duotone.fa-rectangles-mixed::after {
  content: "\e323\e323"; }

.fasds.fa-phone-arrow-up-right::after, .fa-sharp-duotone.fa-phone-arrow-up-right::after {
  content: "\e224\e224"; }

.fasds.fa-phone-arrow-up::after, .fa-sharp-duotone.fa-phone-arrow-up::after {
  content: "\e224\e224"; }

.fasds.fa-phone-outgoing::after, .fa-sharp-duotone.fa-phone-outgoing::after {
  content: "\e224\e224"; }

.fasds.fa-trash-can::after, .fa-sharp-duotone.fa-trash-can::after {
  content: "\f2ed\f2ed"; }

.fasds.fa-trash-alt::after, .fa-sharp-duotone.fa-trash-alt::after {
  content: "\f2ed\f2ed"; }

.fasds.fa-circle-l::after, .fa-sharp-duotone.fa-circle-l::after {
  content: "\e114\e114"; }

.fasds.fa-head-side-goggles::after, .fa-sharp-duotone.fa-head-side-goggles::after {
  content: "\f6ea\f6ea"; }

.fasds.fa-head-vr::after, .fa-sharp-duotone.fa-head-vr::after {
  content: "\f6ea\f6ea"; }

.fasds.fa-text-height::after, .fa-sharp-duotone.fa-text-height::after {
  content: "\f034\f034"; }

.fasds.fa-user-xmark::after, .fa-sharp-duotone.fa-user-xmark::after {
  content: "\f235\f235"; }

.fasds.fa-user-times::after, .fa-sharp-duotone.fa-user-times::after {
  content: "\f235\f235"; }

.fasds.fa-face-hand-yawn::after, .fa-sharp-duotone.fa-face-hand-yawn::after {
  content: "\e379\e379"; }

.fasds.fa-gauge-simple-min::after, .fa-sharp-duotone.fa-gauge-simple-min::after {
  content: "\f62d\f62d"; }

.fasds.fa-tachometer-slowest::after, .fa-sharp-duotone.fa-tachometer-slowest::after {
  content: "\f62d\f62d"; }

.fasds.fa-stethoscope::after, .fa-sharp-duotone.fa-stethoscope::after {
  content: "\f0f1\f0f1"; }

.fasds.fa-coffin::after, .fa-sharp-duotone.fa-coffin::after {
  content: "\f6c6\f6c6"; }

.fasds.fa-message::after, .fa-sharp-duotone.fa-message::after {
  content: "\f27a\f27a"; }

.fasds.fa-comment-alt::after, .fa-sharp-duotone.fa-comment-alt::after {
  content: "\f27a\f27a"; }

.fasds.fa-salad::after, .fa-sharp-duotone.fa-salad::after {
  content: "\f81e\f81e"; }

.fasds.fa-bowl-salad::after, .fa-sharp-duotone.fa-bowl-salad::after {
  content: "\f81e\f81e"; }

.fasds.fa-info::after, .fa-sharp-duotone.fa-info::after {
  content: "\f129\f129"; }

.fasds.fa-robot-astromech::after, .fa-sharp-duotone.fa-robot-astromech::after {
  content: "\e2d2\e2d2"; }

.fasds.fa-ring-diamond::after, .fa-sharp-duotone.fa-ring-diamond::after {
  content: "\e5ab\e5ab"; }

.fasds.fa-fondue-pot::after, .fa-sharp-duotone.fa-fondue-pot::after {
  content: "\e40d\e40d"; }

.fasds.fa-theta::after, .fa-sharp-duotone.fa-theta::after {
  content: "\f69e\f69e"; }

.fasds.fa-face-hand-peeking::after, .fa-sharp-duotone.fa-face-hand-peeking::after {
  content: "\e481\e481"; }

.fasds.fa-square-user::after, .fa-sharp-duotone.fa-square-user::after {
  content: "\e283\e283"; }

.fasds.fa-down-left-and-up-right-to-center::after, .fa-sharp-duotone.fa-down-left-and-up-right-to-center::after {
  content: "\f422\f422"; }

.fasds.fa-compress-alt::after, .fa-sharp-duotone.fa-compress-alt::after {
  content: "\f422\f422"; }

.fasds.fa-explosion::after, .fa-sharp-duotone.fa-explosion::after {
  content: "\e4e9\e4e9"; }

.fasds.fa-file-lines::after, .fa-sharp-duotone.fa-file-lines::after {
  content: "\f15c\f15c"; }

.fasds.fa-file-alt::after, .fa-sharp-duotone.fa-file-alt::after {
  content: "\f15c\f15c"; }

.fasds.fa-file-text::after, .fa-sharp-duotone.fa-file-text::after {
  content: "\f15c\f15c"; }

.fasds.fa-wave-square::after, .fa-sharp-duotone.fa-wave-square::after {
  content: "\f83e\f83e"; }

.fasds.fa-ring::after, .fa-sharp-duotone.fa-ring::after {
  content: "\f70b\f70b"; }

.fasds.fa-building-un::after, .fa-sharp-duotone.fa-building-un::after {
  content: "\e4d9\e4d9"; }

.fasds.fa-dice-three::after, .fa-sharp-duotone.fa-dice-three::after {
  content: "\f527\f527"; }

.fasds.fa-tire-pressure-warning::after, .fa-sharp-duotone.fa-tire-pressure-warning::after {
  content: "\f633\f633"; }

.fasds.fa-wifi-fair::after, .fa-sharp-duotone.fa-wifi-fair::after {
  content: "\f6ab\f6ab"; }

.fasds.fa-wifi-2::after, .fa-sharp-duotone.fa-wifi-2::after {
  content: "\f6ab\f6ab"; }

.fasds.fa-calendar-days::after, .fa-sharp-duotone.fa-calendar-days::after {
  content: "\f073\f073"; }

.fasds.fa-calendar-alt::after, .fa-sharp-duotone.fa-calendar-alt::after {
  content: "\f073\f073"; }

.fasds.fa-mp3-player::after, .fa-sharp-duotone.fa-mp3-player::after {
  content: "\f8ce\f8ce"; }

.fasds.fa-anchor-circle-check::after, .fa-sharp-duotone.fa-anchor-circle-check::after {
  content: "\e4aa\e4aa"; }

.fasds.fa-tally-4::after, .fa-sharp-duotone.fa-tally-4::after {
  content: "\e297\e297"; }

.fasds.fa-rectangle-history::after, .fa-sharp-duotone.fa-rectangle-history::after {
  content: "\e4a2\e4a2"; }

.fasds.fa-building-circle-arrow-right::after, .fa-sharp-duotone.fa-building-circle-arrow-right::after {
  content: "\e4d1\e4d1"; }

.fasds.fa-volleyball::after, .fa-sharp-duotone.fa-volleyball::after {
  content: "\f45f\f45f"; }

.fasds.fa-volleyball-ball::after, .fa-sharp-duotone.fa-volleyball-ball::after {
  content: "\f45f\f45f"; }

.fasds.fa-sun-haze::after, .fa-sharp-duotone.fa-sun-haze::after {
  content: "\f765\f765"; }

.fasds.fa-text-size::after, .fa-sharp-duotone.fa-text-size::after {
  content: "\f894\f894"; }

.fasds.fa-ufo::after, .fa-sharp-duotone.fa-ufo::after {
  content: "\e047\e047"; }

.fasds.fa-fork::after, .fa-sharp-duotone.fa-fork::after {
  content: "\f2e3\f2e3"; }

.fasds.fa-utensil-fork::after, .fa-sharp-duotone.fa-utensil-fork::after {
  content: "\f2e3\f2e3"; }

.fasds.fa-arrows-up-to-line::after, .fa-sharp-duotone.fa-arrows-up-to-line::after {
  content: "\e4c2\e4c2"; }

.fasds.fa-mobile-signal::after, .fa-sharp-duotone.fa-mobile-signal::after {
  content: "\e1ef\e1ef"; }

.fasds.fa-barcode-scan::after, .fa-sharp-duotone.fa-barcode-scan::after {
  content: "\f465\f465"; }

.fasds.fa-sort-down::after, .fa-sharp-duotone.fa-sort-down::after {
  content: "\f0dd\f0dd"; }

.fasds.fa-sort-desc::after, .fa-sharp-duotone.fa-sort-desc::after {
  content: "\f0dd\f0dd"; }

.fasds.fa-folder-arrow-down::after, .fa-sharp-duotone.fa-folder-arrow-down::after {
  content: "\e053\e053"; }

.fasds.fa-folder-download::after, .fa-sharp-duotone.fa-folder-download::after {
  content: "\e053\e053"; }

.fasds.fa-circle-minus::after, .fa-sharp-duotone.fa-circle-minus::after {
  content: "\f056\f056"; }

.fasds.fa-minus-circle::after, .fa-sharp-duotone.fa-minus-circle::after {
  content: "\f056\f056"; }

.fasds.fa-face-icicles::after, .fa-sharp-duotone.fa-face-icicles::after {
  content: "\e37c\e37c"; }

.fasds.fa-shovel::after, .fa-sharp-duotone.fa-shovel::after {
  content: "\f713\f713"; }

.fasds.fa-door-open::after, .fa-sharp-duotone.fa-door-open::after {
  content: "\f52b\f52b"; }

.fasds.fa-films::after, .fa-sharp-duotone.fa-films::after {
  content: "\e17a\e17a"; }

.fasds.fa-right-from-bracket::after, .fa-sharp-duotone.fa-right-from-bracket::after {
  content: "\f2f5\f2f5"; }

.fasds.fa-sign-out-alt::after, .fa-sharp-duotone.fa-sign-out-alt::after {
  content: "\f2f5\f2f5"; }

.fasds.fa-face-glasses::after, .fa-sharp-duotone.fa-face-glasses::after {
  content: "\e377\e377"; }

.fasds.fa-nfc::after, .fa-sharp-duotone.fa-nfc::after {
  content: "\e1f7\e1f7"; }

.fasds.fa-atom::after, .fa-sharp-duotone.fa-atom::after {
  content: "\f5d2\f5d2"; }

.fasds.fa-soap::after, .fa-sharp-duotone.fa-soap::after {
  content: "\e06e\e06e"; }

.fasds.fa-icons::after, .fa-sharp-duotone.fa-icons::after {
  content: "\f86d\f86d"; }

.fasds.fa-heart-music-camera-bolt::after, .fa-sharp-duotone.fa-heart-music-camera-bolt::after {
  content: "\f86d\f86d"; }

.fasds.fa-microphone-lines-slash::after, .fa-sharp-duotone.fa-microphone-lines-slash::after {
  content: "\f539\f539"; }

.fasds.fa-microphone-alt-slash::after, .fa-sharp-duotone.fa-microphone-alt-slash::after {
  content: "\f539\f539"; }

.fasds.fa-closed-captioning-slash::after, .fa-sharp-duotone.fa-closed-captioning-slash::after {
  content: "\e135\e135"; }

.fasds.fa-calculator-simple::after, .fa-sharp-duotone.fa-calculator-simple::after {
  content: "\f64c\f64c"; }

.fasds.fa-calculator-alt::after, .fa-sharp-duotone.fa-calculator-alt::after {
  content: "\f64c\f64c"; }

.fasds.fa-bridge-circle-check::after, .fa-sharp-duotone.fa-bridge-circle-check::after {
  content: "\e4c9\e4c9"; }

.fasds.fa-sliders-up::after, .fa-sharp-duotone.fa-sliders-up::after {
  content: "\f3f1\f3f1"; }

.fasds.fa-sliders-v::after, .fa-sharp-duotone.fa-sliders-v::after {
  content: "\f3f1\f3f1"; }

.fasds.fa-location-minus::after, .fa-sharp-duotone.fa-location-minus::after {
  content: "\f609\f609"; }

.fasds.fa-map-marker-minus::after, .fa-sharp-duotone.fa-map-marker-minus::after {
  content: "\f609\f609"; }

.fasds.fa-pump-medical::after, .fa-sharp-duotone.fa-pump-medical::after {
  content: "\e06a\e06a"; }

.fasds.fa-fingerprint::after, .fa-sharp-duotone.fa-fingerprint::after {
  content: "\f577\f577"; }

.fasds.fa-ski-boot::after, .fa-sharp-duotone.fa-ski-boot::after {
  content: "\e3cc\e3cc"; }

.fasds.fa-standard-definition::after, .fa-sharp-duotone.fa-standard-definition::after {
  content: "\e28a\e28a"; }

.fasds.fa-rectangle-sd::after, .fa-sharp-duotone.fa-rectangle-sd::after {
  content: "\e28a\e28a"; }

.fasds.fa-h1::after, .fa-sharp-duotone.fa-h1::after {
  content: "\f313\f313"; }

.fasds.fa-hand-point-right::after, .fa-sharp-duotone.fa-hand-point-right::after {
  content: "\f0a4\f0a4"; }

.fasds.fa-magnifying-glass-location::after, .fa-sharp-duotone.fa-magnifying-glass-location::after {
  content: "\f689\f689"; }

.fasds.fa-search-location::after, .fa-sharp-duotone.fa-search-location::after {
  content: "\f689\f689"; }

.fasds.fa-message-bot::after, .fa-sharp-duotone.fa-message-bot::after {
  content: "\e3b8\e3b8"; }

.fasds.fa-forward-step::after, .fa-sharp-duotone.fa-forward-step::after {
  content: "\f051\f051"; }

.fasds.fa-step-forward::after, .fa-sharp-duotone.fa-step-forward::after {
  content: "\f051\f051"; }

.fasds.fa-face-smile-beam::after, .fa-sharp-duotone.fa-face-smile-beam::after {
  content: "\f5b8\f5b8"; }

.fasds.fa-smile-beam::after, .fa-sharp-duotone.fa-smile-beam::after {
  content: "\f5b8\f5b8"; }

.fasds.fa-light-ceiling::after, .fa-sharp-duotone.fa-light-ceiling::after {
  content: "\e016\e016"; }

.fasds.fa-message-exclamation::after, .fa-sharp-duotone.fa-message-exclamation::after {
  content: "\f4a5\f4a5"; }

.fasds.fa-comment-alt-exclamation::after, .fa-sharp-duotone.fa-comment-alt-exclamation::after {
  content: "\f4a5\f4a5"; }

.fasds.fa-bowl-scoop::after, .fa-sharp-duotone.fa-bowl-scoop::after {
  content: "\e3de\e3de"; }

.fasds.fa-bowl-shaved-ice::after, .fa-sharp-duotone.fa-bowl-shaved-ice::after {
  content: "\e3de\e3de"; }

.fasds.fa-square-x::after, .fa-sharp-duotone.fa-square-x::after {
  content: "\e286\e286"; }

.fasds.fa-building-memo::after, .fa-sharp-duotone.fa-building-memo::after {
  content: "\e61e\e61e"; }

.fasds.fa-utility-pole-double::after, .fa-sharp-duotone.fa-utility-pole-double::after {
  content: "\e2c4\e2c4"; }

.fasds.fa-flag-checkered::after, .fa-sharp-duotone.fa-flag-checkered::after {
  content: "\f11e\f11e"; }

.fasds.fa-chevrons-up::after, .fa-sharp-duotone.fa-chevrons-up::after {
  content: "\f325\f325"; }

.fasds.fa-chevron-double-up::after, .fa-sharp-duotone.fa-chevron-double-up::after {
  content: "\f325\f325"; }

.fasds.fa-football::after, .fa-sharp-duotone.fa-football::after {
  content: "\f44e\f44e"; }

.fasds.fa-football-ball::after, .fa-sharp-duotone.fa-football-ball::after {
  content: "\f44e\f44e"; }

.fasds.fa-user-vneck::after, .fa-sharp-duotone.fa-user-vneck::after {
  content: "\e461\e461"; }

.fasds.fa-school-circle-exclamation::after, .fa-sharp-duotone.fa-school-circle-exclamation::after {
  content: "\e56c\e56c"; }

.fasds.fa-crop::after, .fa-sharp-duotone.fa-crop::after {
  content: "\f125\f125"; }

.fasds.fa-angles-down::after, .fa-sharp-duotone.fa-angles-down::after {
  content: "\f103\f103"; }

.fasds.fa-angle-double-down::after, .fa-sharp-duotone.fa-angle-double-down::after {
  content: "\f103\f103"; }

.fasds.fa-users-rectangle::after, .fa-sharp-duotone.fa-users-rectangle::after {
  content: "\e594\e594"; }

.fasds.fa-people-roof::after, .fa-sharp-duotone.fa-people-roof::after {
  content: "\e537\e537"; }

.fasds.fa-square-arrow-right::after, .fa-sharp-duotone.fa-square-arrow-right::after {
  content: "\f33b\f33b"; }

.fasds.fa-arrow-square-right::after, .fa-sharp-duotone.fa-arrow-square-right::after {
  content: "\f33b\f33b"; }

.fasds.fa-location-plus::after, .fa-sharp-duotone.fa-location-plus::after {
  content: "\f60a\f60a"; }

.fasds.fa-map-marker-plus::after, .fa-sharp-duotone.fa-map-marker-plus::after {
  content: "\f60a\f60a"; }

.fasds.fa-lightbulb-exclamation-on::after, .fa-sharp-duotone.fa-lightbulb-exclamation-on::after {
  content: "\e1ca\e1ca"; }

.fasds.fa-people-line::after, .fa-sharp-duotone.fa-people-line::after {
  content: "\e534\e534"; }

.fasds.fa-beer-mug-empty::after, .fa-sharp-duotone.fa-beer-mug-empty::after {
  content: "\f0fc\f0fc"; }

.fasds.fa-beer::after, .fa-sharp-duotone.fa-beer::after {
  content: "\f0fc\f0fc"; }

.fasds.fa-crate-empty::after, .fa-sharp-duotone.fa-crate-empty::after {
  content: "\e151\e151"; }

.fasds.fa-diagram-predecessor::after, .fa-sharp-duotone.fa-diagram-predecessor::after {
  content: "\e477\e477"; }

.fasds.fa-transporter::after, .fa-sharp-duotone.fa-transporter::after {
  content: "\e042\e042"; }

.fasds.fa-calendar-circle-user::after, .fa-sharp-duotone.fa-calendar-circle-user::after {
  content: "\e471\e471"; }

.fasds.fa-arrow-up-long::after, .fa-sharp-duotone.fa-arrow-up-long::after {
  content: "\f176\f176"; }

.fasds.fa-long-arrow-up::after, .fa-sharp-duotone.fa-long-arrow-up::after {
  content: "\f176\f176"; }

.fasds.fa-person-carry-box::after, .fa-sharp-duotone.fa-person-carry-box::after {
  content: "\f4cf\f4cf"; }

.fasds.fa-person-carry::after, .fa-sharp-duotone.fa-person-carry::after {
  content: "\f4cf\f4cf"; }

.fasds.fa-fire-flame-simple::after, .fa-sharp-duotone.fa-fire-flame-simple::after {
  content: "\f46a\f46a"; }

.fasds.fa-burn::after, .fa-sharp-duotone.fa-burn::after {
  content: "\f46a\f46a"; }

.fasds.fa-person::after, .fa-sharp-duotone.fa-person::after {
  content: "\f183\f183"; }

.fasds.fa-male::after, .fa-sharp-duotone.fa-male::after {
  content: "\f183\f183"; }

.fasds.fa-laptop::after, .fa-sharp-duotone.fa-laptop::after {
  content: "\f109\f109"; }

.fasds.fa-file-csv::after, .fa-sharp-duotone.fa-file-csv::after {
  content: "\f6dd\f6dd"; }

.fasds.fa-menorah::after, .fa-sharp-duotone.fa-menorah::after {
  content: "\f676\f676"; }

.fasds.fa-union::after, .fa-sharp-duotone.fa-union::after {
  content: "\f6a2\f6a2"; }

.fasds.fa-chevrons-left::after, .fa-sharp-duotone.fa-chevrons-left::after {
  content: "\f323\f323"; }

.fasds.fa-chevron-double-left::after, .fa-sharp-duotone.fa-chevron-double-left::after {
  content: "\f323\f323"; }

.fasds.fa-circle-heart::after, .fa-sharp-duotone.fa-circle-heart::after {
  content: "\f4c7\f4c7"; }

.fasds.fa-heart-circle::after, .fa-sharp-duotone.fa-heart-circle::after {
  content: "\f4c7\f4c7"; }

.fasds.fa-truck-plane::after, .fa-sharp-duotone.fa-truck-plane::after {
  content: "\e58f\e58f"; }

.fasds.fa-record-vinyl::after, .fa-sharp-duotone.fa-record-vinyl::after {
  content: "\f8d9\f8d9"; }

.fasds.fa-bring-forward::after, .fa-sharp-duotone.fa-bring-forward::after {
  content: "\f856\f856"; }

.fasds.fa-square-p::after, .fa-sharp-duotone.fa-square-p::after {
  content: "\e279\e279"; }

.fasds.fa-face-grin-stars::after, .fa-sharp-duotone.fa-face-grin-stars::after {
  content: "\f587\f587"; }

.fasds.fa-grin-stars::after, .fa-sharp-duotone.fa-grin-stars::after {
  content: "\f587\f587"; }

.fasds.fa-sigma::after, .fa-sharp-duotone.fa-sigma::after {
  content: "\f68b\f68b"; }

.fasds.fa-camera-movie::after, .fa-sharp-duotone.fa-camera-movie::after {
  content: "\f8a9\f8a9"; }

.fasds.fa-bong::after, .fa-sharp-duotone.fa-bong::after {
  content: "\f55c\f55c"; }

.fasds.fa-clarinet::after, .fa-sharp-duotone.fa-clarinet::after {
  content: "\f8ad\f8ad"; }

.fasds.fa-truck-flatbed::after, .fa-sharp-duotone.fa-truck-flatbed::after {
  content: "\e2b6\e2b6"; }

.fasds.fa-spaghetti-monster-flying::after, .fa-sharp-duotone.fa-spaghetti-monster-flying::after {
  content: "\f67b\f67b"; }

.fasds.fa-pastafarianism::after, .fa-sharp-duotone.fa-pastafarianism::after {
  content: "\f67b\f67b"; }

.fasds.fa-arrow-down-up-across-line::after, .fa-sharp-duotone.fa-arrow-down-up-across-line::after {
  content: "\e4af\e4af"; }

.fasds.fa-arrows-rotate-reverse::after, .fa-sharp-duotone.fa-arrows-rotate-reverse::after {
  content: "\e630\e630"; }

.fasds.fa-leaf-heart::after, .fa-sharp-duotone.fa-leaf-heart::after {
  content: "\f4cb\f4cb"; }

.fasds.fa-house-building::after, .fa-sharp-duotone.fa-house-building::after {
  content: "\e1b1\e1b1"; }

.fasds.fa-cheese-swiss::after, .fa-sharp-duotone.fa-cheese-swiss::after {
  content: "\f7f0\f7f0"; }

.fasds.fa-spoon::after, .fa-sharp-duotone.fa-spoon::after {
  content: "\f2e5\f2e5"; }

.fasds.fa-utensil-spoon::after, .fa-sharp-duotone.fa-utensil-spoon::after {
  content: "\f2e5\f2e5"; }

.fasds.fa-jar-wheat::after, .fa-sharp-duotone.fa-jar-wheat::after {
  content: "\e517\e517"; }

.fasds.fa-envelopes-bulk::after, .fa-sharp-duotone.fa-envelopes-bulk::after {
  content: "\f674\f674"; }

.fasds.fa-mail-bulk::after, .fa-sharp-duotone.fa-mail-bulk::after {
  content: "\f674\f674"; }

.fasds.fa-file-circle-exclamation::after, .fa-sharp-duotone.fa-file-circle-exclamation::after {
  content: "\e4eb\e4eb"; }

.fasds.fa-bow-arrow::after, .fa-sharp-duotone.fa-bow-arrow::after {
  content: "\f6b9\f6b9"; }

.fasds.fa-cart-xmark::after, .fa-sharp-duotone.fa-cart-xmark::after {
  content: "\e0dd\e0dd"; }

.fasds.fa-hexagon-xmark::after, .fa-sharp-duotone.fa-hexagon-xmark::after {
  content: "\f2ee\f2ee"; }

.fasds.fa-times-hexagon::after, .fa-sharp-duotone.fa-times-hexagon::after {
  content: "\f2ee\f2ee"; }

.fasds.fa-xmark-hexagon::after, .fa-sharp-duotone.fa-xmark-hexagon::after {
  content: "\f2ee\f2ee"; }

.fasds.fa-circle-h::after, .fa-sharp-duotone.fa-circle-h::after {
  content: "\f47e\f47e"; }

.fasds.fa-hospital-symbol::after, .fa-sharp-duotone.fa-hospital-symbol::after {
  content: "\f47e\f47e"; }

.fasds.fa-merge::after, .fa-sharp-duotone.fa-merge::after {
  content: "\e526\e526"; }

.fasds.fa-pager::after, .fa-sharp-duotone.fa-pager::after {
  content: "\f815\f815"; }

.fasds.fa-cart-minus::after, .fa-sharp-duotone.fa-cart-minus::after {
  content: "\e0db\e0db"; }

.fasds.fa-address-book::after, .fa-sharp-duotone.fa-address-book::after {
  content: "\f2b9\f2b9"; }

.fasds.fa-contact-book::after, .fa-sharp-duotone.fa-contact-book::after {
  content: "\f2b9\f2b9"; }

.fasds.fa-pan-frying::after, .fa-sharp-duotone.fa-pan-frying::after {
  content: "\e42c\e42c"; }

.fasds.fa-grid::after, .fa-sharp-duotone.fa-grid::after {
  content: "\e195\e195"; }

.fasds.fa-grid-3::after, .fa-sharp-duotone.fa-grid-3::after {
  content: "\e195\e195"; }

.fasds.fa-football-helmet::after, .fa-sharp-duotone.fa-football-helmet::after {
  content: "\f44f\f44f"; }

.fasds.fa-hand-love::after, .fa-sharp-duotone.fa-hand-love::after {
  content: "\e1a5\e1a5"; }

.fasds.fa-trees::after, .fa-sharp-duotone.fa-trees::after {
  content: "\f724\f724"; }

.fasds.fa-strikethrough::after, .fa-sharp-duotone.fa-strikethrough::after {
  content: "\f0cc\f0cc"; }

.fasds.fa-page::after, .fa-sharp-duotone.fa-page::after {
  content: "\e428\e428"; }

.fasds.fa-k::after, .fa-sharp-duotone.fa-k::after {
  content: "\4b\4b"; }

.fasds.fa-diagram-previous::after, .fa-sharp-duotone.fa-diagram-previous::after {
  content: "\e478\e478"; }

.fasds.fa-gauge-min::after, .fa-sharp-duotone.fa-gauge-min::after {
  content: "\f628\f628"; }

.fasds.fa-tachometer-alt-slowest::after, .fa-sharp-duotone.fa-tachometer-alt-slowest::after {
  content: "\f628\f628"; }

.fasds.fa-folder-grid::after, .fa-sharp-duotone.fa-folder-grid::after {
  content: "\e188\e188"; }

.fasds.fa-eggplant::after, .fa-sharp-duotone.fa-eggplant::after {
  content: "\e16c\e16c"; }

.fasds.fa-excavator::after, .fa-sharp-duotone.fa-excavator::after {
  content: "\e656\e656"; }

.fasds.fa-ram::after, .fa-sharp-duotone.fa-ram::after {
  content: "\f70a\f70a"; }

.fasds.fa-landmark-flag::after, .fa-sharp-duotone.fa-landmark-flag::after {
  content: "\e51c\e51c"; }

.fasds.fa-lips::after, .fa-sharp-duotone.fa-lips::after {
  content: "\f600\f600"; }

.fasds.fa-pencil::after, .fa-sharp-duotone.fa-pencil::after {
  content: "\f303\f303"; }

.fasds.fa-pencil-alt::after, .fa-sharp-duotone.fa-pencil-alt::after {
  content: "\f303\f303"; }

.fasds.fa-backward::after, .fa-sharp-duotone.fa-backward::after {
  content: "\f04a\f04a"; }

.fasds.fa-caret-right::after, .fa-sharp-duotone.fa-caret-right::after {
  content: "\f0da\f0da"; }

.fasds.fa-comments::after, .fa-sharp-duotone.fa-comments::after {
  content: "\f086\f086"; }

.fasds.fa-paste::after, .fa-sharp-duotone.fa-paste::after {
  content: "\f0ea\f0ea"; }

.fasds.fa-file-clipboard::after, .fa-sharp-duotone.fa-file-clipboard::after {
  content: "\f0ea\f0ea"; }

.fasds.fa-desktop-arrow-down::after, .fa-sharp-duotone.fa-desktop-arrow-down::after {
  content: "\e155\e155"; }

.fasds.fa-code-pull-request::after, .fa-sharp-duotone.fa-code-pull-request::after {
  content: "\e13c\e13c"; }

.fasds.fa-pumpkin::after, .fa-sharp-duotone.fa-pumpkin::after {
  content: "\f707\f707"; }

.fasds.fa-clipboard-list::after, .fa-sharp-duotone.fa-clipboard-list::after {
  content: "\f46d\f46d"; }

.fasds.fa-pen-field::after, .fa-sharp-duotone.fa-pen-field::after {
  content: "\e211\e211"; }

.fasds.fa-blueberries::after, .fa-sharp-duotone.fa-blueberries::after {
  content: "\e2e8\e2e8"; }

.fasds.fa-truck-ramp-box::after, .fa-sharp-duotone.fa-truck-ramp-box::after {
  content: "\f4de\f4de"; }

.fasds.fa-truck-loading::after, .fa-sharp-duotone.fa-truck-loading::after {
  content: "\f4de\f4de"; }

.fasds.fa-note::after, .fa-sharp-duotone.fa-note::after {
  content: "\e1ff\e1ff"; }

.fasds.fa-arrow-down-to-square::after, .fa-sharp-duotone.fa-arrow-down-to-square::after {
  content: "\e096\e096"; }

.fasds.fa-user-check::after, .fa-sharp-duotone.fa-user-check::after {
  content: "\f4fc\f4fc"; }

.fasds.fa-cloud-xmark::after, .fa-sharp-duotone.fa-cloud-xmark::after {
  content: "\e35f\e35f"; }

.fasds.fa-vial-virus::after, .fa-sharp-duotone.fa-vial-virus::after {
  content: "\e597\e597"; }

.fasds.fa-book-blank::after, .fa-sharp-duotone.fa-book-blank::after {
  content: "\f5d9\f5d9"; }

.fasds.fa-book-alt::after, .fa-sharp-duotone.fa-book-alt::after {
  content: "\f5d9\f5d9"; }

.fasds.fa-golf-flag-hole::after, .fa-sharp-duotone.fa-golf-flag-hole::after {
  content: "\e3ac\e3ac"; }

.fasds.fa-message-arrow-down::after, .fa-sharp-duotone.fa-message-arrow-down::after {
  content: "\e1db\e1db"; }

.fasds.fa-comment-alt-arrow-down::after, .fa-sharp-duotone.fa-comment-alt-arrow-down::after {
  content: "\e1db\e1db"; }

.fasds.fa-face-unamused::after, .fa-sharp-duotone.fa-face-unamused::after {
  content: "\e39f\e39f"; }

.fasds.fa-sheet-plastic::after, .fa-sharp-duotone.fa-sheet-plastic::after {
  content: "\e571\e571"; }

.fasds.fa-circle-9::after, .fa-sharp-duotone.fa-circle-9::after {
  content: "\e0f6\e0f6"; }

.fasds.fa-blog::after, .fa-sharp-duotone.fa-blog::after {
  content: "\f781\f781"; }

.fasds.fa-user-ninja::after, .fa-sharp-duotone.fa-user-ninja::after {
  content: "\f504\f504"; }

.fasds.fa-pencil-slash::after, .fa-sharp-duotone.fa-pencil-slash::after {
  content: "\e215\e215"; }

.fasds.fa-bowling-pins::after, .fa-sharp-duotone.fa-bowling-pins::after {
  content: "\f437\f437"; }

.fasds.fa-person-arrow-up-from-line::after, .fa-sharp-duotone.fa-person-arrow-up-from-line::after {
  content: "\e539\e539"; }

.fasds.fa-down-right::after, .fa-sharp-duotone.fa-down-right::after {
  content: "\e16b\e16b"; }

.fasds.fa-scroll-torah::after, .fa-sharp-duotone.fa-scroll-torah::after {
  content: "\f6a0\f6a0"; }

.fasds.fa-torah::after, .fa-sharp-duotone.fa-torah::after {
  content: "\f6a0\f6a0"; }

.fasds.fa-webhook::after, .fa-sharp-duotone.fa-webhook::after {
  content: "\e5d5\e5d5"; }

.fasds.fa-blinds-open::after, .fa-sharp-duotone.fa-blinds-open::after {
  content: "\f8fc\f8fc"; }

.fasds.fa-fence::after, .fa-sharp-duotone.fa-fence::after {
  content: "\e303\e303"; }

.fasds.fa-up::after, .fa-sharp-duotone.fa-up::after {
  content: "\f357\f357"; }

.fasds.fa-arrow-alt-up::after, .fa-sharp-duotone.fa-arrow-alt-up::after {
  content: "\f357\f357"; }

.fasds.fa-broom-ball::after, .fa-sharp-duotone.fa-broom-ball::after {
  content: "\f458\f458"; }

.fasds.fa-quidditch::after, .fa-sharp-duotone.fa-quidditch::after {
  content: "\f458\f458"; }

.fasds.fa-quidditch-broom-ball::after, .fa-sharp-duotone.fa-quidditch-broom-ball::after {
  content: "\f458\f458"; }

.fasds.fa-drumstick::after, .fa-sharp-duotone.fa-drumstick::after {
  content: "\f6d6\f6d6"; }

.fasds.fa-square-v::after, .fa-sharp-duotone.fa-square-v::after {
  content: "\e284\e284"; }

.fasds.fa-face-awesome::after, .fa-sharp-duotone.fa-face-awesome::after {
  content: "\e409\e409"; }

.fasds.fa-gave-dandy::after, .fa-sharp-duotone.fa-gave-dandy::after {
  content: "\e409\e409"; }

.fasds.fa-dial-off::after, .fa-sharp-duotone.fa-dial-off::after {
  content: "\e162\e162"; }

.fasds.fa-toggle-off::after, .fa-sharp-duotone.fa-toggle-off::after {
  content: "\f204\f204"; }

.fasds.fa-face-smile-horns::after, .fa-sharp-duotone.fa-face-smile-horns::after {
  content: "\e391\e391"; }

.fasds.fa-box-archive::after, .fa-sharp-duotone.fa-box-archive::after {
  content: "\f187\f187"; }

.fasds.fa-archive::after, .fa-sharp-duotone.fa-archive::after {
  content: "\f187\f187"; }

.fasds.fa-grapes::after, .fa-sharp-duotone.fa-grapes::after {
  content: "\e306\e306"; }

.fasds.fa-person-drowning::after, .fa-sharp-duotone.fa-person-drowning::after {
  content: "\e545\e545"; }

.fasds.fa-dial-max::after, .fa-sharp-duotone.fa-dial-max::after {
  content: "\e15e\e15e"; }

.fasds.fa-circle-m::after, .fa-sharp-duotone.fa-circle-m::after {
  content: "\e115\e115"; }

.fasds.fa-calendar-image::after, .fa-sharp-duotone.fa-calendar-image::after {
  content: "\e0d4\e0d4"; }

.fasds.fa-circle-caret-down::after, .fa-sharp-duotone.fa-circle-caret-down::after {
  content: "\f32d\f32d"; }

.fasds.fa-caret-circle-down::after, .fa-sharp-duotone.fa-caret-circle-down::after {
  content: "\f32d\f32d"; }

.fasds.fa-arrow-down-9-1::after, .fa-sharp-duotone.fa-arrow-down-9-1::after {
  content: "\f886\f886"; }

.fasds.fa-sort-numeric-desc::after, .fa-sharp-duotone.fa-sort-numeric-desc::after {
  content: "\f886\f886"; }

.fasds.fa-sort-numeric-down-alt::after, .fa-sharp-duotone.fa-sort-numeric-down-alt::after {
  content: "\f886\f886"; }

.fasds.fa-face-grin-tongue-squint::after, .fa-sharp-duotone.fa-face-grin-tongue-squint::after {
  content: "\f58a\f58a"; }

.fasds.fa-grin-tongue-squint::after, .fa-sharp-duotone.fa-grin-tongue-squint::after {
  content: "\f58a\f58a"; }

.fasds.fa-shish-kebab::after, .fa-sharp-duotone.fa-shish-kebab::after {
  content: "\f821\f821"; }

.fasds.fa-spray-can::after, .fa-sharp-duotone.fa-spray-can::after {
  content: "\f5bd\f5bd"; }

.fasds.fa-alarm-snooze::after, .fa-sharp-duotone.fa-alarm-snooze::after {
  content: "\f845\f845"; }

.fasds.fa-scarecrow::after, .fa-sharp-duotone.fa-scarecrow::after {
  content: "\f70d\f70d"; }

.fasds.fa-truck-monster::after, .fa-sharp-duotone.fa-truck-monster::after {
  content: "\f63b\f63b"; }

.fasds.fa-gift-card::after, .fa-sharp-duotone.fa-gift-card::after {
  content: "\f663\f663"; }

.fasds.fa-w::after, .fa-sharp-duotone.fa-w::after {
  content: "\57\57"; }

.fasds.fa-code-pull-request-draft::after, .fa-sharp-duotone.fa-code-pull-request-draft::after {
  content: "\e3fa\e3fa"; }

.fasds.fa-square-b::after, .fa-sharp-duotone.fa-square-b::after {
  content: "\e264\e264"; }

.fasds.fa-elephant::after, .fa-sharp-duotone.fa-elephant::after {
  content: "\f6da\f6da"; }

.fasds.fa-earth-africa::after, .fa-sharp-duotone.fa-earth-africa::after {
  content: "\f57c\f57c"; }

.fasds.fa-globe-africa::after, .fa-sharp-duotone.fa-globe-africa::after {
  content: "\f57c\f57c"; }

.fasds.fa-rainbow::after, .fa-sharp-duotone.fa-rainbow::after {
  content: "\f75b\f75b"; }

.fasds.fa-circle-notch::after, .fa-sharp-duotone.fa-circle-notch::after {
  content: "\f1ce\f1ce"; }

.fasds.fa-tablet-screen-button::after, .fa-sharp-duotone.fa-tablet-screen-button::after {
  content: "\f3fa\f3fa"; }

.fasds.fa-tablet-alt::after, .fa-sharp-duotone.fa-tablet-alt::after {
  content: "\f3fa\f3fa"; }

.fasds.fa-paw::after, .fa-sharp-duotone.fa-paw::after {
  content: "\f1b0\f1b0"; }

.fasds.fa-message-question::after, .fa-sharp-duotone.fa-message-question::after {
  content: "\e1e3\e1e3"; }

.fasds.fa-cloud::after, .fa-sharp-duotone.fa-cloud::after {
  content: "\f0c2\f0c2"; }

.fasds.fa-trowel-bricks::after, .fa-sharp-duotone.fa-trowel-bricks::after {
  content: "\e58a\e58a"; }

.fasds.fa-square-3::after, .fa-sharp-duotone.fa-square-3::after {
  content: "\e258\e258"; }

.fasds.fa-face-flushed::after, .fa-sharp-duotone.fa-face-flushed::after {
  content: "\f579\f579"; }

.fasds.fa-flushed::after, .fa-sharp-duotone.fa-flushed::after {
  content: "\f579\f579"; }

.fasds.fa-hospital-user::after, .fa-sharp-duotone.fa-hospital-user::after {
  content: "\f80d\f80d"; }

.fasds.fa-microwave::after, .fa-sharp-duotone.fa-microwave::after {
  content: "\e01b\e01b"; }

.fasds.fa-chf-sign::after, .fa-sharp-duotone.fa-chf-sign::after {
  content: "\e602\e602"; }

.fasds.fa-tent-arrow-left-right::after, .fa-sharp-duotone.fa-tent-arrow-left-right::after {
  content: "\e57f\e57f"; }

.fasds.fa-cart-circle-arrow-up::after, .fa-sharp-duotone.fa-cart-circle-arrow-up::after {
  content: "\e3f0\e3f0"; }

.fasds.fa-trash-clock::after, .fa-sharp-duotone.fa-trash-clock::after {
  content: "\e2b0\e2b0"; }

.fasds.fa-reflect-both::after, .fa-sharp-duotone.fa-reflect-both::after {
  content: "\e66f\e66f"; }

.fasds.fa-gavel::after, .fa-sharp-duotone.fa-gavel::after {
  content: "\f0e3\f0e3"; }

.fasds.fa-legal::after, .fa-sharp-duotone.fa-legal::after {
  content: "\f0e3\f0e3"; }

.fasds.fa-sprinkler-ceiling::after, .fa-sharp-duotone.fa-sprinkler-ceiling::after {
  content: "\e44c\e44c"; }

.fasds.fa-browsers::after, .fa-sharp-duotone.fa-browsers::after {
  content: "\e0cb\e0cb"; }

.fasds.fa-trillium::after, .fa-sharp-duotone.fa-trillium::after {
  content: "\e588\e588"; }

.fasds.fa-table-cells-unlock::after, .fa-sharp-duotone.fa-table-cells-unlock::after {
  content: "\e692\e692"; }

.fasds.fa-music-slash::after, .fa-sharp-duotone.fa-music-slash::after {
  content: "\f8d1\f8d1"; }

.fasds.fa-truck-ramp::after, .fa-sharp-duotone.fa-truck-ramp::after {
  content: "\f4e0\f4e0"; }

.fasds.fa-binoculars::after, .fa-sharp-duotone.fa-binoculars::after {
  content: "\f1e5\f1e5"; }

.fasds.fa-microphone-slash::after, .fa-sharp-duotone.fa-microphone-slash::after {
  content: "\f131\f131"; }

.fasds.fa-box-tissue::after, .fa-sharp-duotone.fa-box-tissue::after {
  content: "\e05b\e05b"; }

.fasds.fa-circle-c::after, .fa-sharp-duotone.fa-circle-c::after {
  content: "\e101\e101"; }

.fasds.fa-star-christmas::after, .fa-sharp-duotone.fa-star-christmas::after {
  content: "\f7d4\f7d4"; }

.fasds.fa-chart-bullet::after, .fa-sharp-duotone.fa-chart-bullet::after {
  content: "\e0e1\e0e1"; }

.fasds.fa-motorcycle::after, .fa-sharp-duotone.fa-motorcycle::after {
  content: "\f21c\f21c"; }

.fasds.fa-tree-christmas::after, .fa-sharp-duotone.fa-tree-christmas::after {
  content: "\f7db\f7db"; }

.fasds.fa-tire-flat::after, .fa-sharp-duotone.fa-tire-flat::after {
  content: "\f632\f632"; }

.fasds.fa-sunglasses::after, .fa-sharp-duotone.fa-sunglasses::after {
  content: "\f892\f892"; }

.fasds.fa-badge::after, .fa-sharp-duotone.fa-badge::after {
  content: "\f335\f335"; }

.fasds.fa-message-pen::after, .fa-sharp-duotone.fa-message-pen::after {
  content: "\f4a4\f4a4"; }

.fasds.fa-comment-alt-edit::after, .fa-sharp-duotone.fa-comment-alt-edit::after {
  content: "\f4a4\f4a4"; }

.fasds.fa-message-edit::after, .fa-sharp-duotone.fa-message-edit::after {
  content: "\f4a4\f4a4"; }

.fasds.fa-bell-concierge::after, .fa-sharp-duotone.fa-bell-concierge::after {
  content: "\f562\f562"; }

.fasds.fa-concierge-bell::after, .fa-sharp-duotone.fa-concierge-bell::after {
  content: "\f562\f562"; }

.fasds.fa-pen-ruler::after, .fa-sharp-duotone.fa-pen-ruler::after {
  content: "\f5ae\f5ae"; }

.fasds.fa-pencil-ruler::after, .fa-sharp-duotone.fa-pencil-ruler::after {
  content: "\f5ae\f5ae"; }

.fasds.fa-file-mp3::after, .fa-sharp-duotone.fa-file-mp3::after {
  content: "\e648\e648"; }

.fasds.fa-arrow-progress::after, .fa-sharp-duotone.fa-arrow-progress::after {
  content: "\e5df\e5df"; }

.fasds.fa-chess-rook-piece::after, .fa-sharp-duotone.fa-chess-rook-piece::after {
  content: "\f448\f448"; }

.fasds.fa-chess-rook-alt::after, .fa-sharp-duotone.fa-chess-rook-alt::after {
  content: "\f448\f448"; }

.fasds.fa-square-root::after, .fa-sharp-duotone.fa-square-root::after {
  content: "\f697\f697"; }

.fasds.fa-album-collection-circle-plus::after, .fa-sharp-duotone.fa-album-collection-circle-plus::after {
  content: "\e48e\e48e"; }

.fasds.fa-people-arrows::after, .fa-sharp-duotone.fa-people-arrows::after {
  content: "\e068\e068"; }

.fasds.fa-people-arrows-left-right::after, .fa-sharp-duotone.fa-people-arrows-left-right::after {
  content: "\e068\e068"; }

.fasds.fa-sign-post::after, .fa-sharp-duotone.fa-sign-post::after {
  content: "\e624\e624"; }

.fasds.fa-face-angry-horns::after, .fa-sharp-duotone.fa-face-angry-horns::after {
  content: "\e368\e368"; }

.fasds.fa-mars-and-venus-burst::after, .fa-sharp-duotone.fa-mars-and-venus-burst::after {
  content: "\e523\e523"; }

.fasds.fa-tombstone::after, .fa-sharp-duotone.fa-tombstone::after {
  content: "\f720\f720"; }

.fasds.fa-square-caret-right::after, .fa-sharp-duotone.fa-square-caret-right::after {
  content: "\f152\f152"; }

.fasds.fa-caret-square-right::after, .fa-sharp-duotone.fa-caret-square-right::after {
  content: "\f152\f152"; }

.fasds.fa-scissors::after, .fa-sharp-duotone.fa-scissors::after {
  content: "\f0c4\f0c4"; }

.fasds.fa-cut::after, .fa-sharp-duotone.fa-cut::after {
  content: "\f0c4\f0c4"; }

.fasds.fa-list-music::after, .fa-sharp-duotone.fa-list-music::after {
  content: "\f8c9\f8c9"; }

.fasds.fa-sun-plant-wilt::after, .fa-sharp-duotone.fa-sun-plant-wilt::after {
  content: "\e57a\e57a"; }

.fasds.fa-toilets-portable::after, .fa-sharp-duotone.fa-toilets-portable::after {
  content: "\e584\e584"; }

.fasds.fa-hockey-puck::after, .fa-sharp-duotone.fa-hockey-puck::after {
  content: "\f453\f453"; }

.fasds.fa-mustache::after, .fa-sharp-duotone.fa-mustache::after {
  content: "\e5bc\e5bc"; }

.fasds.fa-hyphen::after, .fa-sharp-duotone.fa-hyphen::after {
  content: "\2d\2d"; }

.fasds.fa-table::after, .fa-sharp-duotone.fa-table::after {
  content: "\f0ce\f0ce"; }

.fasds.fa-user-chef::after, .fa-sharp-duotone.fa-user-chef::after {
  content: "\e3d2\e3d2"; }

.fasds.fa-message-image::after, .fa-sharp-duotone.fa-message-image::after {
  content: "\e1e0\e1e0"; }

.fasds.fa-comment-alt-image::after, .fa-sharp-duotone.fa-comment-alt-image::after {
  content: "\e1e0\e1e0"; }

.fasds.fa-users-medical::after, .fa-sharp-duotone.fa-users-medical::after {
  content: "\f830\f830"; }

.fasds.fa-sensor-triangle-exclamation::after, .fa-sharp-duotone.fa-sensor-triangle-exclamation::after {
  content: "\e029\e029"; }

.fasds.fa-sensor-alert::after, .fa-sharp-duotone.fa-sensor-alert::after {
  content: "\e029\e029"; }

.fasds.fa-magnifying-glass-arrow-right::after, .fa-sharp-duotone.fa-magnifying-glass-arrow-right::after {
  content: "\e521\e521"; }

.fasds.fa-tachograph-digital::after, .fa-sharp-duotone.fa-tachograph-digital::after {
  content: "\f566\f566"; }

.fasds.fa-digital-tachograph::after, .fa-sharp-duotone.fa-digital-tachograph::after {
  content: "\f566\f566"; }

.fasds.fa-face-mask::after, .fa-sharp-duotone.fa-face-mask::after {
  content: "\e37f\e37f"; }

.fasds.fa-pickleball::after, .fa-sharp-duotone.fa-pickleball::after {
  content: "\e435\e435"; }

.fasds.fa-star-sharp-half::after, .fa-sharp-duotone.fa-star-sharp-half::after {
  content: "\e28c\e28c"; }

.fasds.fa-users-slash::after, .fa-sharp-duotone.fa-users-slash::after {
  content: "\e073\e073"; }

.fasds.fa-clover::after, .fa-sharp-duotone.fa-clover::after {
  content: "\e139\e139"; }

.fasds.fa-meat::after, .fa-sharp-duotone.fa-meat::after {
  content: "\f814\f814"; }

.fasds.fa-reply::after, .fa-sharp-duotone.fa-reply::after {
  content: "\f3e5\f3e5"; }

.fasds.fa-mail-reply::after, .fa-sharp-duotone.fa-mail-reply::after {
  content: "\f3e5\f3e5"; }

.fasds.fa-star-and-crescent::after, .fa-sharp-duotone.fa-star-and-crescent::after {
  content: "\f699\f699"; }

.fasds.fa-empty-set::after, .fa-sharp-duotone.fa-empty-set::after {
  content: "\f656\f656"; }

.fasds.fa-house-fire::after, .fa-sharp-duotone.fa-house-fire::after {
  content: "\e50c\e50c"; }

.fasds.fa-square-minus::after, .fa-sharp-duotone.fa-square-minus::after {
  content: "\f146\f146"; }

.fasds.fa-minus-square::after, .fa-sharp-duotone.fa-minus-square::after {
  content: "\f146\f146"; }

.fasds.fa-helicopter::after, .fa-sharp-duotone.fa-helicopter::after {
  content: "\f533\f533"; }

.fasds.fa-bird::after, .fa-sharp-duotone.fa-bird::after {
  content: "\e469\e469"; }

.fasds.fa-compass::after, .fa-sharp-duotone.fa-compass::after {
  content: "\f14e\f14e"; }

.fasds.fa-square-caret-down::after, .fa-sharp-duotone.fa-square-caret-down::after {
  content: "\f150\f150"; }

.fasds.fa-caret-square-down::after, .fa-sharp-duotone.fa-caret-square-down::after {
  content: "\f150\f150"; }

.fasds.fa-heart-half-stroke::after, .fa-sharp-duotone.fa-heart-half-stroke::after {
  content: "\e1ac\e1ac"; }

.fasds.fa-heart-half-alt::after, .fa-sharp-duotone.fa-heart-half-alt::after {
  content: "\e1ac\e1ac"; }

.fasds.fa-file-circle-question::after, .fa-sharp-duotone.fa-file-circle-question::after {
  content: "\e4ef\e4ef"; }

.fasds.fa-truck-utensils::after, .fa-sharp-duotone.fa-truck-utensils::after {
  content: "\e628\e628"; }

.fasds.fa-laptop-code::after, .fa-sharp-duotone.fa-laptop-code::after {
  content: "\f5fc\f5fc"; }

.fasds.fa-joystick::after, .fa-sharp-duotone.fa-joystick::after {
  content: "\f8c5\f8c5"; }

.fasds.fa-grill-fire::after, .fa-sharp-duotone.fa-grill-fire::after {
  content: "\e5a4\e5a4"; }

.fasds.fa-rectangle-vertical-history::after, .fa-sharp-duotone.fa-rectangle-vertical-history::after {
  content: "\e237\e237"; }

.fasds.fa-swatchbook::after, .fa-sharp-duotone.fa-swatchbook::after {
  content: "\f5c3\f5c3"; }

.fasds.fa-prescription-bottle::after, .fa-sharp-duotone.fa-prescription-bottle::after {
  content: "\f485\f485"; }

.fasds.fa-bars::after, .fa-sharp-duotone.fa-bars::after {
  content: "\f0c9\f0c9"; }

.fasds.fa-navicon::after, .fa-sharp-duotone.fa-navicon::after {
  content: "\f0c9\f0c9"; }

.fasds.fa-keyboard-left::after, .fa-sharp-duotone.fa-keyboard-left::after {
  content: "\e1c3\e1c3"; }

.fasds.fa-people-group::after, .fa-sharp-duotone.fa-people-group::after {
  content: "\e533\e533"; }

.fasds.fa-hourglass-end::after, .fa-sharp-duotone.fa-hourglass-end::after {
  content: "\f253\f253"; }

.fasds.fa-hourglass-3::after, .fa-sharp-duotone.fa-hourglass-3::after {
  content: "\f253\f253"; }

.fasds.fa-heart-crack::after, .fa-sharp-duotone.fa-heart-crack::after {
  content: "\f7a9\f7a9"; }

.fasds.fa-heart-broken::after, .fa-sharp-duotone.fa-heart-broken::after {
  content: "\f7a9\f7a9"; }

.fasds.fa-face-beam-hand-over-mouth::after, .fa-sharp-duotone.fa-face-beam-hand-over-mouth::after {
  content: "\e47c\e47c"; }

.fasds.fa-droplet-percent::after, .fa-sharp-duotone.fa-droplet-percent::after {
  content: "\f750\f750"; }

.fasds.fa-humidity::after, .fa-sharp-duotone.fa-humidity::after {
  content: "\f750\f750"; }

.fasds.fa-square-up-right::after, .fa-sharp-duotone.fa-square-up-right::after {
  content: "\f360\f360"; }

.fasds.fa-external-link-square-alt::after, .fa-sharp-duotone.fa-external-link-square-alt::after {
  content: "\f360\f360"; }

.fasds.fa-face-kiss-beam::after, .fa-sharp-duotone.fa-face-kiss-beam::after {
  content: "\f597\f597"; }

.fasds.fa-kiss-beam::after, .fa-sharp-duotone.fa-kiss-beam::after {
  content: "\f597\f597"; }

.fasds.fa-corn::after, .fa-sharp-duotone.fa-corn::after {
  content: "\f6c7\f6c7"; }

.fasds.fa-roller-coaster::after, .fa-sharp-duotone.fa-roller-coaster::after {
  content: "\e324\e324"; }

.fasds.fa-photo-film-music::after, .fa-sharp-duotone.fa-photo-film-music::after {
  content: "\e228\e228"; }

.fasds.fa-radar::after, .fa-sharp-duotone.fa-radar::after {
  content: "\e024\e024"; }

.fasds.fa-sickle::after, .fa-sharp-duotone.fa-sickle::after {
  content: "\f822\f822"; }

.fasds.fa-film::after, .fa-sharp-duotone.fa-film::after {
  content: "\f008\f008"; }

.fasds.fa-coconut::after, .fa-sharp-duotone.fa-coconut::after {
  content: "\e2f6\e2f6"; }

.fasds.fa-ruler-horizontal::after, .fa-sharp-duotone.fa-ruler-horizontal::after {
  content: "\f547\f547"; }

.fasds.fa-shield-cross::after, .fa-sharp-duotone.fa-shield-cross::after {
  content: "\f712\f712"; }

.fasds.fa-cassette-tape::after, .fa-sharp-duotone.fa-cassette-tape::after {
  content: "\f8ab\f8ab"; }

.fasds.fa-square-terminal::after, .fa-sharp-duotone.fa-square-terminal::after {
  content: "\e32a\e32a"; }

.fasds.fa-people-robbery::after, .fa-sharp-duotone.fa-people-robbery::after {
  content: "\e536\e536"; }

.fasds.fa-lightbulb::after, .fa-sharp-duotone.fa-lightbulb::after {
  content: "\f0eb\f0eb"; }

.fasds.fa-caret-left::after, .fa-sharp-duotone.fa-caret-left::after {
  content: "\f0d9\f0d9"; }

.fasds.fa-comment-middle::after, .fa-sharp-duotone.fa-comment-middle::after {
  content: "\e149\e149"; }

.fasds.fa-trash-can-list::after, .fa-sharp-duotone.fa-trash-can-list::after {
  content: "\e2ab\e2ab"; }

.fasds.fa-block::after, .fa-sharp-duotone.fa-block::after {
  content: "\e46a\e46a"; }

.fasds.fa-circle-exclamation::after, .fa-sharp-duotone.fa-circle-exclamation::after {
  content: "\f06a\f06a"; }

.fasds.fa-exclamation-circle::after, .fa-sharp-duotone.fa-exclamation-circle::after {
  content: "\f06a\f06a"; }

.fasds.fa-school-circle-xmark::after, .fa-sharp-duotone.fa-school-circle-xmark::after {
  content: "\e56d\e56d"; }

.fasds.fa-arrow-right-from-bracket::after, .fa-sharp-duotone.fa-arrow-right-from-bracket::after {
  content: "\f08b\f08b"; }

.fasds.fa-sign-out::after, .fa-sharp-duotone.fa-sign-out::after {
  content: "\f08b\f08b"; }

.fasds.fa-face-frown-slight::after, .fa-sharp-duotone.fa-face-frown-slight::after {
  content: "\e376\e376"; }

.fasds.fa-circle-chevron-down::after, .fa-sharp-duotone.fa-circle-chevron-down::after {
  content: "\f13a\f13a"; }

.fasds.fa-chevron-circle-down::after, .fa-sharp-duotone.fa-chevron-circle-down::after {
  content: "\f13a\f13a"; }

.fasds.fa-sidebar-flip::after, .fa-sharp-duotone.fa-sidebar-flip::after {
  content: "\e24f\e24f"; }

.fasds.fa-unlock-keyhole::after, .fa-sharp-duotone.fa-unlock-keyhole::after {
  content: "\f13e\f13e"; }

.fasds.fa-unlock-alt::after, .fa-sharp-duotone.fa-unlock-alt::after {
  content: "\f13e\f13e"; }

.fasds.fa-temperature-list::after, .fa-sharp-duotone.fa-temperature-list::after {
  content: "\e299\e299"; }

.fasds.fa-cloud-showers-heavy::after, .fa-sharp-duotone.fa-cloud-showers-heavy::after {
  content: "\f740\f740"; }

.fasds.fa-headphones-simple::after, .fa-sharp-duotone.fa-headphones-simple::after {
  content: "\f58f\f58f"; }

.fasds.fa-headphones-alt::after, .fa-sharp-duotone.fa-headphones-alt::after {
  content: "\f58f\f58f"; }

.fasds.fa-sitemap::after, .fa-sharp-duotone.fa-sitemap::after {
  content: "\f0e8\f0e8"; }

.fasds.fa-pipe-section::after, .fa-sharp-duotone.fa-pipe-section::after {
  content: "\e438\e438"; }

.fasds.fa-space-station-moon-construction::after, .fa-sharp-duotone.fa-space-station-moon-construction::after {
  content: "\e034\e034"; }

.fasds.fa-space-station-moon-alt::after, .fa-sharp-duotone.fa-space-station-moon-alt::after {
  content: "\e034\e034"; }

.fasds.fa-circle-dollar-to-slot::after, .fa-sharp-duotone.fa-circle-dollar-to-slot::after {
  content: "\f4b9\f4b9"; }

.fasds.fa-donate::after, .fa-sharp-duotone.fa-donate::after {
  content: "\f4b9\f4b9"; }

.fasds.fa-memory::after, .fa-sharp-duotone.fa-memory::after {
  content: "\f538\f538"; }

.fasds.fa-face-sleeping::after, .fa-sharp-duotone.fa-face-sleeping::after {
  content: "\e38d\e38d"; }

.fasds.fa-road-spikes::after, .fa-sharp-duotone.fa-road-spikes::after {
  content: "\e568\e568"; }

.fasds.fa-fire-burner::after, .fa-sharp-duotone.fa-fire-burner::after {
  content: "\e4f1\e4f1"; }

.fasds.fa-squirrel::after, .fa-sharp-duotone.fa-squirrel::after {
  content: "\f71a\f71a"; }

.fasds.fa-arrow-up-to-line::after, .fa-sharp-duotone.fa-arrow-up-to-line::after {
  content: "\f341\f341"; }

.fasds.fa-arrow-to-top::after, .fa-sharp-duotone.fa-arrow-to-top::after {
  content: "\f341\f341"; }

.fasds.fa-flag::after, .fa-sharp-duotone.fa-flag::after {
  content: "\f024\f024"; }

.fasds.fa-face-cowboy-hat::after, .fa-sharp-duotone.fa-face-cowboy-hat::after {
  content: "\e36e\e36e"; }

.fasds.fa-hanukiah::after, .fa-sharp-duotone.fa-hanukiah::after {
  content: "\f6e6\f6e6"; }

.fasds.fa-chart-scatter-3d::after, .fa-sharp-duotone.fa-chart-scatter-3d::after {
  content: "\e0e8\e0e8"; }

.fasds.fa-display-chart-up::after, .fa-sharp-duotone.fa-display-chart-up::after {
  content: "\e5e3\e5e3"; }

.fasds.fa-square-code::after, .fa-sharp-duotone.fa-square-code::after {
  content: "\e267\e267"; }

.fasds.fa-feather::after, .fa-sharp-duotone.fa-feather::after {
  content: "\f52d\f52d"; }

.fasds.fa-volume-low::after, .fa-sharp-duotone.fa-volume-low::after {
  content: "\f027\f027"; }

.fasds.fa-volume-down::after, .fa-sharp-duotone.fa-volume-down::after {
  content: "\f027\f027"; }

.fasds.fa-xmark-to-slot::after, .fa-sharp-duotone.fa-xmark-to-slot::after {
  content: "\f771\f771"; }

.fasds.fa-times-to-slot::after, .fa-sharp-duotone.fa-times-to-slot::after {
  content: "\f771\f771"; }

.fasds.fa-vote-nay::after, .fa-sharp-duotone.fa-vote-nay::after {
  content: "\f771\f771"; }

.fasds.fa-box-taped::after, .fa-sharp-duotone.fa-box-taped::after {
  content: "\f49a\f49a"; }

.fasds.fa-box-alt::after, .fa-sharp-duotone.fa-box-alt::after {
  content: "\f49a\f49a"; }

.fasds.fa-comment-slash::after, .fa-sharp-duotone.fa-comment-slash::after {
  content: "\f4b3\f4b3"; }

.fasds.fa-swords::after, .fa-sharp-duotone.fa-swords::after {
  content: "\f71d\f71d"; }

.fasds.fa-cloud-sun-rain::after, .fa-sharp-duotone.fa-cloud-sun-rain::after {
  content: "\f743\f743"; }

.fasds.fa-album::after, .fa-sharp-duotone.fa-album::after {
  content: "\f89f\f89f"; }

.fasds.fa-circle-n::after, .fa-sharp-duotone.fa-circle-n::after {
  content: "\e118\e118"; }

.fasds.fa-compress::after, .fa-sharp-duotone.fa-compress::after {
  content: "\f066\f066"; }

.fasds.fa-wheat-awn::after, .fa-sharp-duotone.fa-wheat-awn::after {
  content: "\e2cd\e2cd"; }

.fasds.fa-wheat-alt::after, .fa-sharp-duotone.fa-wheat-alt::after {
  content: "\e2cd\e2cd"; }

.fasds.fa-ankh::after, .fa-sharp-duotone.fa-ankh::after {
  content: "\f644\f644"; }

.fasds.fa-hands-holding-child::after, .fa-sharp-duotone.fa-hands-holding-child::after {
  content: "\e4fa\e4fa"; }

.fasds.fa-asterisk::after, .fa-sharp-duotone.fa-asterisk::after {
  content: "\2a\2a"; }

.fasds.fa-key-skeleton-left-right::after, .fa-sharp-duotone.fa-key-skeleton-left-right::after {
  content: "\e3b4\e3b4"; }

.fasds.fa-comment-lines::after, .fa-sharp-duotone.fa-comment-lines::after {
  content: "\f4b0\f4b0"; }

.fasds.fa-luchador-mask::after, .fa-sharp-duotone.fa-luchador-mask::after {
  content: "\f455\f455"; }

.fasds.fa-luchador::after, .fa-sharp-duotone.fa-luchador::after {
  content: "\f455\f455"; }

.fasds.fa-mask-luchador::after, .fa-sharp-duotone.fa-mask-luchador::after {
  content: "\f455\f455"; }

.fasds.fa-square-check::after, .fa-sharp-duotone.fa-square-check::after {
  content: "\f14a\f14a"; }

.fasds.fa-check-square::after, .fa-sharp-duotone.fa-check-square::after {
  content: "\f14a\f14a"; }

.fasds.fa-shredder::after, .fa-sharp-duotone.fa-shredder::after {
  content: "\f68a\f68a"; }

.fasds.fa-book-open-cover::after, .fa-sharp-duotone.fa-book-open-cover::after {
  content: "\e0c0\e0c0"; }

.fasds.fa-book-open-alt::after, .fa-sharp-duotone.fa-book-open-alt::after {
  content: "\e0c0\e0c0"; }

.fasds.fa-sandwich::after, .fa-sharp-duotone.fa-sandwich::after {
  content: "\f81f\f81f"; }

.fasds.fa-peseta-sign::after, .fa-sharp-duotone.fa-peseta-sign::after {
  content: "\e221\e221"; }

.fasds.fa-square-parking-slash::after, .fa-sharp-duotone.fa-square-parking-slash::after {
  content: "\f617\f617"; }

.fasds.fa-parking-slash::after, .fa-sharp-duotone.fa-parking-slash::after {
  content: "\f617\f617"; }

.fasds.fa-train-tunnel::after, .fa-sharp-duotone.fa-train-tunnel::after {
  content: "\e454\e454"; }

.fasds.fa-heading::after, .fa-sharp-duotone.fa-heading::after {
  content: "\f1dc\f1dc"; }

.fasds.fa-header::after, .fa-sharp-duotone.fa-header::after {
  content: "\f1dc\f1dc"; }

.fasds.fa-ghost::after, .fa-sharp-duotone.fa-ghost::after {
  content: "\f6e2\f6e2"; }

.fasds.fa-face-anguished::after, .fa-sharp-duotone.fa-face-anguished::after {
  content: "\e369\e369"; }

.fasds.fa-hockey-sticks::after, .fa-sharp-duotone.fa-hockey-sticks::after {
  content: "\f454\f454"; }

.fasds.fa-abacus::after, .fa-sharp-duotone.fa-abacus::after {
  content: "\f640\f640"; }

.fasds.fa-film-simple::after, .fa-sharp-duotone.fa-film-simple::after {
  content: "\f3a0\f3a0"; }

.fasds.fa-film-alt::after, .fa-sharp-duotone.fa-film-alt::after {
  content: "\f3a0\f3a0"; }

.fasds.fa-list::after, .fa-sharp-duotone.fa-list::after {
  content: "\f03a\f03a"; }

.fasds.fa-list-squares::after, .fa-sharp-duotone.fa-list-squares::after {
  content: "\f03a\f03a"; }

.fasds.fa-tree-palm::after, .fa-sharp-duotone.fa-tree-palm::after {
  content: "\f82b\f82b"; }

.fasds.fa-square-phone-flip::after, .fa-sharp-duotone.fa-square-phone-flip::after {
  content: "\f87b\f87b"; }

.fasds.fa-phone-square-alt::after, .fa-sharp-duotone.fa-phone-square-alt::after {
  content: "\f87b\f87b"; }

.fasds.fa-user-beard-bolt::after, .fa-sharp-duotone.fa-user-beard-bolt::after {
  content: "\e689\e689"; }

.fasds.fa-cart-plus::after, .fa-sharp-duotone.fa-cart-plus::after {
  content: "\f217\f217"; }

.fasds.fa-gamepad::after, .fa-sharp-duotone.fa-gamepad::after {
  content: "\f11b\f11b"; }

.fasds.fa-border-center-v::after, .fa-sharp-duotone.fa-border-center-v::after {
  content: "\f89d\f89d"; }

.fasds.fa-circle-dot::after, .fa-sharp-duotone.fa-circle-dot::after {
  content: "\f192\f192"; }

.fasds.fa-dot-circle::after, .fa-sharp-duotone.fa-dot-circle::after {
  content: "\f192\f192"; }

.fasds.fa-clipboard-medical::after, .fa-sharp-duotone.fa-clipboard-medical::after {
  content: "\e133\e133"; }

.fasds.fa-face-dizzy::after, .fa-sharp-duotone.fa-face-dizzy::after {
  content: "\f567\f567"; }

.fasds.fa-dizzy::after, .fa-sharp-duotone.fa-dizzy::after {
  content: "\f567\f567"; }

.fasds.fa-egg::after, .fa-sharp-duotone.fa-egg::after {
  content: "\f7fb\f7fb"; }

.fasds.fa-up-to-line::after, .fa-sharp-duotone.fa-up-to-line::after {
  content: "\f34d\f34d"; }

.fasds.fa-arrow-alt-to-top::after, .fa-sharp-duotone.fa-arrow-alt-to-top::after {
  content: "\f34d\f34d"; }

.fasds.fa-house-medical-circle-xmark::after, .fa-sharp-duotone.fa-house-medical-circle-xmark::after {
  content: "\e513\e513"; }

.fasds.fa-watch-fitness::after, .fa-sharp-duotone.fa-watch-fitness::after {
  content: "\f63e\f63e"; }

.fasds.fa-clock-nine-thirty::after, .fa-sharp-duotone.fa-clock-nine-thirty::after {
  content: "\e34d\e34d"; }

.fasds.fa-campground::after, .fa-sharp-duotone.fa-campground::after {
  content: "\f6bb\f6bb"; }

.fasds.fa-folder-plus::after, .fa-sharp-duotone.fa-folder-plus::after {
  content: "\f65e\f65e"; }

.fasds.fa-jug::after, .fa-sharp-duotone.fa-jug::after {
  content: "\f8c6\f8c6"; }

.fasds.fa-futbol::after, .fa-sharp-duotone.fa-futbol::after {
  content: "\f1e3\f1e3"; }

.fasds.fa-futbol-ball::after, .fa-sharp-duotone.fa-futbol-ball::after {
  content: "\f1e3\f1e3"; }

.fasds.fa-soccer-ball::after, .fa-sharp-duotone.fa-soccer-ball::after {
  content: "\f1e3\f1e3"; }

.fasds.fa-snow-blowing::after, .fa-sharp-duotone.fa-snow-blowing::after {
  content: "\f761\f761"; }

.fasds.fa-paintbrush::after, .fa-sharp-duotone.fa-paintbrush::after {
  content: "\f1fc\f1fc"; }

.fasds.fa-paint-brush::after, .fa-sharp-duotone.fa-paint-brush::after {
  content: "\f1fc\f1fc"; }

.fasds.fa-lock::after, .fa-sharp-duotone.fa-lock::after {
  content: "\f023\f023"; }

.fasds.fa-arrow-down-from-line::after, .fa-sharp-duotone.fa-arrow-down-from-line::after {
  content: "\f345\f345"; }

.fasds.fa-arrow-from-top::after, .fa-sharp-duotone.fa-arrow-from-top::after {
  content: "\f345\f345"; }

.fasds.fa-gas-pump::after, .fa-sharp-duotone.fa-gas-pump::after {
  content: "\f52f\f52f"; }

.fasds.fa-signal-bars-slash::after, .fa-sharp-duotone.fa-signal-bars-slash::after {
  content: "\f694\f694"; }

.fasds.fa-signal-alt-slash::after, .fa-sharp-duotone.fa-signal-alt-slash::after {
  content: "\f694\f694"; }

.fasds.fa-monkey::after, .fa-sharp-duotone.fa-monkey::after {
  content: "\f6fb\f6fb"; }

.fasds.fa-rectangle-pro::after, .fa-sharp-duotone.fa-rectangle-pro::after {
  content: "\e235\e235"; }

.fasds.fa-pro::after, .fa-sharp-duotone.fa-pro::after {
  content: "\e235\e235"; }

.fasds.fa-house-night::after, .fa-sharp-duotone.fa-house-night::after {
  content: "\e010\e010"; }

.fasds.fa-hot-tub-person::after, .fa-sharp-duotone.fa-hot-tub-person::after {
  content: "\f593\f593"; }

.fasds.fa-hot-tub::after, .fa-sharp-duotone.fa-hot-tub::after {
  content: "\f593\f593"; }

.fasds.fa-globe-pointer::after, .fa-sharp-duotone.fa-globe-pointer::after {
  content: "\e60e\e60e"; }

.fasds.fa-blanket::after, .fa-sharp-duotone.fa-blanket::after {
  content: "\f498\f498"; }

.fasds.fa-map-location::after, .fa-sharp-duotone.fa-map-location::after {
  content: "\f59f\f59f"; }

.fasds.fa-map-marked::after, .fa-sharp-duotone.fa-map-marked::after {
  content: "\f59f\f59f"; }

.fasds.fa-house-flood-water::after, .fa-sharp-duotone.fa-house-flood-water::after {
  content: "\e50e\e50e"; }

.fasds.fa-comments-question-check::after, .fa-sharp-duotone.fa-comments-question-check::after {
  content: "\e14f\e14f"; }

.fasds.fa-tree::after, .fa-sharp-duotone.fa-tree::after {
  content: "\f1bb\f1bb"; }

.fasds.fa-arrows-cross::after, .fa-sharp-duotone.fa-arrows-cross::after {
  content: "\e0a2\e0a2"; }

.fasds.fa-backpack::after, .fa-sharp-duotone.fa-backpack::after {
  content: "\f5d4\f5d4"; }

.fasds.fa-square-small::after, .fa-sharp-duotone.fa-square-small::after {
  content: "\e27e\e27e"; }

.fasds.fa-folder-arrow-up::after, .fa-sharp-duotone.fa-folder-arrow-up::after {
  content: "\e054\e054"; }

.fasds.fa-folder-upload::after, .fa-sharp-duotone.fa-folder-upload::after {
  content: "\e054\e054"; }

.fasds.fa-bridge-lock::after, .fa-sharp-duotone.fa-bridge-lock::after {
  content: "\e4cc\e4cc"; }

.fasds.fa-crosshairs-simple::after, .fa-sharp-duotone.fa-crosshairs-simple::after {
  content: "\e59f\e59f"; }

.fasds.fa-sack-dollar::after, .fa-sharp-duotone.fa-sack-dollar::after {
  content: "\f81d\f81d"; }

.fasds.fa-pen-to-square::after, .fa-sharp-duotone.fa-pen-to-square::after {
  content: "\f044\f044"; }

.fasds.fa-edit::after, .fa-sharp-duotone.fa-edit::after {
  content: "\f044\f044"; }

.fasds.fa-square-sliders::after, .fa-sharp-duotone.fa-square-sliders::after {
  content: "\f3f0\f3f0"; }

.fasds.fa-sliders-h-square::after, .fa-sharp-duotone.fa-sliders-h-square::after {
  content: "\f3f0\f3f0"; }

.fasds.fa-car-side::after, .fa-sharp-duotone.fa-car-side::after {
  content: "\f5e4\f5e4"; }

.fasds.fa-message-middle-top::after, .fa-sharp-duotone.fa-message-middle-top::after {
  content: "\e1e2\e1e2"; }

.fasds.fa-comment-middle-top-alt::after, .fa-sharp-duotone.fa-comment-middle-top-alt::after {
  content: "\e1e2\e1e2"; }

.fasds.fa-lightbulb-on::after, .fa-sharp-duotone.fa-lightbulb-on::after {
  content: "\f672\f672"; }

.fasds.fa-knife::after, .fa-sharp-duotone.fa-knife::after {
  content: "\f2e4\f2e4"; }

.fasds.fa-utensil-knife::after, .fa-sharp-duotone.fa-utensil-knife::after {
  content: "\f2e4\f2e4"; }

.fasds.fa-share-nodes::after, .fa-sharp-duotone.fa-share-nodes::after {
  content: "\f1e0\f1e0"; }

.fasds.fa-share-alt::after, .fa-sharp-duotone.fa-share-alt::after {
  content: "\f1e0\f1e0"; }

.fasds.fa-display-chart-up-circle-dollar::after, .fa-sharp-duotone.fa-display-chart-up-circle-dollar::after {
  content: "\e5e6\e5e6"; }

.fasds.fa-wave-sine::after, .fa-sharp-duotone.fa-wave-sine::after {
  content: "\f899\f899"; }

.fasds.fa-heart-circle-minus::after, .fa-sharp-duotone.fa-heart-circle-minus::after {
  content: "\e4ff\e4ff"; }

.fasds.fa-circle-w::after, .fa-sharp-duotone.fa-circle-w::after {
  content: "\e12c\e12c"; }

.fasds.fa-circle-calendar::after, .fa-sharp-duotone.fa-circle-calendar::after {
  content: "\e102\e102"; }

.fasds.fa-calendar-circle::after, .fa-sharp-duotone.fa-calendar-circle::after {
  content: "\e102\e102"; }

.fasds.fa-hourglass-half::after, .fa-sharp-duotone.fa-hourglass-half::after {
  content: "\f252\f252"; }

.fasds.fa-hourglass-2::after, .fa-sharp-duotone.fa-hourglass-2::after {
  content: "\f252\f252"; }

.fasds.fa-microscope::after, .fa-sharp-duotone.fa-microscope::after {
  content: "\f610\f610"; }

.fasds.fa-sunset::after, .fa-sharp-duotone.fa-sunset::after {
  content: "\f767\f767"; }

.fasds.fa-sink::after, .fa-sharp-duotone.fa-sink::after {
  content: "\e06d\e06d"; }

.fasds.fa-calendar-exclamation::after, .fa-sharp-duotone.fa-calendar-exclamation::after {
  content: "\f334\f334"; }

.fasds.fa-truck-container-empty::after, .fa-sharp-duotone.fa-truck-container-empty::after {
  content: "\e2b5\e2b5"; }

.fasds.fa-hand-heart::after, .fa-sharp-duotone.fa-hand-heart::after {
  content: "\f4bc\f4bc"; }

.fasds.fa-bag-shopping::after, .fa-sharp-duotone.fa-bag-shopping::after {
  content: "\f290\f290"; }

.fasds.fa-shopping-bag::after, .fa-sharp-duotone.fa-shopping-bag::after {
  content: "\f290\f290"; }

.fasds.fa-arrow-down-z-a::after, .fa-sharp-duotone.fa-arrow-down-z-a::after {
  content: "\f881\f881"; }

.fasds.fa-sort-alpha-desc::after, .fa-sharp-duotone.fa-sort-alpha-desc::after {
  content: "\f881\f881"; }

.fasds.fa-sort-alpha-down-alt::after, .fa-sharp-duotone.fa-sort-alpha-down-alt::after {
  content: "\f881\f881"; }

.fasds.fa-mitten::after, .fa-sharp-duotone.fa-mitten::after {
  content: "\f7b5\f7b5"; }

.fasds.fa-reply-clock::after, .fa-sharp-duotone.fa-reply-clock::after {
  content: "\e239\e239"; }

.fasds.fa-reply-time::after, .fa-sharp-duotone.fa-reply-time::after {
  content: "\e239\e239"; }

.fasds.fa-person-rays::after, .fa-sharp-duotone.fa-person-rays::after {
  content: "\e54d\e54d"; }

.fasds.fa-right::after, .fa-sharp-duotone.fa-right::after {
  content: "\f356\f356"; }

.fasds.fa-arrow-alt-right::after, .fa-sharp-duotone.fa-arrow-alt-right::after {
  content: "\f356\f356"; }

.fasds.fa-circle-f::after, .fa-sharp-duotone.fa-circle-f::after {
  content: "\e10e\e10e"; }

.fasds.fa-users::after, .fa-sharp-duotone.fa-users::after {
  content: "\f0c0\f0c0"; }

.fasds.fa-face-pleading::after, .fa-sharp-duotone.fa-face-pleading::after {
  content: "\e386\e386"; }

.fasds.fa-eye-slash::after, .fa-sharp-duotone.fa-eye-slash::after {
  content: "\f070\f070"; }

.fasds.fa-flask-vial::after, .fa-sharp-duotone.fa-flask-vial::after {
  content: "\e4f3\e4f3"; }

.fasds.fa-police-box::after, .fa-sharp-duotone.fa-police-box::after {
  content: "\e021\e021"; }

.fasds.fa-cucumber::after, .fa-sharp-duotone.fa-cucumber::after {
  content: "\e401\e401"; }

.fasds.fa-head-side-brain::after, .fa-sharp-duotone.fa-head-side-brain::after {
  content: "\f808\f808"; }

.fasds.fa-hand::after, .fa-sharp-duotone.fa-hand::after {
  content: "\f256\f256"; }

.fasds.fa-hand-paper::after, .fa-sharp-duotone.fa-hand-paper::after {
  content: "\f256\f256"; }

.fasds.fa-person-biking-mountain::after, .fa-sharp-duotone.fa-person-biking-mountain::after {
  content: "\f84b\f84b"; }

.fasds.fa-biking-mountain::after, .fa-sharp-duotone.fa-biking-mountain::after {
  content: "\f84b\f84b"; }

.fasds.fa-utensils-slash::after, .fa-sharp-duotone.fa-utensils-slash::after {
  content: "\e464\e464"; }

.fasds.fa-print-magnifying-glass::after, .fa-sharp-duotone.fa-print-magnifying-glass::after {
  content: "\f81a\f81a"; }

.fasds.fa-print-search::after, .fa-sharp-duotone.fa-print-search::after {
  content: "\f81a\f81a"; }

.fasds.fa-turn-right::after, .fa-sharp-duotone.fa-turn-right::after {
  content: "\e639\e639"; }

.fasds.fa-folder-bookmark::after, .fa-sharp-duotone.fa-folder-bookmark::after {
  content: "\e186\e186"; }

.fasds.fa-arrow-turn-left-down::after, .fa-sharp-duotone.fa-arrow-turn-left-down::after {
  content: "\e633\e633"; }

.fasds.fa-om::after, .fa-sharp-duotone.fa-om::after {
  content: "\f679\f679"; }

.fasds.fa-pi::after, .fa-sharp-duotone.fa-pi::after {
  content: "\f67e\f67e"; }

.fasds.fa-flask-round-potion::after, .fa-sharp-duotone.fa-flask-round-potion::after {
  content: "\f6e1\f6e1"; }

.fasds.fa-flask-potion::after, .fa-sharp-duotone.fa-flask-potion::after {
  content: "\f6e1\f6e1"; }

.fasds.fa-face-shush::after, .fa-sharp-duotone.fa-face-shush::after {
  content: "\e38c\e38c"; }

.fasds.fa-worm::after, .fa-sharp-duotone.fa-worm::after {
  content: "\e599\e599"; }

.fasds.fa-house-circle-xmark::after, .fa-sharp-duotone.fa-house-circle-xmark::after {
  content: "\e50b\e50b"; }

.fasds.fa-plug::after, .fa-sharp-duotone.fa-plug::after {
  content: "\f1e6\f1e6"; }

.fasds.fa-calendar-circle-exclamation::after, .fa-sharp-duotone.fa-calendar-circle-exclamation::after {
  content: "\e46e\e46e"; }

.fasds.fa-square-i::after, .fa-sharp-duotone.fa-square-i::after {
  content: "\e272\e272"; }

.fasds.fa-chevron-up::after, .fa-sharp-duotone.fa-chevron-up::after {
  content: "\f077\f077"; }

.fasds.fa-face-saluting::after, .fa-sharp-duotone.fa-face-saluting::after {
  content: "\e484\e484"; }

.fasds.fa-gauge-simple-low::after, .fa-sharp-duotone.fa-gauge-simple-low::after {
  content: "\f62c\f62c"; }

.fasds.fa-tachometer-slow::after, .fa-sharp-duotone.fa-tachometer-slow::after {
  content: "\f62c\f62c"; }

.fasds.fa-face-persevering::after, .fa-sharp-duotone.fa-face-persevering::after {
  content: "\e385\e385"; }

.fasds.fa-circle-camera::after, .fa-sharp-duotone.fa-circle-camera::after {
  content: "\e103\e103"; }

.fasds.fa-camera-circle::after, .fa-sharp-duotone.fa-camera-circle::after {
  content: "\e103\e103"; }

.fasds.fa-hand-spock::after, .fa-sharp-duotone.fa-hand-spock::after {
  content: "\f259\f259"; }

.fasds.fa-spider-web::after, .fa-sharp-duotone.fa-spider-web::after {
  content: "\f719\f719"; }

.fasds.fa-circle-microphone::after, .fa-sharp-duotone.fa-circle-microphone::after {
  content: "\e116\e116"; }

.fasds.fa-microphone-circle::after, .fa-sharp-duotone.fa-microphone-circle::after {
  content: "\e116\e116"; }

.fasds.fa-book-arrow-up::after, .fa-sharp-duotone.fa-book-arrow-up::after {
  content: "\e0ba\e0ba"; }

.fasds.fa-popsicle::after, .fa-sharp-duotone.fa-popsicle::after {
  content: "\e43e\e43e"; }

.fasds.fa-command::after, .fa-sharp-duotone.fa-command::after {
  content: "\e142\e142"; }

.fasds.fa-blinds::after, .fa-sharp-duotone.fa-blinds::after {
  content: "\f8fb\f8fb"; }

.fasds.fa-stopwatch::after, .fa-sharp-duotone.fa-stopwatch::after {
  content: "\f2f2\f2f2"; }

.fasds.fa-saxophone::after, .fa-sharp-duotone.fa-saxophone::after {
  content: "\f8dc\f8dc"; }

.fasds.fa-square-2::after, .fa-sharp-duotone.fa-square-2::after {
  content: "\e257\e257"; }

.fasds.fa-field-hockey-stick-ball::after, .fa-sharp-duotone.fa-field-hockey-stick-ball::after {
  content: "\f44c\f44c"; }

.fasds.fa-field-hockey::after, .fa-sharp-duotone.fa-field-hockey::after {
  content: "\f44c\f44c"; }

.fasds.fa-arrow-up-square-triangle::after, .fa-sharp-duotone.fa-arrow-up-square-triangle::after {
  content: "\f88b\f88b"; }

.fasds.fa-sort-shapes-up-alt::after, .fa-sharp-duotone.fa-sort-shapes-up-alt::after {
  content: "\f88b\f88b"; }

.fasds.fa-face-scream::after, .fa-sharp-duotone.fa-face-scream::after {
  content: "\e38b\e38b"; }

.fasds.fa-square-m::after, .fa-sharp-duotone.fa-square-m::after {
  content: "\e276\e276"; }

.fasds.fa-camera-web::after, .fa-sharp-duotone.fa-camera-web::after {
  content: "\f832\f832"; }

.fasds.fa-webcam::after, .fa-sharp-duotone.fa-webcam::after {
  content: "\f832\f832"; }

.fasds.fa-comment-arrow-down::after, .fa-sharp-duotone.fa-comment-arrow-down::after {
  content: "\e143\e143"; }

.fasds.fa-lightbulb-cfl::after, .fa-sharp-duotone.fa-lightbulb-cfl::after {
  content: "\e5a6\e5a6"; }

.fasds.fa-window-frame-open::after, .fa-sharp-duotone.fa-window-frame-open::after {
  content: "\e050\e050"; }

.fasds.fa-face-kiss::after, .fa-sharp-duotone.fa-face-kiss::after {
  content: "\f596\f596"; }

.fasds.fa-kiss::after, .fa-sharp-duotone.fa-kiss::after {
  content: "\f596\f596"; }

.fasds.fa-bridge-circle-xmark::after, .fa-sharp-duotone.fa-bridge-circle-xmark::after {
  content: "\e4cb\e4cb"; }

.fasds.fa-period::after, .fa-sharp-duotone.fa-period::after {
  content: "\2e\2e"; }

.fasds.fa-face-grin-tongue::after, .fa-sharp-duotone.fa-face-grin-tongue::after {
  content: "\f589\f589"; }

.fasds.fa-grin-tongue::after, .fa-sharp-duotone.fa-grin-tongue::after {
  content: "\f589\f589"; }

.fasds.fa-up-to-dotted-line::after, .fa-sharp-duotone.fa-up-to-dotted-line::after {
  content: "\e457\e457"; }

.fasds.fa-thought-bubble::after, .fa-sharp-duotone.fa-thought-bubble::after {
  content: "\e32e\e32e"; }

.fasds.fa-skeleton-ribs::after, .fa-sharp-duotone.fa-skeleton-ribs::after {
  content: "\e5cb\e5cb"; }

.fasds.fa-raygun::after, .fa-sharp-duotone.fa-raygun::after {
  content: "\e025\e025"; }

.fasds.fa-flute::after, .fa-sharp-duotone.fa-flute::after {
  content: "\f8b9\f8b9"; }

.fasds.fa-acorn::after, .fa-sharp-duotone.fa-acorn::after {
  content: "\f6ae\f6ae"; }

.fasds.fa-video-arrow-up-right::after, .fa-sharp-duotone.fa-video-arrow-up-right::after {
  content: "\e2c9\e2c9"; }

.fasds.fa-grate-droplet::after, .fa-sharp-duotone.fa-grate-droplet::after {
  content: "\e194\e194"; }

.fasds.fa-seal-exclamation::after, .fa-sharp-duotone.fa-seal-exclamation::after {
  content: "\e242\e242"; }

.fasds.fa-chess-bishop::after, .fa-sharp-duotone.fa-chess-bishop::after {
  content: "\f43a\f43a"; }

.fasds.fa-message-sms::after, .fa-sharp-duotone.fa-message-sms::after {
  content: "\e1e5\e1e5"; }

.fasds.fa-coffee-beans::after, .fa-sharp-duotone.fa-coffee-beans::after {
  content: "\e13f\e13f"; }

.fasds.fa-hat-witch::after, .fa-sharp-duotone.fa-hat-witch::after {
  content: "\f6e7\f6e7"; }

.fasds.fa-face-grin-wink::after, .fa-sharp-duotone.fa-face-grin-wink::after {
  content: "\f58c\f58c"; }

.fasds.fa-grin-wink::after, .fa-sharp-duotone.fa-grin-wink::after {
  content: "\f58c\f58c"; }

.fasds.fa-clock-three-thirty::after, .fa-sharp-duotone.fa-clock-three-thirty::after {
  content: "\e357\e357"; }

.fasds.fa-ear-deaf::after, .fa-sharp-duotone.fa-ear-deaf::after {
  content: "\f2a4\f2a4"; }

.fasds.fa-deaf::after, .fa-sharp-duotone.fa-deaf::after {
  content: "\f2a4\f2a4"; }

.fasds.fa-deafness::after, .fa-sharp-duotone.fa-deafness::after {
  content: "\f2a4\f2a4"; }

.fasds.fa-hard-of-hearing::after, .fa-sharp-duotone.fa-hard-of-hearing::after {
  content: "\f2a4\f2a4"; }

.fasds.fa-alarm-clock::after, .fa-sharp-duotone.fa-alarm-clock::after {
  content: "\f34e\f34e"; }

.fasds.fa-eclipse::after, .fa-sharp-duotone.fa-eclipse::after {
  content: "\f749\f749"; }

.fasds.fa-face-relieved::after, .fa-sharp-duotone.fa-face-relieved::after {
  content: "\e389\e389"; }

.fasds.fa-road-circle-check::after, .fa-sharp-duotone.fa-road-circle-check::after {
  content: "\e564\e564"; }

.fasds.fa-dice-five::after, .fa-sharp-duotone.fa-dice-five::after {
  content: "\f523\f523"; }

.fasds.fa-octagon-minus::after, .fa-sharp-duotone.fa-octagon-minus::after {
  content: "\f308\f308"; }

.fasds.fa-minus-octagon::after, .fa-sharp-duotone.fa-minus-octagon::after {
  content: "\f308\f308"; }

.fasds.fa-square-rss::after, .fa-sharp-duotone.fa-square-rss::after {
  content: "\f143\f143"; }

.fasds.fa-rss-square::after, .fa-sharp-duotone.fa-rss-square::after {
  content: "\f143\f143"; }

.fasds.fa-face-zany::after, .fa-sharp-duotone.fa-face-zany::after {
  content: "\e3a4\e3a4"; }

.fasds.fa-tricycle::after, .fa-sharp-duotone.fa-tricycle::after {
  content: "\e5c3\e5c3"; }

.fasds.fa-land-mine-on::after, .fa-sharp-duotone.fa-land-mine-on::after {
  content: "\e51b\e51b"; }

.fasds.fa-square-arrow-up-left::after, .fa-sharp-duotone.fa-square-arrow-up-left::after {
  content: "\e263\e263"; }

.fasds.fa-i-cursor::after, .fa-sharp-duotone.fa-i-cursor::after {
  content: "\f246\f246"; }

.fasds.fa-chart-mixed-up-circle-dollar::after, .fa-sharp-duotone.fa-chart-mixed-up-circle-dollar::after {
  content: "\e5d9\e5d9"; }

.fasds.fa-salt-shaker::after, .fa-sharp-duotone.fa-salt-shaker::after {
  content: "\e446\e446"; }

.fasds.fa-stamp::after, .fa-sharp-duotone.fa-stamp::after {
  content: "\f5bf\f5bf"; }

.fasds.fa-file-plus::after, .fa-sharp-duotone.fa-file-plus::after {
  content: "\f319\f319"; }

.fasds.fa-draw-square::after, .fa-sharp-duotone.fa-draw-square::after {
  content: "\f5ef\f5ef"; }

.fasds.fa-toilet-paper-under-slash::after, .fa-sharp-duotone.fa-toilet-paper-under-slash::after {
  content: "\e2a1\e2a1"; }

.fasds.fa-toilet-paper-reverse-slash::after, .fa-sharp-duotone.fa-toilet-paper-reverse-slash::after {
  content: "\e2a1\e2a1"; }

.fasds.fa-stairs::after, .fa-sharp-duotone.fa-stairs::after {
  content: "\e289\e289"; }

.fasds.fa-drone-front::after, .fa-sharp-duotone.fa-drone-front::after {
  content: "\f860\f860"; }

.fasds.fa-drone-alt::after, .fa-sharp-duotone.fa-drone-alt::after {
  content: "\f860\f860"; }

.fasds.fa-glass-empty::after, .fa-sharp-duotone.fa-glass-empty::after {
  content: "\e191\e191"; }

.fasds.fa-dial-high::after, .fa-sharp-duotone.fa-dial-high::after {
  content: "\e15c\e15c"; }

.fasds.fa-user-helmet-safety::after, .fa-sharp-duotone.fa-user-helmet-safety::after {
  content: "\f82c\f82c"; }

.fasds.fa-user-construction::after, .fa-sharp-duotone.fa-user-construction::after {
  content: "\f82c\f82c"; }

.fasds.fa-user-hard-hat::after, .fa-sharp-duotone.fa-user-hard-hat::after {
  content: "\f82c\f82c"; }

.fasds.fa-i::after, .fa-sharp-duotone.fa-i::after {
  content: "\49\49"; }

.fasds.fa-hryvnia-sign::after, .fa-sharp-duotone.fa-hryvnia-sign::after {
  content: "\f6f2\f6f2"; }

.fasds.fa-hryvnia::after, .fa-sharp-duotone.fa-hryvnia::after {
  content: "\f6f2\f6f2"; }

.fasds.fa-arrow-down-left-and-arrow-up-right-to-center::after, .fa-sharp-duotone.fa-arrow-down-left-and-arrow-up-right-to-center::after {
  content: "\e092\e092"; }

.fasds.fa-pills::after, .fa-sharp-duotone.fa-pills::after {
  content: "\f484\f484"; }

.fasds.fa-face-grin-wide::after, .fa-sharp-duotone.fa-face-grin-wide::after {
  content: "\f581\f581"; }

.fasds.fa-grin-alt::after, .fa-sharp-duotone.fa-grin-alt::after {
  content: "\f581\f581"; }

.fasds.fa-tooth::after, .fa-sharp-duotone.fa-tooth::after {
  content: "\f5c9\f5c9"; }

.fasds.fa-basketball-hoop::after, .fa-sharp-duotone.fa-basketball-hoop::after {
  content: "\f435\f435"; }

.fasds.fa-objects-align-bottom::after, .fa-sharp-duotone.fa-objects-align-bottom::after {
  content: "\e3bb\e3bb"; }

.fasds.fa-v::after, .fa-sharp-duotone.fa-v::after {
  content: "\56\56"; }

.fasds.fa-sparkles::after, .fa-sharp-duotone.fa-sparkles::after {
  content: "\f890\f890"; }

.fasds.fa-squid::after, .fa-sharp-duotone.fa-squid::after {
  content: "\e450\e450"; }

.fasds.fa-leafy-green::after, .fa-sharp-duotone.fa-leafy-green::after {
  content: "\e41d\e41d"; }

.fasds.fa-circle-arrow-up-right::after, .fa-sharp-duotone.fa-circle-arrow-up-right::after {
  content: "\e0fc\e0fc"; }

.fasds.fa-calendars::after, .fa-sharp-duotone.fa-calendars::after {
  content: "\e0d7\e0d7"; }

.fasds.fa-bangladeshi-taka-sign::after, .fa-sharp-duotone.fa-bangladeshi-taka-sign::after {
  content: "\e2e6\e2e6"; }

.fasds.fa-bicycle::after, .fa-sharp-duotone.fa-bicycle::after {
  content: "\f206\f206"; }

.fasds.fa-hammer-war::after, .fa-sharp-duotone.fa-hammer-war::after {
  content: "\f6e4\f6e4"; }

.fasds.fa-circle-d::after, .fa-sharp-duotone.fa-circle-d::after {
  content: "\e104\e104"; }

.fasds.fa-spider-black-widow::after, .fa-sharp-duotone.fa-spider-black-widow::after {
  content: "\f718\f718"; }

.fasds.fa-staff-snake::after, .fa-sharp-duotone.fa-staff-snake::after {
  content: "\e579\e579"; }

.fasds.fa-rod-asclepius::after, .fa-sharp-duotone.fa-rod-asclepius::after {
  content: "\e579\e579"; }

.fasds.fa-rod-snake::after, .fa-sharp-duotone.fa-rod-snake::after {
  content: "\e579\e579"; }

.fasds.fa-staff-aesculapius::after, .fa-sharp-duotone.fa-staff-aesculapius::after {
  content: "\e579\e579"; }

.fasds.fa-pear::after, .fa-sharp-duotone.fa-pear::after {
  content: "\e20c\e20c"; }

.fasds.fa-head-side-cough-slash::after, .fa-sharp-duotone.fa-head-side-cough-slash::after {
  content: "\e062\e062"; }

.fasds.fa-file-mov::after, .fa-sharp-duotone.fa-file-mov::after {
  content: "\e647\e647"; }

.fasds.fa-triangle::after, .fa-sharp-duotone.fa-triangle::after {
  content: "\f2ec\f2ec"; }

.fasds.fa-apartment::after, .fa-sharp-duotone.fa-apartment::after {
  content: "\e468\e468"; }

.fasds.fa-truck-medical::after, .fa-sharp-duotone.fa-truck-medical::after {
  content: "\f0f9\f0f9"; }

.fasds.fa-ambulance::after, .fa-sharp-duotone.fa-ambulance::after {
  content: "\f0f9\f0f9"; }

.fasds.fa-pepper::after, .fa-sharp-duotone.fa-pepper::after {
  content: "\e432\e432"; }

.fasds.fa-piano::after, .fa-sharp-duotone.fa-piano::after {
  content: "\f8d4\f8d4"; }

.fasds.fa-gun-squirt::after, .fa-sharp-duotone.fa-gun-squirt::after {
  content: "\e19d\e19d"; }

.fasds.fa-wheat-awn-circle-exclamation::after, .fa-sharp-duotone.fa-wheat-awn-circle-exclamation::after {
  content: "\e598\e598"; }

.fasds.fa-snowman::after, .fa-sharp-duotone.fa-snowman::after {
  content: "\f7d0\f7d0"; }

.fasds.fa-user-alien::after, .fa-sharp-duotone.fa-user-alien::after {
  content: "\e04a\e04a"; }

.fasds.fa-shield-check::after, .fa-sharp-duotone.fa-shield-check::after {
  content: "\f2f7\f2f7"; }

.fasds.fa-mortar-pestle::after, .fa-sharp-duotone.fa-mortar-pestle::after {
  content: "\f5a7\f5a7"; }

.fasds.fa-road-barrier::after, .fa-sharp-duotone.fa-road-barrier::after {
  content: "\e562\e562"; }

.fasds.fa-chart-candlestick::after, .fa-sharp-duotone.fa-chart-candlestick::after {
  content: "\e0e2\e0e2"; }

.fasds.fa-briefcase-blank::after, .fa-sharp-duotone.fa-briefcase-blank::after {
  content: "\e0c8\e0c8"; }

.fasds.fa-school::after, .fa-sharp-duotone.fa-school::after {
  content: "\f549\f549"; }

.fasds.fa-igloo::after, .fa-sharp-duotone.fa-igloo::after {
  content: "\f7ae\f7ae"; }

.fasds.fa-bracket-round::after, .fa-sharp-duotone.fa-bracket-round::after {
  content: "\28\28"; }

.fasds.fa-parenthesis::after, .fa-sharp-duotone.fa-parenthesis::after {
  content: "\28\28"; }

.fasds.fa-joint::after, .fa-sharp-duotone.fa-joint::after {
  content: "\f595\f595"; }

.fasds.fa-horse-saddle::after, .fa-sharp-duotone.fa-horse-saddle::after {
  content: "\f8c3\f8c3"; }

.fasds.fa-mug-marshmallows::after, .fa-sharp-duotone.fa-mug-marshmallows::after {
  content: "\f7b7\f7b7"; }

.fasds.fa-filters::after, .fa-sharp-duotone.fa-filters::after {
  content: "\e17e\e17e"; }

.fasds.fa-bell-on::after, .fa-sharp-duotone.fa-bell-on::after {
  content: "\f8fa\f8fa"; }

.fasds.fa-angle-right::after, .fa-sharp-duotone.fa-angle-right::after {
  content: "\f105\f105"; }

.fasds.fa-dial-med::after, .fa-sharp-duotone.fa-dial-med::after {
  content: "\e15f\e15f"; }

.fasds.fa-horse::after, .fa-sharp-duotone.fa-horse::after {
  content: "\f6f0\f6f0"; }

.fasds.fa-q::after, .fa-sharp-duotone.fa-q::after {
  content: "\51\51"; }

.fasds.fa-monitor-waveform::after, .fa-sharp-duotone.fa-monitor-waveform::after {
  content: "\f611\f611"; }

.fasds.fa-monitor-heart-rate::after, .fa-sharp-duotone.fa-monitor-heart-rate::after {
  content: "\f611\f611"; }

.fasds.fa-link-simple::after, .fa-sharp-duotone.fa-link-simple::after {
  content: "\e1cd\e1cd"; }

.fasds.fa-whistle::after, .fa-sharp-duotone.fa-whistle::after {
  content: "\f460\f460"; }

.fasds.fa-g::after, .fa-sharp-duotone.fa-g::after {
  content: "\47\47"; }

.fasds.fa-wine-glass-crack::after, .fa-sharp-duotone.fa-wine-glass-crack::after {
  content: "\f4bb\f4bb"; }

.fasds.fa-fragile::after, .fa-sharp-duotone.fa-fragile::after {
  content: "\f4bb\f4bb"; }

.fasds.fa-slot-machine::after, .fa-sharp-duotone.fa-slot-machine::after {
  content: "\e3ce\e3ce"; }

.fasds.fa-notes-medical::after, .fa-sharp-duotone.fa-notes-medical::after {
  content: "\f481\f481"; }

.fasds.fa-car-wash::after, .fa-sharp-duotone.fa-car-wash::after {
  content: "\f5e6\f5e6"; }

.fasds.fa-escalator::after, .fa-sharp-duotone.fa-escalator::after {
  content: "\e171\e171"; }

.fasds.fa-comment-image::after, .fa-sharp-duotone.fa-comment-image::after {
  content: "\e148\e148"; }

.fasds.fa-temperature-half::after, .fa-sharp-duotone.fa-temperature-half::after {
  content: "\f2c9\f2c9"; }

.fasds.fa-temperature-2::after, .fa-sharp-duotone.fa-temperature-2::after {
  content: "\f2c9\f2c9"; }

.fasds.fa-thermometer-2::after, .fa-sharp-duotone.fa-thermometer-2::after {
  content: "\f2c9\f2c9"; }

.fasds.fa-thermometer-half::after, .fa-sharp-duotone.fa-thermometer-half::after {
  content: "\f2c9\f2c9"; }

.fasds.fa-dong-sign::after, .fa-sharp-duotone.fa-dong-sign::after {
  content: "\e169\e169"; }

.fasds.fa-donut::after, .fa-sharp-duotone.fa-donut::after {
  content: "\e406\e406"; }

.fasds.fa-doughnut::after, .fa-sharp-duotone.fa-doughnut::after {
  content: "\e406\e406"; }

.fasds.fa-capsules::after, .fa-sharp-duotone.fa-capsules::after {
  content: "\f46b\f46b"; }

.fasds.fa-poo-storm::after, .fa-sharp-duotone.fa-poo-storm::after {
  content: "\f75a\f75a"; }

.fasds.fa-poo-bolt::after, .fa-sharp-duotone.fa-poo-bolt::after {
  content: "\f75a\f75a"; }

.fasds.fa-tally-1::after, .fa-sharp-duotone.fa-tally-1::after {
  content: "\e294\e294"; }

.fasds.fa-file-vector::after, .fa-sharp-duotone.fa-file-vector::after {
  content: "\e64c\e64c"; }

.fasds.fa-face-frown-open::after, .fa-sharp-duotone.fa-face-frown-open::after {
  content: "\f57a\f57a"; }

.fasds.fa-frown-open::after, .fa-sharp-duotone.fa-frown-open::after {
  content: "\f57a\f57a"; }

.fasds.fa-square-dashed::after, .fa-sharp-duotone.fa-square-dashed::after {
  content: "\e269\e269"; }

.fasds.fa-bag-shopping-plus::after, .fa-sharp-duotone.fa-bag-shopping-plus::after {
  content: "\e651\e651"; }

.fasds.fa-square-j::after, .fa-sharp-duotone.fa-square-j::after {
  content: "\e273\e273"; }

.fasds.fa-hand-point-up::after, .fa-sharp-duotone.fa-hand-point-up::after {
  content: "\f0a6\f0a6"; }

.fasds.fa-money-bill::after, .fa-sharp-duotone.fa-money-bill::after {
  content: "\f0d6\f0d6"; }

.fasds.fa-arrow-up-big-small::after, .fa-sharp-duotone.fa-arrow-up-big-small::after {
  content: "\f88e\f88e"; }

.fasds.fa-sort-size-up::after, .fa-sharp-duotone.fa-sort-size-up::after {
  content: "\f88e\f88e"; }

.fasds.fa-barcode-read::after, .fa-sharp-duotone.fa-barcode-read::after {
  content: "\f464\f464"; }

.fasds.fa-baguette::after, .fa-sharp-duotone.fa-baguette::after {
  content: "\e3d8\e3d8"; }

.fasds.fa-bowl-soft-serve::after, .fa-sharp-duotone.fa-bowl-soft-serve::after {
  content: "\e46b\e46b"; }

.fasds.fa-face-holding-back-tears::after, .fa-sharp-duotone.fa-face-holding-back-tears::after {
  content: "\e482\e482"; }

.fasds.fa-square-up::after, .fa-sharp-duotone.fa-square-up::after {
  content: "\f353\f353"; }

.fasds.fa-arrow-alt-square-up::after, .fa-sharp-duotone.fa-arrow-alt-square-up::after {
  content: "\f353\f353"; }

.fasds.fa-train-subway-tunnel::after, .fa-sharp-duotone.fa-train-subway-tunnel::after {
  content: "\e2a3\e2a3"; }

.fasds.fa-subway-tunnel::after, .fa-sharp-duotone.fa-subway-tunnel::after {
  content: "\e2a3\e2a3"; }

.fasds.fa-square-exclamation::after, .fa-sharp-duotone.fa-square-exclamation::after {
  content: "\f321\f321"; }

.fasds.fa-exclamation-square::after, .fa-sharp-duotone.fa-exclamation-square::after {
  content: "\f321\f321"; }

.fasds.fa-semicolon::after, .fa-sharp-duotone.fa-semicolon::after {
  content: "\3b\3b"; }

.fasds.fa-bookmark::after, .fa-sharp-duotone.fa-bookmark::after {
  content: "\f02e\f02e"; }

.fasds.fa-fan-table::after, .fa-sharp-duotone.fa-fan-table::after {
  content: "\e004\e004"; }

.fasds.fa-align-justify::after, .fa-sharp-duotone.fa-align-justify::after {
  content: "\f039\f039"; }

.fasds.fa-battery-low::after, .fa-sharp-duotone.fa-battery-low::after {
  content: "\e0b1\e0b1"; }

.fasds.fa-battery-1::after, .fa-sharp-duotone.fa-battery-1::after {
  content: "\e0b1\e0b1"; }

.fasds.fa-credit-card-front::after, .fa-sharp-duotone.fa-credit-card-front::after {
  content: "\f38a\f38a"; }

.fasds.fa-brain-arrow-curved-right::after, .fa-sharp-duotone.fa-brain-arrow-curved-right::after {
  content: "\f677\f677"; }

.fasds.fa-mind-share::after, .fa-sharp-duotone.fa-mind-share::after {
  content: "\f677\f677"; }

.fasds.fa-umbrella-beach::after, .fa-sharp-duotone.fa-umbrella-beach::after {
  content: "\f5ca\f5ca"; }

.fasds.fa-helmet-un::after, .fa-sharp-duotone.fa-helmet-un::after {
  content: "\e503\e503"; }

.fasds.fa-location-smile::after, .fa-sharp-duotone.fa-location-smile::after {
  content: "\f60d\f60d"; }

.fasds.fa-map-marker-smile::after, .fa-sharp-duotone.fa-map-marker-smile::after {
  content: "\f60d\f60d"; }

.fasds.fa-arrow-left-to-line::after, .fa-sharp-duotone.fa-arrow-left-to-line::after {
  content: "\f33e\f33e"; }

.fasds.fa-arrow-to-left::after, .fa-sharp-duotone.fa-arrow-to-left::after {
  content: "\f33e\f33e"; }

.fasds.fa-bullseye::after, .fa-sharp-duotone.fa-bullseye::after {
  content: "\f140\f140"; }

.fasds.fa-sushi::after, .fa-sharp-duotone.fa-sushi::after {
  content: "\e48a\e48a"; }

.fasds.fa-nigiri::after, .fa-sharp-duotone.fa-nigiri::after {
  content: "\e48a\e48a"; }

.fasds.fa-message-captions::after, .fa-sharp-duotone.fa-message-captions::after {
  content: "\e1de\e1de"; }

.fasds.fa-comment-alt-captions::after, .fa-sharp-duotone.fa-comment-alt-captions::after {
  content: "\e1de\e1de"; }

.fasds.fa-trash-list::after, .fa-sharp-duotone.fa-trash-list::after {
  content: "\e2b1\e2b1"; }

.fasds.fa-bacon::after, .fa-sharp-duotone.fa-bacon::after {
  content: "\f7e5\f7e5"; }

.fasds.fa-option::after, .fa-sharp-duotone.fa-option::after {
  content: "\e318\e318"; }

.fasds.fa-raccoon::after, .fa-sharp-duotone.fa-raccoon::after {
  content: "\e613\e613"; }

.fasds.fa-hand-point-down::after, .fa-sharp-duotone.fa-hand-point-down::after {
  content: "\f0a7\f0a7"; }

.fasds.fa-arrow-up-from-bracket::after, .fa-sharp-duotone.fa-arrow-up-from-bracket::after {
  content: "\e09a\e09a"; }

.fasds.fa-head-side-gear::after, .fa-sharp-duotone.fa-head-side-gear::after {
  content: "\e611\e611"; }

.fasds.fa-trash-plus::after, .fa-sharp-duotone.fa-trash-plus::after {
  content: "\e2b2\e2b2"; }

.fasds.fa-file-cad::after, .fa-sharp-duotone.fa-file-cad::after {
  content: "\e672\e672"; }

.fasds.fa-objects-align-top::after, .fa-sharp-duotone.fa-objects-align-top::after {
  content: "\e3c0\e3c0"; }

.fasds.fa-folder::after, .fa-sharp-duotone.fa-folder::after {
  content: "\f07b\f07b"; }

.fasds.fa-folder-blank::after, .fa-sharp-duotone.fa-folder-blank::after {
  content: "\f07b\f07b"; }

.fasds.fa-face-anxious-sweat::after, .fa-sharp-duotone.fa-face-anxious-sweat::after {
  content: "\e36a\e36a"; }

.fasds.fa-credit-card-blank::after, .fa-sharp-duotone.fa-credit-card-blank::after {
  content: "\f389\f389"; }

.fasds.fa-file-waveform::after, .fa-sharp-duotone.fa-file-waveform::after {
  content: "\f478\f478"; }

.fasds.fa-file-medical-alt::after, .fa-sharp-duotone.fa-file-medical-alt::after {
  content: "\f478\f478"; }

.fasds.fa-microchip-ai::after, .fa-sharp-duotone.fa-microchip-ai::after {
  content: "\e1ec\e1ec"; }

.fasds.fa-mug::after, .fa-sharp-duotone.fa-mug::after {
  content: "\f874\f874"; }

.fasds.fa-plane-up-slash::after, .fa-sharp-duotone.fa-plane-up-slash::after {
  content: "\e22e\e22e"; }

.fasds.fa-radiation::after, .fa-sharp-duotone.fa-radiation::after {
  content: "\f7b9\f7b9"; }

.fasds.fa-pen-circle::after, .fa-sharp-duotone.fa-pen-circle::after {
  content: "\e20e\e20e"; }

.fasds.fa-bag-seedling::after, .fa-sharp-duotone.fa-bag-seedling::after {
  content: "\e5f2\e5f2"; }

.fasds.fa-chart-simple::after, .fa-sharp-duotone.fa-chart-simple::after {
  content: "\e473\e473"; }

.fasds.fa-crutches::after, .fa-sharp-duotone.fa-crutches::after {
  content: "\f7f8\f7f8"; }

.fasds.fa-circle-parking::after, .fa-sharp-duotone.fa-circle-parking::after {
  content: "\f615\f615"; }

.fasds.fa-parking-circle::after, .fa-sharp-duotone.fa-parking-circle::after {
  content: "\f615\f615"; }

.fasds.fa-mars-stroke::after, .fa-sharp-duotone.fa-mars-stroke::after {
  content: "\f229\f229"; }

.fasds.fa-leaf-oak::after, .fa-sharp-duotone.fa-leaf-oak::after {
  content: "\f6f7\f6f7"; }

.fasds.fa-square-bolt::after, .fa-sharp-duotone.fa-square-bolt::after {
  content: "\e265\e265"; }

.fasds.fa-vial::after, .fa-sharp-duotone.fa-vial::after {
  content: "\f492\f492"; }

.fasds.fa-gauge::after, .fa-sharp-duotone.fa-gauge::after {
  content: "\f624\f624"; }

.fasds.fa-dashboard::after, .fa-sharp-duotone.fa-dashboard::after {
  content: "\f624\f624"; }

.fasds.fa-gauge-med::after, .fa-sharp-duotone.fa-gauge-med::after {
  content: "\f624\f624"; }

.fasds.fa-tachometer-alt-average::after, .fa-sharp-duotone.fa-tachometer-alt-average::after {
  content: "\f624\f624"; }

.fasds.fa-wand-magic-sparkles::after, .fa-sharp-duotone.fa-wand-magic-sparkles::after {
  content: "\e2ca\e2ca"; }

.fasds.fa-magic-wand-sparkles::after, .fa-sharp-duotone.fa-magic-wand-sparkles::after {
  content: "\e2ca\e2ca"; }

.fasds.fa-lambda::after, .fa-sharp-duotone.fa-lambda::after {
  content: "\f66e\f66e"; }

.fasds.fa-e::after, .fa-sharp-duotone.fa-e::after {
  content: "\45\45"; }

.fasds.fa-pizza::after, .fa-sharp-duotone.fa-pizza::after {
  content: "\f817\f817"; }

.fasds.fa-bowl-chopsticks-noodles::after, .fa-sharp-duotone.fa-bowl-chopsticks-noodles::after {
  content: "\e2ea\e2ea"; }

.fasds.fa-h3::after, .fa-sharp-duotone.fa-h3::after {
  content: "\f315\f315"; }

.fasds.fa-pen-clip::after, .fa-sharp-duotone.fa-pen-clip::after {
  content: "\f305\f305"; }

.fasds.fa-pen-alt::after, .fa-sharp-duotone.fa-pen-alt::after {
  content: "\f305\f305"; }

.fasds.fa-bridge-circle-exclamation::after, .fa-sharp-duotone.fa-bridge-circle-exclamation::after {
  content: "\e4ca\e4ca"; }

.fasds.fa-badge-percent::after, .fa-sharp-duotone.fa-badge-percent::after {
  content: "\f646\f646"; }

.fasds.fa-rotate-reverse::after, .fa-sharp-duotone.fa-rotate-reverse::after {
  content: "\e631\e631"; }

.fasds.fa-user::after, .fa-sharp-duotone.fa-user::after {
  content: "\f007\f007"; }

.fasds.fa-sensor::after, .fa-sharp-duotone.fa-sensor::after {
  content: "\e028\e028"; }

.fasds.fa-comma::after, .fa-sharp-duotone.fa-comma::after {
  content: "\2c\2c"; }

.fasds.fa-school-circle-check::after, .fa-sharp-duotone.fa-school-circle-check::after {
  content: "\e56b\e56b"; }

.fasds.fa-toilet-paper-under::after, .fa-sharp-duotone.fa-toilet-paper-under::after {
  content: "\e2a0\e2a0"; }

.fasds.fa-toilet-paper-reverse::after, .fa-sharp-duotone.fa-toilet-paper-reverse::after {
  content: "\e2a0\e2a0"; }

.fasds.fa-light-emergency::after, .fa-sharp-duotone.fa-light-emergency::after {
  content: "\e41f\e41f"; }

.fasds.fa-arrow-down-to-arc::after, .fa-sharp-duotone.fa-arrow-down-to-arc::after {
  content: "\e4ae\e4ae"; }

.fasds.fa-dumpster::after, .fa-sharp-duotone.fa-dumpster::after {
  content: "\f793\f793"; }

.fasds.fa-van-shuttle::after, .fa-sharp-duotone.fa-van-shuttle::after {
  content: "\f5b6\f5b6"; }

.fasds.fa-shuttle-van::after, .fa-sharp-duotone.fa-shuttle-van::after {
  content: "\f5b6\f5b6"; }

.fasds.fa-building-user::after, .fa-sharp-duotone.fa-building-user::after {
  content: "\e4da\e4da"; }

.fasds.fa-light-switch::after, .fa-sharp-duotone.fa-light-switch::after {
  content: "\e017\e017"; }

.fasds.fa-square-caret-left::after, .fa-sharp-duotone.fa-square-caret-left::after {
  content: "\f191\f191"; }

.fasds.fa-caret-square-left::after, .fa-sharp-duotone.fa-caret-square-left::after {
  content: "\f191\f191"; }

.fasds.fa-highlighter::after, .fa-sharp-duotone.fa-highlighter::after {
  content: "\f591\f591"; }

.fasds.fa-wave-pulse::after, .fa-sharp-duotone.fa-wave-pulse::after {
  content: "\f5f8\f5f8"; }

.fasds.fa-heart-rate::after, .fa-sharp-duotone.fa-heart-rate::after {
  content: "\f5f8\f5f8"; }

.fasds.fa-key::after, .fa-sharp-duotone.fa-key::after {
  content: "\f084\f084"; }

.fasds.fa-arrow-left-to-bracket::after, .fa-sharp-duotone.fa-arrow-left-to-bracket::after {
  content: "\e669\e669"; }

.fasds.fa-hat-santa::after, .fa-sharp-duotone.fa-hat-santa::after {
  content: "\f7a7\f7a7"; }

.fasds.fa-tamale::after, .fa-sharp-duotone.fa-tamale::after {
  content: "\e451\e451"; }

.fasds.fa-box-check::after, .fa-sharp-duotone.fa-box-check::after {
  content: "\f467\f467"; }

.fasds.fa-bullhorn::after, .fa-sharp-duotone.fa-bullhorn::after {
  content: "\f0a1\f0a1"; }

.fasds.fa-steak::after, .fa-sharp-duotone.fa-steak::after {
  content: "\f824\f824"; }

.fasds.fa-location-crosshairs-slash::after, .fa-sharp-duotone.fa-location-crosshairs-slash::after {
  content: "\f603\f603"; }

.fasds.fa-location-slash::after, .fa-sharp-duotone.fa-location-slash::after {
  content: "\f603\f603"; }

.fasds.fa-person-dolly::after, .fa-sharp-duotone.fa-person-dolly::after {
  content: "\f4d0\f4d0"; }

.fasds.fa-globe::after, .fa-sharp-duotone.fa-globe::after {
  content: "\f0ac\f0ac"; }

.fasds.fa-synagogue::after, .fa-sharp-duotone.fa-synagogue::after {
  content: "\f69b\f69b"; }

.fasds.fa-file-chart-column::after, .fa-sharp-duotone.fa-file-chart-column::after {
  content: "\f659\f659"; }

.fasds.fa-file-chart-line::after, .fa-sharp-duotone.fa-file-chart-line::after {
  content: "\f659\f659"; }

.fasds.fa-person-half-dress::after, .fa-sharp-duotone.fa-person-half-dress::after {
  content: "\e548\e548"; }

.fasds.fa-folder-image::after, .fa-sharp-duotone.fa-folder-image::after {
  content: "\e18a\e18a"; }

.fasds.fa-calendar-pen::after, .fa-sharp-duotone.fa-calendar-pen::after {
  content: "\f333\f333"; }

.fasds.fa-calendar-edit::after, .fa-sharp-duotone.fa-calendar-edit::after {
  content: "\f333\f333"; }

.fasds.fa-road-bridge::after, .fa-sharp-duotone.fa-road-bridge::after {
  content: "\e563\e563"; }

.fasds.fa-face-smile-tear::after, .fa-sharp-duotone.fa-face-smile-tear::after {
  content: "\e393\e393"; }

.fasds.fa-message-plus::after, .fa-sharp-duotone.fa-message-plus::after {
  content: "\f4a8\f4a8"; }

.fasds.fa-comment-alt-plus::after, .fa-sharp-duotone.fa-comment-alt-plus::after {
  content: "\f4a8\f4a8"; }

.fasds.fa-location-arrow::after, .fa-sharp-duotone.fa-location-arrow::after {
  content: "\f124\f124"; }

.fasds.fa-c::after, .fa-sharp-duotone.fa-c::after {
  content: "\43\43"; }

.fasds.fa-tablet-button::after, .fa-sharp-duotone.fa-tablet-button::after {
  content: "\f10a\f10a"; }

.fasds.fa-person-dress-fairy::after, .fa-sharp-duotone.fa-person-dress-fairy::after {
  content: "\e607\e607"; }

.fasds.fa-rectangle-history-circle-user::after, .fa-sharp-duotone.fa-rectangle-history-circle-user::after {
  content: "\e4a4\e4a4"; }

.fasds.fa-building-lock::after, .fa-sharp-duotone.fa-building-lock::after {
  content: "\e4d6\e4d6"; }

.fasds.fa-chart-line-up::after, .fa-sharp-duotone.fa-chart-line-up::after {
  content: "\e0e5\e0e5"; }

.fasds.fa-mailbox::after, .fa-sharp-duotone.fa-mailbox::after {
  content: "\f813\f813"; }

.fasds.fa-sign-posts::after, .fa-sharp-duotone.fa-sign-posts::after {
  content: "\e625\e625"; }

.fasds.fa-truck-bolt::after, .fa-sharp-duotone.fa-truck-bolt::after {
  content: "\e3d0\e3d0"; }

.fasds.fa-pizza-slice::after, .fa-sharp-duotone.fa-pizza-slice::after {
  content: "\f818\f818"; }

.fasds.fa-money-bill-wave::after, .fa-sharp-duotone.fa-money-bill-wave::after {
  content: "\f53a\f53a"; }

.fasds.fa-chart-area::after, .fa-sharp-duotone.fa-chart-area::after {
  content: "\f1fe\f1fe"; }

.fasds.fa-area-chart::after, .fa-sharp-duotone.fa-area-chart::after {
  content: "\f1fe\f1fe"; }

.fasds.fa-house-flag::after, .fa-sharp-duotone.fa-house-flag::after {
  content: "\e50d\e50d"; }

.fasds.fa-circle-three-quarters-stroke::after, .fa-sharp-duotone.fa-circle-three-quarters-stroke::after {
  content: "\e5d4\e5d4"; }

.fasds.fa-person-circle-minus::after, .fa-sharp-duotone.fa-person-circle-minus::after {
  content: "\e540\e540"; }

.fasds.fa-scalpel::after, .fa-sharp-duotone.fa-scalpel::after {
  content: "\f61d\f61d"; }

.fasds.fa-ban::after, .fa-sharp-duotone.fa-ban::after {
  content: "\f05e\f05e"; }

.fasds.fa-cancel::after, .fa-sharp-duotone.fa-cancel::after {
  content: "\f05e\f05e"; }

.fasds.fa-bell-exclamation::after, .fa-sharp-duotone.fa-bell-exclamation::after {
  content: "\f848\f848"; }

.fasds.fa-circle-bookmark::after, .fa-sharp-duotone.fa-circle-bookmark::after {
  content: "\e100\e100"; }

.fasds.fa-bookmark-circle::after, .fa-sharp-duotone.fa-bookmark-circle::after {
  content: "\e100\e100"; }

.fasds.fa-egg-fried::after, .fa-sharp-duotone.fa-egg-fried::after {
  content: "\f7fc\f7fc"; }

.fasds.fa-face-weary::after, .fa-sharp-duotone.fa-face-weary::after {
  content: "\e3a1\e3a1"; }

.fasds.fa-uniform-martial-arts::after, .fa-sharp-duotone.fa-uniform-martial-arts::after {
  content: "\e3d1\e3d1"; }

.fasds.fa-camera-rotate::after, .fa-sharp-duotone.fa-camera-rotate::after {
  content: "\e0d8\e0d8"; }

.fasds.fa-sun-dust::after, .fa-sharp-duotone.fa-sun-dust::after {
  content: "\f764\f764"; }

.fasds.fa-comment-text::after, .fa-sharp-duotone.fa-comment-text::after {
  content: "\e14d\e14d"; }

.fasds.fa-spray-can-sparkles::after, .fa-sharp-duotone.fa-spray-can-sparkles::after {
  content: "\f5d0\f5d0"; }

.fasds.fa-air-freshener::after, .fa-sharp-duotone.fa-air-freshener::after {
  content: "\f5d0\f5d0"; }

.fasds.fa-signal-bars::after, .fa-sharp-duotone.fa-signal-bars::after {
  content: "\f690\f690"; }

.fasds.fa-signal-alt::after, .fa-sharp-duotone.fa-signal-alt::after {
  content: "\f690\f690"; }

.fasds.fa-signal-alt-4::after, .fa-sharp-duotone.fa-signal-alt-4::after {
  content: "\f690\f690"; }

.fasds.fa-signal-bars-strong::after, .fa-sharp-duotone.fa-signal-bars-strong::after {
  content: "\f690\f690"; }

.fasds.fa-diamond-exclamation::after, .fa-sharp-duotone.fa-diamond-exclamation::after {
  content: "\e405\e405"; }

.fasds.fa-star::after, .fa-sharp-duotone.fa-star::after {
  content: "\f005\f005"; }

.fasds.fa-dial-min::after, .fa-sharp-duotone.fa-dial-min::after {
  content: "\e161\e161"; }

.fasds.fa-repeat::after, .fa-sharp-duotone.fa-repeat::after {
  content: "\f363\f363"; }

.fasds.fa-cross::after, .fa-sharp-duotone.fa-cross::after {
  content: "\f654\f654"; }

.fasds.fa-page-caret-down::after, .fa-sharp-duotone.fa-page-caret-down::after {
  content: "\e429\e429"; }

.fasds.fa-file-caret-down::after, .fa-sharp-duotone.fa-file-caret-down::after {
  content: "\e429\e429"; }

.fasds.fa-box::after, .fa-sharp-duotone.fa-box::after {
  content: "\f466\f466"; }

.fasds.fa-venus-mars::after, .fa-sharp-duotone.fa-venus-mars::after {
  content: "\f228\f228"; }

.fasds.fa-clock-seven-thirty::after, .fa-sharp-duotone.fa-clock-seven-thirty::after {
  content: "\e351\e351"; }

.fasds.fa-arrow-pointer::after, .fa-sharp-duotone.fa-arrow-pointer::after {
  content: "\f245\f245"; }

.fasds.fa-mouse-pointer::after, .fa-sharp-duotone.fa-mouse-pointer::after {
  content: "\f245\f245"; }

.fasds.fa-clock-four-thirty::after, .fa-sharp-duotone.fa-clock-four-thirty::after {
  content: "\e34b\e34b"; }

.fasds.fa-signal-bars-good::after, .fa-sharp-duotone.fa-signal-bars-good::after {
  content: "\f693\f693"; }

.fasds.fa-signal-alt-3::after, .fa-sharp-duotone.fa-signal-alt-3::after {
  content: "\f693\f693"; }

.fasds.fa-cactus::after, .fa-sharp-duotone.fa-cactus::after {
  content: "\f8a7\f8a7"; }

.fasds.fa-lightbulb-gear::after, .fa-sharp-duotone.fa-lightbulb-gear::after {
  content: "\e5fd\e5fd"; }

.fasds.fa-maximize::after, .fa-sharp-duotone.fa-maximize::after {
  content: "\f31e\f31e"; }

.fasds.fa-expand-arrows-alt::after, .fa-sharp-duotone.fa-expand-arrows-alt::after {
  content: "\f31e\f31e"; }

.fasds.fa-charging-station::after, .fa-sharp-duotone.fa-charging-station::after {
  content: "\f5e7\f5e7"; }

.fasds.fa-shapes::after, .fa-sharp-duotone.fa-shapes::after {
  content: "\f61f\f61f"; }

.fasds.fa-triangle-circle-square::after, .fa-sharp-duotone.fa-triangle-circle-square::after {
  content: "\f61f\f61f"; }

.fasds.fa-plane-tail::after, .fa-sharp-duotone.fa-plane-tail::after {
  content: "\e22c\e22c"; }

.fasds.fa-gauge-simple-max::after, .fa-sharp-duotone.fa-gauge-simple-max::after {
  content: "\f62b\f62b"; }

.fasds.fa-tachometer-fastest::after, .fa-sharp-duotone.fa-tachometer-fastest::after {
  content: "\f62b\f62b"; }

.fasds.fa-circle-u::after, .fa-sharp-duotone.fa-circle-u::after {
  content: "\e127\e127"; }

.fasds.fa-shield-slash::after, .fa-sharp-duotone.fa-shield-slash::after {
  content: "\e24b\e24b"; }

.fasds.fa-square-phone-hangup::after, .fa-sharp-duotone.fa-square-phone-hangup::after {
  content: "\e27a\e27a"; }

.fasds.fa-phone-square-down::after, .fa-sharp-duotone.fa-phone-square-down::after {
  content: "\e27a\e27a"; }

.fasds.fa-arrow-up-left::after, .fa-sharp-duotone.fa-arrow-up-left::after {
  content: "\e09d\e09d"; }

.fasds.fa-transporter-1::after, .fa-sharp-duotone.fa-transporter-1::after {
  content: "\e043\e043"; }

.fasds.fa-peanuts::after, .fa-sharp-duotone.fa-peanuts::after {
  content: "\e431\e431"; }

.fasds.fa-shuffle::after, .fa-sharp-duotone.fa-shuffle::after {
  content: "\f074\f074"; }

.fasds.fa-random::after, .fa-sharp-duotone.fa-random::after {
  content: "\f074\f074"; }

.fasds.fa-person-running::after, .fa-sharp-duotone.fa-person-running::after {
  content: "\f70c\f70c"; }

.fasds.fa-running::after, .fa-sharp-duotone.fa-running::after {
  content: "\f70c\f70c"; }

.fasds.fa-mobile-retro::after, .fa-sharp-duotone.fa-mobile-retro::after {
  content: "\e527\e527"; }

.fasds.fa-grip-lines-vertical::after, .fa-sharp-duotone.fa-grip-lines-vertical::after {
  content: "\f7a5\f7a5"; }

.fasds.fa-bin-bottles-recycle::after, .fa-sharp-duotone.fa-bin-bottles-recycle::after {
  content: "\e5f6\e5f6"; }

.fasds.fa-arrow-up-from-square::after, .fa-sharp-duotone.fa-arrow-up-from-square::after {
  content: "\e09c\e09c"; }

.fasds.fa-file-dashed-line::after, .fa-sharp-duotone.fa-file-dashed-line::after {
  content: "\f877\f877"; }

.fasds.fa-page-break::after, .fa-sharp-duotone.fa-page-break::after {
  content: "\f877\f877"; }

.fasds.fa-bracket-curly-right::after, .fa-sharp-duotone.fa-bracket-curly-right::after {
  content: "\7d\7d"; }

.fasds.fa-spider::after, .fa-sharp-duotone.fa-spider::after {
  content: "\f717\f717"; }

.fasds.fa-clock-three::after, .fa-sharp-duotone.fa-clock-three::after {
  content: "\e356\e356"; }

.fasds.fa-hands-bound::after, .fa-sharp-duotone.fa-hands-bound::after {
  content: "\e4f9\e4f9"; }

.fasds.fa-scalpel-line-dashed::after, .fa-sharp-duotone.fa-scalpel-line-dashed::after {
  content: "\f61e\f61e"; }

.fasds.fa-scalpel-path::after, .fa-sharp-duotone.fa-scalpel-path::after {
  content: "\f61e\f61e"; }

.fasds.fa-file-invoice-dollar::after, .fa-sharp-duotone.fa-file-invoice-dollar::after {
  content: "\f571\f571"; }

.fasds.fa-pipe-smoking::after, .fa-sharp-duotone.fa-pipe-smoking::after {
  content: "\e3c4\e3c4"; }

.fasds.fa-face-astonished::after, .fa-sharp-duotone.fa-face-astonished::after {
  content: "\e36b\e36b"; }

.fasds.fa-window::after, .fa-sharp-duotone.fa-window::after {
  content: "\f40e\f40e"; }

.fasds.fa-plane-circle-exclamation::after, .fa-sharp-duotone.fa-plane-circle-exclamation::after {
  content: "\e556\e556"; }

.fasds.fa-ear::after, .fa-sharp-duotone.fa-ear::after {
  content: "\f5f0\f5f0"; }

.fasds.fa-file-lock::after, .fa-sharp-duotone.fa-file-lock::after {
  content: "\e3a6\e3a6"; }

.fasds.fa-diagram-venn::after, .fa-sharp-duotone.fa-diagram-venn::after {
  content: "\e15a\e15a"; }

.fasds.fa-arrow-down-from-bracket::after, .fa-sharp-duotone.fa-arrow-down-from-bracket::after {
  content: "\e667\e667"; }

.fasds.fa-x-ray::after, .fa-sharp-duotone.fa-x-ray::after {
  content: "\f497\f497"; }

.fasds.fa-goal-net::after, .fa-sharp-duotone.fa-goal-net::after {
  content: "\e3ab\e3ab"; }

.fasds.fa-coffin-cross::after, .fa-sharp-duotone.fa-coffin-cross::after {
  content: "\e051\e051"; }

.fasds.fa-octopus::after, .fa-sharp-duotone.fa-octopus::after {
  content: "\e688\e688"; }

.fasds.fa-spell-check::after, .fa-sharp-duotone.fa-spell-check::after {
  content: "\f891\f891"; }

.fasds.fa-location-xmark::after, .fa-sharp-duotone.fa-location-xmark::after {
  content: "\f60e\f60e"; }

.fasds.fa-map-marker-times::after, .fa-sharp-duotone.fa-map-marker-times::after {
  content: "\f60e\f60e"; }

.fasds.fa-map-marker-xmark::after, .fa-sharp-duotone.fa-map-marker-xmark::after {
  content: "\f60e\f60e"; }

.fasds.fa-circle-quarter-stroke::after, .fa-sharp-duotone.fa-circle-quarter-stroke::after {
  content: "\e5d3\e5d3"; }

.fasds.fa-lasso::after, .fa-sharp-duotone.fa-lasso::after {
  content: "\f8c8\f8c8"; }

.fasds.fa-slash::after, .fa-sharp-duotone.fa-slash::after {
  content: "\f715\f715"; }

.fasds.fa-person-to-portal::after, .fa-sharp-duotone.fa-person-to-portal::after {
  content: "\e022\e022"; }

.fasds.fa-portal-enter::after, .fa-sharp-duotone.fa-portal-enter::after {
  content: "\e022\e022"; }

.fasds.fa-calendar-star::after, .fa-sharp-duotone.fa-calendar-star::after {
  content: "\f736\f736"; }

.fasds.fa-computer-mouse::after, .fa-sharp-duotone.fa-computer-mouse::after {
  content: "\f8cc\f8cc"; }

.fasds.fa-mouse::after, .fa-sharp-duotone.fa-mouse::after {
  content: "\f8cc\f8cc"; }

.fasds.fa-arrow-right-to-bracket::after, .fa-sharp-duotone.fa-arrow-right-to-bracket::after {
  content: "\f090\f090"; }

.fasds.fa-sign-in::after, .fa-sharp-duotone.fa-sign-in::after {
  content: "\f090\f090"; }

.fasds.fa-pegasus::after, .fa-sharp-duotone.fa-pegasus::after {
  content: "\f703\f703"; }

.fasds.fa-files-medical::after, .fa-sharp-duotone.fa-files-medical::after {
  content: "\f7fd\f7fd"; }

.fasds.fa-cannon::after, .fa-sharp-duotone.fa-cannon::after {
  content: "\e642\e642"; }

.fasds.fa-nfc-lock::after, .fa-sharp-duotone.fa-nfc-lock::after {
  content: "\e1f8\e1f8"; }

.fasds.fa-person-ski-lift::after, .fa-sharp-duotone.fa-person-ski-lift::after {
  content: "\f7c8\f7c8"; }

.fasds.fa-ski-lift::after, .fa-sharp-duotone.fa-ski-lift::after {
  content: "\f7c8\f7c8"; }

.fasds.fa-square-6::after, .fa-sharp-duotone.fa-square-6::after {
  content: "\e25b\e25b"; }

.fasds.fa-shop-slash::after, .fa-sharp-duotone.fa-shop-slash::after {
  content: "\e070\e070"; }

.fasds.fa-store-alt-slash::after, .fa-sharp-duotone.fa-store-alt-slash::after {
  content: "\e070\e070"; }

.fasds.fa-wind-turbine::after, .fa-sharp-duotone.fa-wind-turbine::after {
  content: "\f89b\f89b"; }

.fasds.fa-sliders-simple::after, .fa-sharp-duotone.fa-sliders-simple::after {
  content: "\e253\e253"; }

.fasds.fa-grid-round::after, .fa-sharp-duotone.fa-grid-round::after {
  content: "\e5da\e5da"; }

.fasds.fa-badge-sheriff::after, .fa-sharp-duotone.fa-badge-sheriff::after {
  content: "\f8a2\f8a2"; }

.fasds.fa-server::after, .fa-sharp-duotone.fa-server::after {
  content: "\f233\f233"; }

.fasds.fa-virus-covid-slash::after, .fa-sharp-duotone.fa-virus-covid-slash::after {
  content: "\e4a9\e4a9"; }

.fasds.fa-intersection::after, .fa-sharp-duotone.fa-intersection::after {
  content: "\f668\f668"; }

.fasds.fa-shop-lock::after, .fa-sharp-duotone.fa-shop-lock::after {
  content: "\e4a5\e4a5"; }

.fasds.fa-family::after, .fa-sharp-duotone.fa-family::after {
  content: "\e300\e300"; }

.fasds.fa-hourglass-start::after, .fa-sharp-duotone.fa-hourglass-start::after {
  content: "\f251\f251"; }

.fasds.fa-hourglass-1::after, .fa-sharp-duotone.fa-hourglass-1::after {
  content: "\f251\f251"; }

.fasds.fa-user-hair-buns::after, .fa-sharp-duotone.fa-user-hair-buns::after {
  content: "\e3d3\e3d3"; }

.fasds.fa-blender-phone::after, .fa-sharp-duotone.fa-blender-phone::after {
  content: "\f6b6\f6b6"; }

.fasds.fa-hourglass-clock::after, .fa-sharp-duotone.fa-hourglass-clock::after {
  content: "\e41b\e41b"; }

.fasds.fa-person-seat-reclined::after, .fa-sharp-duotone.fa-person-seat-reclined::after {
  content: "\e21f\e21f"; }

.fasds.fa-paper-plane-top::after, .fa-sharp-duotone.fa-paper-plane-top::after {
  content: "\e20a\e20a"; }

.fasds.fa-paper-plane-alt::after, .fa-sharp-duotone.fa-paper-plane-alt::after {
  content: "\e20a\e20a"; }

.fasds.fa-send::after, .fa-sharp-duotone.fa-send::after {
  content: "\e20a\e20a"; }

.fasds.fa-message-arrow-up::after, .fa-sharp-duotone.fa-message-arrow-up::after {
  content: "\e1dc\e1dc"; }

.fasds.fa-comment-alt-arrow-up::after, .fa-sharp-duotone.fa-comment-alt-arrow-up::after {
  content: "\e1dc\e1dc"; }

.fasds.fa-lightbulb-exclamation::after, .fa-sharp-duotone.fa-lightbulb-exclamation::after {
  content: "\f671\f671"; }

.fasds.fa-layer-minus::after, .fa-sharp-duotone.fa-layer-minus::after {
  content: "\f5fe\f5fe"; }

.fasds.fa-layer-group-minus::after, .fa-sharp-duotone.fa-layer-group-minus::after {
  content: "\f5fe\f5fe"; }

.fasds.fa-chart-pie-simple-circle-currency::after, .fa-sharp-duotone.fa-chart-pie-simple-circle-currency::after {
  content: "\e604\e604"; }

.fasds.fa-circle-e::after, .fa-sharp-duotone.fa-circle-e::after {
  content: "\e109\e109"; }

.fasds.fa-building-wheat::after, .fa-sharp-duotone.fa-building-wheat::after {
  content: "\e4db\e4db"; }

.fasds.fa-gauge-max::after, .fa-sharp-duotone.fa-gauge-max::after {
  content: "\f626\f626"; }

.fasds.fa-tachometer-alt-fastest::after, .fa-sharp-duotone.fa-tachometer-alt-fastest::after {
  content: "\f626\f626"; }

.fasds.fa-person-breastfeeding::after, .fa-sharp-duotone.fa-person-breastfeeding::after {
  content: "\e53a\e53a"; }

.fasds.fa-apostrophe::after, .fa-sharp-duotone.fa-apostrophe::after {
  content: "\27\27"; }

.fasds.fa-file-png::after, .fa-sharp-duotone.fa-file-png::after {
  content: "\e666\e666"; }

.fasds.fa-fire-hydrant::after, .fa-sharp-duotone.fa-fire-hydrant::after {
  content: "\e17f\e17f"; }

.fasds.fa-right-to-bracket::after, .fa-sharp-duotone.fa-right-to-bracket::after {
  content: "\f2f6\f2f6"; }

.fasds.fa-sign-in-alt::after, .fa-sharp-duotone.fa-sign-in-alt::after {
  content: "\f2f6\f2f6"; }

.fasds.fa-video-plus::after, .fa-sharp-duotone.fa-video-plus::after {
  content: "\f4e1\f4e1"; }

.fasds.fa-square-right::after, .fa-sharp-duotone.fa-square-right::after {
  content: "\f352\f352"; }

.fasds.fa-arrow-alt-square-right::after, .fa-sharp-duotone.fa-arrow-alt-square-right::after {
  content: "\f352\f352"; }

.fasds.fa-comment-smile::after, .fa-sharp-duotone.fa-comment-smile::after {
  content: "\f4b4\f4b4"; }

.fasds.fa-venus::after, .fa-sharp-duotone.fa-venus::after {
  content: "\f221\f221"; }

.fasds.fa-passport::after, .fa-sharp-duotone.fa-passport::after {
  content: "\f5ab\f5ab"; }

.fasds.fa-thumbtack-slash::after, .fa-sharp-duotone.fa-thumbtack-slash::after {
  content: "\e68f\e68f"; }

.fasds.fa-thumb-tack-slash::after, .fa-sharp-duotone.fa-thumb-tack-slash::after {
  content: "\e68f\e68f"; }

.fasds.fa-inbox-in::after, .fa-sharp-duotone.fa-inbox-in::after {
  content: "\f310\f310"; }

.fasds.fa-inbox-arrow-down::after, .fa-sharp-duotone.fa-inbox-arrow-down::after {
  content: "\f310\f310"; }

.fasds.fa-heart-pulse::after, .fa-sharp-duotone.fa-heart-pulse::after {
  content: "\f21e\f21e"; }

.fasds.fa-heartbeat::after, .fa-sharp-duotone.fa-heartbeat::after {
  content: "\f21e\f21e"; }

.fasds.fa-circle-8::after, .fa-sharp-duotone.fa-circle-8::after {
  content: "\e0f5\e0f5"; }

.fasds.fa-clouds-moon::after, .fa-sharp-duotone.fa-clouds-moon::after {
  content: "\f745\f745"; }

.fasds.fa-clock-ten-thirty::after, .fa-sharp-duotone.fa-clock-ten-thirty::after {
  content: "\e355\e355"; }

.fasds.fa-people-carry-box::after, .fa-sharp-duotone.fa-people-carry-box::after {
  content: "\f4ce\f4ce"; }

.fasds.fa-people-carry::after, .fa-sharp-duotone.fa-people-carry::after {
  content: "\f4ce\f4ce"; }

.fasds.fa-folder-user::after, .fa-sharp-duotone.fa-folder-user::after {
  content: "\e18e\e18e"; }

.fasds.fa-trash-can-xmark::after, .fa-sharp-duotone.fa-trash-can-xmark::after {
  content: "\e2ae\e2ae"; }

.fasds.fa-temperature-high::after, .fa-sharp-duotone.fa-temperature-high::after {
  content: "\f769\f769"; }

.fasds.fa-microchip::after, .fa-sharp-duotone.fa-microchip::after {
  content: "\f2db\f2db"; }

.fasds.fa-left-long-to-line::after, .fa-sharp-duotone.fa-left-long-to-line::after {
  content: "\e41e\e41e"; }

.fasds.fa-crown::after, .fa-sharp-duotone.fa-crown::after {
  content: "\f521\f521"; }

.fasds.fa-weight-hanging::after, .fa-sharp-duotone.fa-weight-hanging::after {
  content: "\f5cd\f5cd"; }

.fasds.fa-xmarks-lines::after, .fa-sharp-duotone.fa-xmarks-lines::after {
  content: "\e59a\e59a"; }

.fasds.fa-file-prescription::after, .fa-sharp-duotone.fa-file-prescription::after {
  content: "\f572\f572"; }

.fasds.fa-table-cells-lock::after, .fa-sharp-duotone.fa-table-cells-lock::after {
  content: "\e679\e679"; }

.fasds.fa-calendar-range::after, .fa-sharp-duotone.fa-calendar-range::after {
  content: "\e0d6\e0d6"; }

.fasds.fa-flower-daffodil::after, .fa-sharp-duotone.fa-flower-daffodil::after {
  content: "\f800\f800"; }

.fasds.fa-hand-back-point-up::after, .fa-sharp-duotone.fa-hand-back-point-up::after {
  content: "\e1a2\e1a2"; }

.fasds.fa-weight-scale::after, .fa-sharp-duotone.fa-weight-scale::after {
  content: "\f496\f496"; }

.fasds.fa-weight::after, .fa-sharp-duotone.fa-weight::after {
  content: "\f496\f496"; }

.fasds.fa-arrow-up-to-arc::after, .fa-sharp-duotone.fa-arrow-up-to-arc::after {
  content: "\e617\e617"; }

.fasds.fa-star-exclamation::after, .fa-sharp-duotone.fa-star-exclamation::after {
  content: "\f2f3\f2f3"; }

.fasds.fa-books::after, .fa-sharp-duotone.fa-books::after {
  content: "\f5db\f5db"; }

.fasds.fa-user-group::after, .fa-sharp-duotone.fa-user-group::after {
  content: "\f500\f500"; }

.fasds.fa-user-friends::after, .fa-sharp-duotone.fa-user-friends::after {
  content: "\f500\f500"; }

.fasds.fa-arrow-up-a-z::after, .fa-sharp-duotone.fa-arrow-up-a-z::after {
  content: "\f15e\f15e"; }

.fasds.fa-sort-alpha-up::after, .fa-sharp-duotone.fa-sort-alpha-up::after {
  content: "\f15e\f15e"; }

.fasds.fa-layer-plus::after, .fa-sharp-duotone.fa-layer-plus::after {
  content: "\f5ff\f5ff"; }

.fasds.fa-layer-group-plus::after, .fa-sharp-duotone.fa-layer-group-plus::after {
  content: "\f5ff\f5ff"; }

.fasds.fa-play-pause::after, .fa-sharp-duotone.fa-play-pause::after {
  content: "\e22f\e22f"; }

.fasds.fa-block-question::after, .fa-sharp-duotone.fa-block-question::after {
  content: "\e3dd\e3dd"; }

.fasds.fa-snooze::after, .fa-sharp-duotone.fa-snooze::after {
  content: "\f880\f880"; }

.fasds.fa-zzz::after, .fa-sharp-duotone.fa-zzz::after {
  content: "\f880\f880"; }

.fasds.fa-scanner-image::after, .fa-sharp-duotone.fa-scanner-image::after {
  content: "\f8f3\f8f3"; }

.fasds.fa-tv-retro::after, .fa-sharp-duotone.fa-tv-retro::after {
  content: "\f401\f401"; }

.fasds.fa-square-t::after, .fa-sharp-duotone.fa-square-t::after {
  content: "\e280\e280"; }

.fasds.fa-farm::after, .fa-sharp-duotone.fa-farm::after {
  content: "\f864\f864"; }

.fasds.fa-barn-silo::after, .fa-sharp-duotone.fa-barn-silo::after {
  content: "\f864\f864"; }

.fasds.fa-chess-knight::after, .fa-sharp-duotone.fa-chess-knight::after {
  content: "\f441\f441"; }

.fasds.fa-bars-sort::after, .fa-sharp-duotone.fa-bars-sort::after {
  content: "\e0ae\e0ae"; }

.fasds.fa-pallet-boxes::after, .fa-sharp-duotone.fa-pallet-boxes::after {
  content: "\f483\f483"; }

.fasds.fa-palette-boxes::after, .fa-sharp-duotone.fa-palette-boxes::after {
  content: "\f483\f483"; }

.fasds.fa-pallet-alt::after, .fa-sharp-duotone.fa-pallet-alt::after {
  content: "\f483\f483"; }

.fasds.fa-face-laugh-squint::after, .fa-sharp-duotone.fa-face-laugh-squint::after {
  content: "\f59b\f59b"; }

.fasds.fa-laugh-squint::after, .fa-sharp-duotone.fa-laugh-squint::after {
  content: "\f59b\f59b"; }

.fasds.fa-code-simple::after, .fa-sharp-duotone.fa-code-simple::after {
  content: "\e13d\e13d"; }

.fasds.fa-bolt-slash::after, .fa-sharp-duotone.fa-bolt-slash::after {
  content: "\e0b8\e0b8"; }

.fasds.fa-panel-fire::after, .fa-sharp-duotone.fa-panel-fire::after {
  content: "\e42f\e42f"; }

.fasds.fa-binary-circle-check::after, .fa-sharp-duotone.fa-binary-circle-check::after {
  content: "\e33c\e33c"; }

.fasds.fa-comment-minus::after, .fa-sharp-duotone.fa-comment-minus::after {
  content: "\f4b1\f4b1"; }

.fasds.fa-burrito::after, .fa-sharp-duotone.fa-burrito::after {
  content: "\f7ed\f7ed"; }

.fasds.fa-violin::after, .fa-sharp-duotone.fa-violin::after {
  content: "\f8ed\f8ed"; }

.fasds.fa-objects-column::after, .fa-sharp-duotone.fa-objects-column::after {
  content: "\e3c1\e3c1"; }

.fasds.fa-square-chevron-down::after, .fa-sharp-duotone.fa-square-chevron-down::after {
  content: "\f329\f329"; }

.fasds.fa-chevron-square-down::after, .fa-sharp-duotone.fa-chevron-square-down::after {
  content: "\f329\f329"; }

.fasds.fa-comment-plus::after, .fa-sharp-duotone.fa-comment-plus::after {
  content: "\f4b2\f4b2"; }

.fasds.fa-triangle-instrument::after, .fa-sharp-duotone.fa-triangle-instrument::after {
  content: "\f8e2\f8e2"; }

.fasds.fa-triangle-music::after, .fa-sharp-duotone.fa-triangle-music::after {
  content: "\f8e2\f8e2"; }

.fasds.fa-wheelchair::after, .fa-sharp-duotone.fa-wheelchair::after {
  content: "\f193\f193"; }

.fasds.fa-user-pilot-tie::after, .fa-sharp-duotone.fa-user-pilot-tie::after {
  content: "\e2c1\e2c1"; }

.fasds.fa-piano-keyboard::after, .fa-sharp-duotone.fa-piano-keyboard::after {
  content: "\f8d5\f8d5"; }

.fasds.fa-bed-empty::after, .fa-sharp-duotone.fa-bed-empty::after {
  content: "\f8f9\f8f9"; }

.fasds.fa-circle-arrow-up::after, .fa-sharp-duotone.fa-circle-arrow-up::after {
  content: "\f0aa\f0aa"; }

.fasds.fa-arrow-circle-up::after, .fa-sharp-duotone.fa-arrow-circle-up::after {
  content: "\f0aa\f0aa"; }

.fasds.fa-toggle-on::after, .fa-sharp-duotone.fa-toggle-on::after {
  content: "\f205\f205"; }

.fasds.fa-rectangle-vertical::after, .fa-sharp-duotone.fa-rectangle-vertical::after {
  content: "\f2fb\f2fb"; }

.fasds.fa-rectangle-portrait::after, .fa-sharp-duotone.fa-rectangle-portrait::after {
  content: "\f2fb\f2fb"; }

.fasds.fa-person-walking::after, .fa-sharp-duotone.fa-person-walking::after {
  content: "\f554\f554"; }

.fasds.fa-walking::after, .fa-sharp-duotone.fa-walking::after {
  content: "\f554\f554"; }

.fasds.fa-l::after, .fa-sharp-duotone.fa-l::after {
  content: "\4c\4c"; }

.fasds.fa-signal-stream::after, .fa-sharp-duotone.fa-signal-stream::after {
  content: "\f8dd\f8dd"; }

.fasds.fa-down-to-bracket::after, .fa-sharp-duotone.fa-down-to-bracket::after {
  content: "\e4e7\e4e7"; }

.fasds.fa-circle-z::after, .fa-sharp-duotone.fa-circle-z::after {
  content: "\e130\e130"; }

.fasds.fa-stars::after, .fa-sharp-duotone.fa-stars::after {
  content: "\f762\f762"; }

.fasds.fa-fire::after, .fa-sharp-duotone.fa-fire::after {
  content: "\f06d\f06d"; }

.fasds.fa-bed-pulse::after, .fa-sharp-duotone.fa-bed-pulse::after {
  content: "\f487\f487"; }

.fasds.fa-procedures::after, .fa-sharp-duotone.fa-procedures::after {
  content: "\f487\f487"; }

.fasds.fa-house-day::after, .fa-sharp-duotone.fa-house-day::after {
  content: "\e00e\e00e"; }

.fasds.fa-shuttle-space::after, .fa-sharp-duotone.fa-shuttle-space::after {
  content: "\f197\f197"; }

.fasds.fa-space-shuttle::after, .fa-sharp-duotone.fa-space-shuttle::after {
  content: "\f197\f197"; }

.fasds.fa-shirt-long-sleeve::after, .fa-sharp-duotone.fa-shirt-long-sleeve::after {
  content: "\e3c7\e3c7"; }

.fasds.fa-chart-pie-simple::after, .fa-sharp-duotone.fa-chart-pie-simple::after {
  content: "\f64e\f64e"; }

.fasds.fa-chart-pie-alt::after, .fa-sharp-duotone.fa-chart-pie-alt::after {
  content: "\f64e\f64e"; }

.fasds.fa-face-laugh::after, .fa-sharp-duotone.fa-face-laugh::after {
  content: "\f599\f599"; }

.fasds.fa-laugh::after, .fa-sharp-duotone.fa-laugh::after {
  content: "\f599\f599"; }

.fasds.fa-folder-open::after, .fa-sharp-duotone.fa-folder-open::after {
  content: "\f07c\f07c"; }

.fasds.fa-album-collection-circle-user::after, .fa-sharp-duotone.fa-album-collection-circle-user::after {
  content: "\e48f\e48f"; }

.fasds.fa-candy::after, .fa-sharp-duotone.fa-candy::after {
  content: "\e3e7\e3e7"; }

.fasds.fa-bowl-hot::after, .fa-sharp-duotone.fa-bowl-hot::after {
  content: "\f823\f823"; }

.fasds.fa-soup::after, .fa-sharp-duotone.fa-soup::after {
  content: "\f823\f823"; }

.fasds.fa-flatbread::after, .fa-sharp-duotone.fa-flatbread::after {
  content: "\e40b\e40b"; }

.fasds.fa-heart-circle-plus::after, .fa-sharp-duotone.fa-heart-circle-plus::after {
  content: "\e500\e500"; }

.fasds.fa-code-fork::after, .fa-sharp-duotone.fa-code-fork::after {
  content: "\e13b\e13b"; }

.fasds.fa-city::after, .fa-sharp-duotone.fa-city::after {
  content: "\f64f\f64f"; }

.fasds.fa-signal-bars-weak::after, .fa-sharp-duotone.fa-signal-bars-weak::after {
  content: "\f691\f691"; }

.fasds.fa-signal-alt-1::after, .fa-sharp-duotone.fa-signal-alt-1::after {
  content: "\f691\f691"; }

.fasds.fa-microphone-lines::after, .fa-sharp-duotone.fa-microphone-lines::after {
  content: "\f3c9\f3c9"; }

.fasds.fa-microphone-alt::after, .fa-sharp-duotone.fa-microphone-alt::after {
  content: "\f3c9\f3c9"; }

.fasds.fa-clock-twelve::after, .fa-sharp-duotone.fa-clock-twelve::after {
  content: "\e358\e358"; }

.fasds.fa-pepper-hot::after, .fa-sharp-duotone.fa-pepper-hot::after {
  content: "\f816\f816"; }

.fasds.fa-citrus-slice::after, .fa-sharp-duotone.fa-citrus-slice::after {
  content: "\e2f5\e2f5"; }

.fasds.fa-sheep::after, .fa-sharp-duotone.fa-sheep::after {
  content: "\f711\f711"; }

.fasds.fa-unlock::after, .fa-sharp-duotone.fa-unlock::after {
  content: "\f09c\f09c"; }

.fasds.fa-colon-sign::after, .fa-sharp-duotone.fa-colon-sign::after {
  content: "\e140\e140"; }

.fasds.fa-headset::after, .fa-sharp-duotone.fa-headset::after {
  content: "\f590\f590"; }

.fasds.fa-badger-honey::after, .fa-sharp-duotone.fa-badger-honey::after {
  content: "\f6b4\f6b4"; }

.fasds.fa-h4::after, .fa-sharp-duotone.fa-h4::after {
  content: "\f86a\f86a"; }

.fasds.fa-store-slash::after, .fa-sharp-duotone.fa-store-slash::after {
  content: "\e071\e071"; }

.fasds.fa-road-circle-xmark::after, .fa-sharp-duotone.fa-road-circle-xmark::after {
  content: "\e566\e566"; }

.fasds.fa-signal-slash::after, .fa-sharp-duotone.fa-signal-slash::after {
  content: "\f695\f695"; }

.fasds.fa-user-minus::after, .fa-sharp-duotone.fa-user-minus::after {
  content: "\f503\f503"; }

.fasds.fa-mars-stroke-up::after, .fa-sharp-duotone.fa-mars-stroke-up::after {
  content: "\f22a\f22a"; }

.fasds.fa-mars-stroke-v::after, .fa-sharp-duotone.fa-mars-stroke-v::after {
  content: "\f22a\f22a"; }

.fasds.fa-champagne-glasses::after, .fa-sharp-duotone.fa-champagne-glasses::after {
  content: "\f79f\f79f"; }

.fasds.fa-glass-cheers::after, .fa-sharp-duotone.fa-glass-cheers::after {
  content: "\f79f\f79f"; }

.fasds.fa-taco::after, .fa-sharp-duotone.fa-taco::after {
  content: "\f826\f826"; }

.fasds.fa-hexagon-plus::after, .fa-sharp-duotone.fa-hexagon-plus::after {
  content: "\f300\f300"; }

.fasds.fa-plus-hexagon::after, .fa-sharp-duotone.fa-plus-hexagon::after {
  content: "\f300\f300"; }

.fasds.fa-clipboard::after, .fa-sharp-duotone.fa-clipboard::after {
  content: "\f328\f328"; }

.fasds.fa-house-circle-exclamation::after, .fa-sharp-duotone.fa-house-circle-exclamation::after {
  content: "\e50a\e50a"; }

.fasds.fa-file-arrow-up::after, .fa-sharp-duotone.fa-file-arrow-up::after {
  content: "\f574\f574"; }

.fasds.fa-file-upload::after, .fa-sharp-duotone.fa-file-upload::after {
  content: "\f574\f574"; }

.fasds.fa-wifi::after, .fa-sharp-duotone.fa-wifi::after {
  content: "\f1eb\f1eb"; }

.fasds.fa-wifi-3::after, .fa-sharp-duotone.fa-wifi-3::after {
  content: "\f1eb\f1eb"; }

.fasds.fa-wifi-strong::after, .fa-sharp-duotone.fa-wifi-strong::after {
  content: "\f1eb\f1eb"; }

.fasds.fa-messages::after, .fa-sharp-duotone.fa-messages::after {
  content: "\f4b6\f4b6"; }

.fasds.fa-comments-alt::after, .fa-sharp-duotone.fa-comments-alt::after {
  content: "\f4b6\f4b6"; }

.fasds.fa-bath::after, .fa-sharp-duotone.fa-bath::after {
  content: "\f2cd\f2cd"; }

.fasds.fa-bathtub::after, .fa-sharp-duotone.fa-bathtub::after {
  content: "\f2cd\f2cd"; }

.fasds.fa-umbrella-simple::after, .fa-sharp-duotone.fa-umbrella-simple::after {
  content: "\e2bc\e2bc"; }

.fasds.fa-umbrella-alt::after, .fa-sharp-duotone.fa-umbrella-alt::after {
  content: "\e2bc\e2bc"; }

.fasds.fa-rectangle-history-circle-plus::after, .fa-sharp-duotone.fa-rectangle-history-circle-plus::after {
  content: "\e4a3\e4a3"; }

.fasds.fa-underline::after, .fa-sharp-duotone.fa-underline::after {
  content: "\f0cd\f0cd"; }

.fasds.fa-prescription-bottle-pill::after, .fa-sharp-duotone.fa-prescription-bottle-pill::after {
  content: "\e5c0\e5c0"; }

.fasds.fa-user-pen::after, .fa-sharp-duotone.fa-user-pen::after {
  content: "\f4ff\f4ff"; }

.fasds.fa-user-edit::after, .fa-sharp-duotone.fa-user-edit::after {
  content: "\f4ff\f4ff"; }

.fasds.fa-binary-slash::after, .fa-sharp-duotone.fa-binary-slash::after {
  content: "\e33e\e33e"; }

.fasds.fa-square-o::after, .fa-sharp-duotone.fa-square-o::after {
  content: "\e278\e278"; }

.fasds.fa-caduceus::after, .fa-sharp-duotone.fa-caduceus::after {
  content: "\e681\e681"; }

.fasds.fa-signature::after, .fa-sharp-duotone.fa-signature::after {
  content: "\f5b7\f5b7"; }

.fasds.fa-stroopwafel::after, .fa-sharp-duotone.fa-stroopwafel::after {
  content: "\f551\f551"; }

.fasds.fa-bold::after, .fa-sharp-duotone.fa-bold::after {
  content: "\f032\f032"; }

.fasds.fa-anchor-lock::after, .fa-sharp-duotone.fa-anchor-lock::after {
  content: "\e4ad\e4ad"; }

.fasds.fa-building-ngo::after, .fa-sharp-duotone.fa-building-ngo::after {
  content: "\e4d7\e4d7"; }

.fasds.fa-transporter-3::after, .fa-sharp-duotone.fa-transporter-3::after {
  content: "\e045\e045"; }

.fasds.fa-engine-warning::after, .fa-sharp-duotone.fa-engine-warning::after {
  content: "\f5f2\f5f2"; }

.fasds.fa-engine-exclamation::after, .fa-sharp-duotone.fa-engine-exclamation::after {
  content: "\f5f2\f5f2"; }

.fasds.fa-circle-down-right::after, .fa-sharp-duotone.fa-circle-down-right::after {
  content: "\e108\e108"; }

.fasds.fa-square-k::after, .fa-sharp-duotone.fa-square-k::after {
  content: "\e274\e274"; }

.fasds.fa-manat-sign::after, .fa-sharp-duotone.fa-manat-sign::after {
  content: "\e1d5\e1d5"; }

.fasds.fa-money-check-pen::after, .fa-sharp-duotone.fa-money-check-pen::after {
  content: "\f872\f872"; }

.fasds.fa-money-check-edit::after, .fa-sharp-duotone.fa-money-check-edit::after {
  content: "\f872\f872"; }

.fasds.fa-not-equal::after, .fa-sharp-duotone.fa-not-equal::after {
  content: "\f53e\f53e"; }

.fasds.fa-border-top-left::after, .fa-sharp-duotone.fa-border-top-left::after {
  content: "\f853\f853"; }

.fasds.fa-border-style::after, .fa-sharp-duotone.fa-border-style::after {
  content: "\f853\f853"; }

.fasds.fa-map-location-dot::after, .fa-sharp-duotone.fa-map-location-dot::after {
  content: "\f5a0\f5a0"; }

.fasds.fa-map-marked-alt::after, .fa-sharp-duotone.fa-map-marked-alt::after {
  content: "\f5a0\f5a0"; }

.fasds.fa-tilde::after, .fa-sharp-duotone.fa-tilde::after {
  content: "\7e\7e"; }

.fasds.fa-jedi::after, .fa-sharp-duotone.fa-jedi::after {
  content: "\f669\f669"; }

.fasds.fa-square-poll-vertical::after, .fa-sharp-duotone.fa-square-poll-vertical::after {
  content: "\f681\f681"; }

.fasds.fa-poll::after, .fa-sharp-duotone.fa-poll::after {
  content: "\f681\f681"; }

.fasds.fa-arrow-down-square-triangle::after, .fa-sharp-duotone.fa-arrow-down-square-triangle::after {
  content: "\f889\f889"; }

.fasds.fa-sort-shapes-down-alt::after, .fa-sharp-duotone.fa-sort-shapes-down-alt::after {
  content: "\f889\f889"; }

.fasds.fa-mug-hot::after, .fa-sharp-duotone.fa-mug-hot::after {
  content: "\f7b6\f7b6"; }

.fasds.fa-dog-leashed::after, .fa-sharp-duotone.fa-dog-leashed::after {
  content: "\f6d4\f6d4"; }

.fasds.fa-car-battery::after, .fa-sharp-duotone.fa-car-battery::after {
  content: "\f5df\f5df"; }

.fasds.fa-battery-car::after, .fa-sharp-duotone.fa-battery-car::after {
  content: "\f5df\f5df"; }

.fasds.fa-face-downcast-sweat::after, .fa-sharp-duotone.fa-face-downcast-sweat::after {
  content: "\e371\e371"; }

.fasds.fa-mailbox-flag-up::after, .fa-sharp-duotone.fa-mailbox-flag-up::after {
  content: "\e5bb\e5bb"; }

.fasds.fa-memo-circle-info::after, .fa-sharp-duotone.fa-memo-circle-info::after {
  content: "\e49a\e49a"; }

.fasds.fa-gift::after, .fa-sharp-duotone.fa-gift::after {
  content: "\f06b\f06b"; }

.fasds.fa-dice-two::after, .fa-sharp-duotone.fa-dice-two::after {
  content: "\f528\f528"; }

.fasds.fa-volume::after, .fa-sharp-duotone.fa-volume::after {
  content: "\f6a8\f6a8"; }

.fasds.fa-volume-medium::after, .fa-sharp-duotone.fa-volume-medium::after {
  content: "\f6a8\f6a8"; }

.fasds.fa-transporter-5::after, .fa-sharp-duotone.fa-transporter-5::after {
  content: "\e2a6\e2a6"; }

.fasds.fa-gauge-circle-bolt::after, .fa-sharp-duotone.fa-gauge-circle-bolt::after {
  content: "\e496\e496"; }

.fasds.fa-coin-front::after, .fa-sharp-duotone.fa-coin-front::after {
  content: "\e3fc\e3fc"; }

.fasds.fa-file-slash::after, .fa-sharp-duotone.fa-file-slash::after {
  content: "\e3a7\e3a7"; }

.fasds.fa-message-arrow-up-right::after, .fa-sharp-duotone.fa-message-arrow-up-right::after {
  content: "\e1dd\e1dd"; }

.fasds.fa-treasure-chest::after, .fa-sharp-duotone.fa-treasure-chest::after {
  content: "\f723\f723"; }

.fasds.fa-chess-queen::after, .fa-sharp-duotone.fa-chess-queen::after {
  content: "\f445\f445"; }

.fasds.fa-paintbrush-fine::after, .fa-sharp-duotone.fa-paintbrush-fine::after {
  content: "\f5a9\f5a9"; }

.fasds.fa-paint-brush-alt::after, .fa-sharp-duotone.fa-paint-brush-alt::after {
  content: "\f5a9\f5a9"; }

.fasds.fa-paint-brush-fine::after, .fa-sharp-duotone.fa-paint-brush-fine::after {
  content: "\f5a9\f5a9"; }

.fasds.fa-paintbrush-alt::after, .fa-sharp-duotone.fa-paintbrush-alt::after {
  content: "\f5a9\f5a9"; }

.fasds.fa-glasses::after, .fa-sharp-duotone.fa-glasses::after {
  content: "\f530\f530"; }

.fasds.fa-hood-cloak::after, .fa-sharp-duotone.fa-hood-cloak::after {
  content: "\f6ef\f6ef"; }

.fasds.fa-square-quote::after, .fa-sharp-duotone.fa-square-quote::after {
  content: "\e329\e329"; }

.fasds.fa-up-left::after, .fa-sharp-duotone.fa-up-left::after {
  content: "\e2bd\e2bd"; }

.fasds.fa-bring-front::after, .fa-sharp-duotone.fa-bring-front::after {
  content: "\f857\f857"; }

.fasds.fa-chess-board::after, .fa-sharp-duotone.fa-chess-board::after {
  content: "\f43c\f43c"; }

.fasds.fa-burger-cheese::after, .fa-sharp-duotone.fa-burger-cheese::after {
  content: "\f7f1\f7f1"; }

.fasds.fa-cheeseburger::after, .fa-sharp-duotone.fa-cheeseburger::after {
  content: "\f7f1\f7f1"; }

.fasds.fa-building-circle-check::after, .fa-sharp-duotone.fa-building-circle-check::after {
  content: "\e4d2\e4d2"; }

.fasds.fa-repeat-1::after, .fa-sharp-duotone.fa-repeat-1::after {
  content: "\f365\f365"; }

.fasds.fa-arrow-down-to-line::after, .fa-sharp-duotone.fa-arrow-down-to-line::after {
  content: "\f33d\f33d"; }

.fasds.fa-arrow-to-bottom::after, .fa-sharp-duotone.fa-arrow-to-bottom::after {
  content: "\f33d\f33d"; }

.fasds.fa-grid-5::after, .fa-sharp-duotone.fa-grid-5::after {
  content: "\e199\e199"; }

.fasds.fa-swap-arrows::after, .fa-sharp-duotone.fa-swap-arrows::after {
  content: "\e60a\e60a"; }

.fasds.fa-right-long-to-line::after, .fa-sharp-duotone.fa-right-long-to-line::after {
  content: "\e444\e444"; }

.fasds.fa-person-chalkboard::after, .fa-sharp-duotone.fa-person-chalkboard::after {
  content: "\e53d\e53d"; }

.fasds.fa-mars-stroke-right::after, .fa-sharp-duotone.fa-mars-stroke-right::after {
  content: "\f22b\f22b"; }

.fasds.fa-mars-stroke-h::after, .fa-sharp-duotone.fa-mars-stroke-h::after {
  content: "\f22b\f22b"; }

.fasds.fa-hand-back-fist::after, .fa-sharp-duotone.fa-hand-back-fist::after {
  content: "\f255\f255"; }

.fasds.fa-hand-rock::after, .fa-sharp-duotone.fa-hand-rock::after {
  content: "\f255\f255"; }

.fasds.fa-grid-round-5::after, .fa-sharp-duotone.fa-grid-round-5::after {
  content: "\e5de\e5de"; }

.fasds.fa-tally::after, .fa-sharp-duotone.fa-tally::after {
  content: "\f69c\f69c"; }

.fasds.fa-tally-5::after, .fa-sharp-duotone.fa-tally-5::after {
  content: "\f69c\f69c"; }

.fasds.fa-square-caret-up::after, .fa-sharp-duotone.fa-square-caret-up::after {
  content: "\f151\f151"; }

.fasds.fa-caret-square-up::after, .fa-sharp-duotone.fa-caret-square-up::after {
  content: "\f151\f151"; }

.fasds.fa-cloud-showers-water::after, .fa-sharp-duotone.fa-cloud-showers-water::after {
  content: "\e4e4\e4e4"; }

.fasds.fa-chart-bar::after, .fa-sharp-duotone.fa-chart-bar::after {
  content: "\f080\f080"; }

.fasds.fa-bar-chart::after, .fa-sharp-duotone.fa-bar-chart::after {
  content: "\f080\f080"; }

.fasds.fa-hands-bubbles::after, .fa-sharp-duotone.fa-hands-bubbles::after {
  content: "\e05e\e05e"; }

.fasds.fa-hands-wash::after, .fa-sharp-duotone.fa-hands-wash::after {
  content: "\e05e\e05e"; }

.fasds.fa-less-than-equal::after, .fa-sharp-duotone.fa-less-than-equal::after {
  content: "\f537\f537"; }

.fasds.fa-train::after, .fa-sharp-duotone.fa-train::after {
  content: "\f238\f238"; }

.fasds.fa-up-from-dotted-line::after, .fa-sharp-duotone.fa-up-from-dotted-line::after {
  content: "\e456\e456"; }

.fasds.fa-eye-low-vision::after, .fa-sharp-duotone.fa-eye-low-vision::after {
  content: "\f2a8\f2a8"; }

.fasds.fa-low-vision::after, .fa-sharp-duotone.fa-low-vision::after {
  content: "\f2a8\f2a8"; }

.fasds.fa-traffic-light-go::after, .fa-sharp-duotone.fa-traffic-light-go::after {
  content: "\f638\f638"; }

.fasds.fa-face-exhaling::after, .fa-sharp-duotone.fa-face-exhaling::after {
  content: "\e480\e480"; }

.fasds.fa-sensor-fire::after, .fa-sharp-duotone.fa-sensor-fire::after {
  content: "\e02a\e02a"; }

.fasds.fa-user-unlock::after, .fa-sharp-duotone.fa-user-unlock::after {
  content: "\e058\e058"; }

.fasds.fa-hexagon-divide::after, .fa-sharp-duotone.fa-hexagon-divide::after {
  content: "\e1ad\e1ad"; }

.fasds.fa-00::after, .fa-sharp-duotone.fa-00::after {
  content: "\e467\e467"; }

.fasds.fa-crow::after, .fa-sharp-duotone.fa-crow::after {
  content: "\f520\f520"; }

.fasds.fa-cassette-betamax::after, .fa-sharp-duotone.fa-cassette-betamax::after {
  content: "\f8a4\f8a4"; }

.fasds.fa-betamax::after, .fa-sharp-duotone.fa-betamax::after {
  content: "\f8a4\f8a4"; }

.fasds.fa-sailboat::after, .fa-sharp-duotone.fa-sailboat::after {
  content: "\e445\e445"; }

.fasds.fa-window-restore::after, .fa-sharp-duotone.fa-window-restore::after {
  content: "\f2d2\f2d2"; }

.fasds.fa-nfc-magnifying-glass::after, .fa-sharp-duotone.fa-nfc-magnifying-glass::after {
  content: "\e1f9\e1f9"; }

.fasds.fa-file-binary::after, .fa-sharp-duotone.fa-file-binary::after {
  content: "\e175\e175"; }

.fasds.fa-circle-v::after, .fa-sharp-duotone.fa-circle-v::after {
  content: "\e12a\e12a"; }

.fasds.fa-square-plus::after, .fa-sharp-duotone.fa-square-plus::after {
  content: "\f0fe\f0fe"; }

.fasds.fa-plus-square::after, .fa-sharp-duotone.fa-plus-square::after {
  content: "\f0fe\f0fe"; }

.fasds.fa-bowl-scoops::after, .fa-sharp-duotone.fa-bowl-scoops::after {
  content: "\e3df\e3df"; }

.fasds.fa-mistletoe::after, .fa-sharp-duotone.fa-mistletoe::after {
  content: "\f7b4\f7b4"; }

.fasds.fa-custard::after, .fa-sharp-duotone.fa-custard::after {
  content: "\e403\e403"; }

.fasds.fa-lacrosse-stick::after, .fa-sharp-duotone.fa-lacrosse-stick::after {
  content: "\e3b5\e3b5"; }

.fasds.fa-hockey-mask::after, .fa-sharp-duotone.fa-hockey-mask::after {
  content: "\f6ee\f6ee"; }

.fasds.fa-sunrise::after, .fa-sharp-duotone.fa-sunrise::after {
  content: "\f766\f766"; }

.fasds.fa-subtitles::after, .fa-sharp-duotone.fa-subtitles::after {
  content: "\e60f\e60f"; }

.fasds.fa-panel-ews::after, .fa-sharp-duotone.fa-panel-ews::after {
  content: "\e42e\e42e"; }

.fasds.fa-torii-gate::after, .fa-sharp-duotone.fa-torii-gate::after {
  content: "\f6a1\f6a1"; }

.fasds.fa-cloud-exclamation::after, .fa-sharp-duotone.fa-cloud-exclamation::after {
  content: "\e491\e491"; }

.fasds.fa-message-lines::after, .fa-sharp-duotone.fa-message-lines::after {
  content: "\f4a6\f4a6"; }

.fasds.fa-comment-alt-lines::after, .fa-sharp-duotone.fa-comment-alt-lines::after {
  content: "\f4a6\f4a6"; }

.fasds.fa-frog::after, .fa-sharp-duotone.fa-frog::after {
  content: "\f52e\f52e"; }

.fasds.fa-bucket::after, .fa-sharp-duotone.fa-bucket::after {
  content: "\e4cf\e4cf"; }

.fasds.fa-floppy-disk-pen::after, .fa-sharp-duotone.fa-floppy-disk-pen::after {
  content: "\e182\e182"; }

.fasds.fa-image::after, .fa-sharp-duotone.fa-image::after {
  content: "\f03e\f03e"; }

.fasds.fa-window-frame::after, .fa-sharp-duotone.fa-window-frame::after {
  content: "\e04f\e04f"; }

.fasds.fa-microphone::after, .fa-sharp-duotone.fa-microphone::after {
  content: "\f130\f130"; }

.fasds.fa-cow::after, .fa-sharp-duotone.fa-cow::after {
  content: "\f6c8\f6c8"; }

.fasds.fa-file-zip::after, .fa-sharp-duotone.fa-file-zip::after {
  content: "\e5ee\e5ee"; }

.fasds.fa-square-ring::after, .fa-sharp-duotone.fa-square-ring::after {
  content: "\e44f\e44f"; }

.fasds.fa-down-from-line::after, .fa-sharp-duotone.fa-down-from-line::after {
  content: "\f349\f349"; }

.fasds.fa-arrow-alt-from-top::after, .fa-sharp-duotone.fa-arrow-alt-from-top::after {
  content: "\f349\f349"; }

.fasds.fa-caret-up::after, .fa-sharp-duotone.fa-caret-up::after {
  content: "\f0d8\f0d8"; }

.fasds.fa-shield-xmark::after, .fa-sharp-duotone.fa-shield-xmark::after {
  content: "\e24c\e24c"; }

.fasds.fa-shield-times::after, .fa-sharp-duotone.fa-shield-times::after {
  content: "\e24c\e24c"; }

.fasds.fa-screwdriver::after, .fa-sharp-duotone.fa-screwdriver::after {
  content: "\f54a\f54a"; }

.fasds.fa-circle-sort-down::after, .fa-sharp-duotone.fa-circle-sort-down::after {
  content: "\e031\e031"; }

.fasds.fa-sort-circle-down::after, .fa-sharp-duotone.fa-sort-circle-down::after {
  content: "\e031\e031"; }

.fasds.fa-folder-closed::after, .fa-sharp-duotone.fa-folder-closed::after {
  content: "\e185\e185"; }

.fasds.fa-house-tsunami::after, .fa-sharp-duotone.fa-house-tsunami::after {
  content: "\e515\e515"; }

.fasds.fa-square-nfi::after, .fa-sharp-duotone.fa-square-nfi::after {
  content: "\e576\e576"; }

.fasds.fa-forklift::after, .fa-sharp-duotone.fa-forklift::after {
  content: "\f47a\f47a"; }

.fasds.fa-arrow-up-from-ground-water::after, .fa-sharp-duotone.fa-arrow-up-from-ground-water::after {
  content: "\e4b5\e4b5"; }

.fasds.fa-bracket-square-right::after, .fa-sharp-duotone.fa-bracket-square-right::after {
  content: "\5d\5d"; }

.fasds.fa-martini-glass::after, .fa-sharp-duotone.fa-martini-glass::after {
  content: "\f57b\f57b"; }

.fasds.fa-glass-martini-alt::after, .fa-sharp-duotone.fa-glass-martini-alt::after {
  content: "\f57b\f57b"; }

.fasds.fa-rotate-left::after, .fa-sharp-duotone.fa-rotate-left::after {
  content: "\f2ea\f2ea"; }

.fasds.fa-rotate-back::after, .fa-sharp-duotone.fa-rotate-back::after {
  content: "\f2ea\f2ea"; }

.fasds.fa-rotate-backward::after, .fa-sharp-duotone.fa-rotate-backward::after {
  content: "\f2ea\f2ea"; }

.fasds.fa-undo-alt::after, .fa-sharp-duotone.fa-undo-alt::after {
  content: "\f2ea\f2ea"; }

.fasds.fa-table-columns::after, .fa-sharp-duotone.fa-table-columns::after {
  content: "\f0db\f0db"; }

.fasds.fa-columns::after, .fa-sharp-duotone.fa-columns::after {
  content: "\f0db\f0db"; }

.fasds.fa-square-a::after, .fa-sharp-duotone.fa-square-a::after {
  content: "\e25f\e25f"; }

.fasds.fa-tick::after, .fa-sharp-duotone.fa-tick::after {
  content: "\e32f\e32f"; }

.fasds.fa-lemon::after, .fa-sharp-duotone.fa-lemon::after {
  content: "\f094\f094"; }

.fasds.fa-head-side-mask::after, .fa-sharp-duotone.fa-head-side-mask::after {
  content: "\e063\e063"; }

.fasds.fa-handshake::after, .fa-sharp-duotone.fa-handshake::after {
  content: "\f2b5\f2b5"; }

.fasds.fa-gem::after, .fa-sharp-duotone.fa-gem::after {
  content: "\f3a5\f3a5"; }

.fasds.fa-dolly::after, .fa-sharp-duotone.fa-dolly::after {
  content: "\f472\f472"; }

.fasds.fa-dolly-box::after, .fa-sharp-duotone.fa-dolly-box::after {
  content: "\f472\f472"; }

.fasds.fa-smoking::after, .fa-sharp-duotone.fa-smoking::after {
  content: "\f48d\f48d"; }

.fasds.fa-minimize::after, .fa-sharp-duotone.fa-minimize::after {
  content: "\f78c\f78c"; }

.fasds.fa-compress-arrows-alt::after, .fa-sharp-duotone.fa-compress-arrows-alt::after {
  content: "\f78c\f78c"; }

.fasds.fa-refrigerator::after, .fa-sharp-duotone.fa-refrigerator::after {
  content: "\e026\e026"; }

.fasds.fa-monument::after, .fa-sharp-duotone.fa-monument::after {
  content: "\f5a6\f5a6"; }

.fasds.fa-octagon-xmark::after, .fa-sharp-duotone.fa-octagon-xmark::after {
  content: "\f2f0\f2f0"; }

.fasds.fa-times-octagon::after, .fa-sharp-duotone.fa-times-octagon::after {
  content: "\f2f0\f2f0"; }

.fasds.fa-xmark-octagon::after, .fa-sharp-duotone.fa-xmark-octagon::after {
  content: "\f2f0\f2f0"; }

.fasds.fa-align-slash::after, .fa-sharp-duotone.fa-align-slash::after {
  content: "\f846\f846"; }

.fasds.fa-snowplow::after, .fa-sharp-duotone.fa-snowplow::after {
  content: "\f7d2\f7d2"; }

.fasds.fa-angles-right::after, .fa-sharp-duotone.fa-angles-right::after {
  content: "\f101\f101"; }

.fasds.fa-angle-double-right::after, .fa-sharp-duotone.fa-angle-double-right::after {
  content: "\f101\f101"; }

.fasds.fa-truck-ramp-couch::after, .fa-sharp-duotone.fa-truck-ramp-couch::after {
  content: "\f4dd\f4dd"; }

.fasds.fa-truck-couch::after, .fa-sharp-duotone.fa-truck-couch::after {
  content: "\f4dd\f4dd"; }

.fasds.fa-cannabis::after, .fa-sharp-duotone.fa-cannabis::after {
  content: "\f55f\f55f"; }

.fasds.fa-circle-play::after, .fa-sharp-duotone.fa-circle-play::after {
  content: "\f144\f144"; }

.fasds.fa-play-circle::after, .fa-sharp-duotone.fa-play-circle::after {
  content: "\f144\f144"; }

.fasds.fa-arrow-up-right-and-arrow-down-left-from-center::after, .fa-sharp-duotone.fa-arrow-up-right-and-arrow-down-left-from-center::after {
  content: "\e0a0\e0a0"; }

.fasds.fa-location-arrow-up::after, .fa-sharp-duotone.fa-location-arrow-up::after {
  content: "\e63a\e63a"; }

.fasds.fa-tablets::after, .fa-sharp-duotone.fa-tablets::after {
  content: "\f490\f490"; }

.fasds.fa-360-degrees::after, .fa-sharp-duotone.fa-360-degrees::after {
  content: "\e2dc\e2dc"; }

.fasds.fa-ethernet::after, .fa-sharp-duotone.fa-ethernet::after {
  content: "\f796\f796"; }

.fasds.fa-euro-sign::after, .fa-sharp-duotone.fa-euro-sign::after {
  content: "\f153\f153"; }

.fasds.fa-eur::after, .fa-sharp-duotone.fa-eur::after {
  content: "\f153\f153"; }

.fasds.fa-euro::after, .fa-sharp-duotone.fa-euro::after {
  content: "\f153\f153"; }

.fasds.fa-chair::after, .fa-sharp-duotone.fa-chair::after {
  content: "\f6c0\f6c0"; }

.fasds.fa-circle-check::after, .fa-sharp-duotone.fa-circle-check::after {
  content: "\f058\f058"; }

.fasds.fa-check-circle::after, .fa-sharp-duotone.fa-check-circle::after {
  content: "\f058\f058"; }

.fasds.fa-square-dashed-circle-plus::after, .fa-sharp-duotone.fa-square-dashed-circle-plus::after {
  content: "\e5c2\e5c2"; }

.fasds.fa-hand-holding-circle-dollar::after, .fa-sharp-duotone.fa-hand-holding-circle-dollar::after {
  content: "\e621\e621"; }

.fasds.fa-money-simple-from-bracket::after, .fa-sharp-duotone.fa-money-simple-from-bracket::after {
  content: "\e313\e313"; }

.fasds.fa-bat::after, .fa-sharp-duotone.fa-bat::after {
  content: "\f6b5\f6b5"; }

.fasds.fa-circle-stop::after, .fa-sharp-duotone.fa-circle-stop::after {
  content: "\f28d\f28d"; }

.fasds.fa-stop-circle::after, .fa-sharp-duotone.fa-stop-circle::after {
  content: "\f28d\f28d"; }

.fasds.fa-head-side-headphones::after, .fa-sharp-duotone.fa-head-side-headphones::after {
  content: "\f8c2\f8c2"; }

.fasds.fa-phone-rotary::after, .fa-sharp-duotone.fa-phone-rotary::after {
  content: "\f8d3\f8d3"; }

.fasds.fa-arrow-up-to-bracket::after, .fa-sharp-duotone.fa-arrow-up-to-bracket::after {
  content: "\e66a\e66a"; }

.fasds.fa-compass-drafting::after, .fa-sharp-duotone.fa-compass-drafting::after {
  content: "\f568\f568"; }

.fasds.fa-drafting-compass::after, .fa-sharp-duotone.fa-drafting-compass::after {
  content: "\f568\f568"; }

.fasds.fa-plate-wheat::after, .fa-sharp-duotone.fa-plate-wheat::after {
  content: "\e55a\e55a"; }

.fasds.fa-calendar-circle-minus::after, .fa-sharp-duotone.fa-calendar-circle-minus::after {
  content: "\e46f\e46f"; }

.fasds.fa-chopsticks::after, .fa-sharp-duotone.fa-chopsticks::after {
  content: "\e3f7\e3f7"; }

.fasds.fa-car-wrench::after, .fa-sharp-duotone.fa-car-wrench::after {
  content: "\f5e3\f5e3"; }

.fasds.fa-car-mechanic::after, .fa-sharp-duotone.fa-car-mechanic::after {
  content: "\f5e3\f5e3"; }

.fasds.fa-icicles::after, .fa-sharp-duotone.fa-icicles::after {
  content: "\f7ad\f7ad"; }

.fasds.fa-person-shelter::after, .fa-sharp-duotone.fa-person-shelter::after {
  content: "\e54f\e54f"; }

.fasds.fa-neuter::after, .fa-sharp-duotone.fa-neuter::after {
  content: "\f22c\f22c"; }

.fasds.fa-id-badge::after, .fa-sharp-duotone.fa-id-badge::after {
  content: "\f2c1\f2c1"; }

.fasds.fa-kazoo::after, .fa-sharp-duotone.fa-kazoo::after {
  content: "\f8c7\f8c7"; }

.fasds.fa-marker::after, .fa-sharp-duotone.fa-marker::after {
  content: "\f5a1\f5a1"; }

.fasds.fa-bin-bottles::after, .fa-sharp-duotone.fa-bin-bottles::after {
  content: "\e5f5\e5f5"; }

.fasds.fa-face-laugh-beam::after, .fa-sharp-duotone.fa-face-laugh-beam::after {
  content: "\f59a\f59a"; }

.fasds.fa-laugh-beam::after, .fa-sharp-duotone.fa-laugh-beam::after {
  content: "\f59a\f59a"; }

.fasds.fa-square-arrow-down-left::after, .fa-sharp-duotone.fa-square-arrow-down-left::after {
  content: "\e261\e261"; }

.fasds.fa-battery-bolt::after, .fa-sharp-duotone.fa-battery-bolt::after {
  content: "\f376\f376"; }

.fasds.fa-tree-large::after, .fa-sharp-duotone.fa-tree-large::after {
  content: "\f7dd\f7dd"; }

.fasds.fa-helicopter-symbol::after, .fa-sharp-duotone.fa-helicopter-symbol::after {
  content: "\e502\e502"; }

.fasds.fa-aperture::after, .fa-sharp-duotone.fa-aperture::after {
  content: "\e2df\e2df"; }

.fasds.fa-universal-access::after, .fa-sharp-duotone.fa-universal-access::after {
  content: "\f29a\f29a"; }

.fasds.fa-gear-complex::after, .fa-sharp-duotone.fa-gear-complex::after {
  content: "\e5e9\e5e9"; }

.fasds.fa-file-magnifying-glass::after, .fa-sharp-duotone.fa-file-magnifying-glass::after {
  content: "\f865\f865"; }

.fasds.fa-file-search::after, .fa-sharp-duotone.fa-file-search::after {
  content: "\f865\f865"; }

.fasds.fa-up-right::after, .fa-sharp-duotone.fa-up-right::after {
  content: "\e2be\e2be"; }

.fasds.fa-circle-chevron-up::after, .fa-sharp-duotone.fa-circle-chevron-up::after {
  content: "\f139\f139"; }

.fasds.fa-chevron-circle-up::after, .fa-sharp-duotone.fa-chevron-circle-up::after {
  content: "\f139\f139"; }

.fasds.fa-user-police::after, .fa-sharp-duotone.fa-user-police::after {
  content: "\e333\e333"; }

.fasds.fa-lari-sign::after, .fa-sharp-duotone.fa-lari-sign::after {
  content: "\e1c8\e1c8"; }

.fasds.fa-volcano::after, .fa-sharp-duotone.fa-volcano::after {
  content: "\f770\f770"; }

.fasds.fa-teddy-bear::after, .fa-sharp-duotone.fa-teddy-bear::after {
  content: "\e3cf\e3cf"; }

.fasds.fa-stocking::after, .fa-sharp-duotone.fa-stocking::after {
  content: "\f7d5\f7d5"; }

.fasds.fa-person-walking-dashed-line-arrow-right::after, .fa-sharp-duotone.fa-person-walking-dashed-line-arrow-right::after {
  content: "\e553\e553"; }

.fasds.fa-image-slash::after, .fa-sharp-duotone.fa-image-slash::after {
  content: "\e1b7\e1b7"; }

.fasds.fa-mask-snorkel::after, .fa-sharp-duotone.fa-mask-snorkel::after {
  content: "\e3b7\e3b7"; }

.fasds.fa-smoke::after, .fa-sharp-duotone.fa-smoke::after {
  content: "\f760\f760"; }

.fasds.fa-sterling-sign::after, .fa-sharp-duotone.fa-sterling-sign::after {
  content: "\f154\f154"; }

.fasds.fa-gbp::after, .fa-sharp-duotone.fa-gbp::after {
  content: "\f154\f154"; }

.fasds.fa-pound-sign::after, .fa-sharp-duotone.fa-pound-sign::after {
  content: "\f154\f154"; }

.fasds.fa-battery-exclamation::after, .fa-sharp-duotone.fa-battery-exclamation::after {
  content: "\e0b0\e0b0"; }

.fasds.fa-viruses::after, .fa-sharp-duotone.fa-viruses::after {
  content: "\e076\e076"; }

.fasds.fa-square-person-confined::after, .fa-sharp-duotone.fa-square-person-confined::after {
  content: "\e577\e577"; }

.fasds.fa-user-tie::after, .fa-sharp-duotone.fa-user-tie::after {
  content: "\f508\f508"; }

.fasds.fa-up-to-bracket::after, .fa-sharp-duotone.fa-up-to-bracket::after {
  content: "\e66e\e66e"; }

.fasds.fa-arrow-down-long::after, .fa-sharp-duotone.fa-arrow-down-long::after {
  content: "\f175\f175"; }

.fasds.fa-long-arrow-down::after, .fa-sharp-duotone.fa-long-arrow-down::after {
  content: "\f175\f175"; }

.fasds.fa-tent-arrow-down-to-line::after, .fa-sharp-duotone.fa-tent-arrow-down-to-line::after {
  content: "\e57e\e57e"; }

.fasds.fa-certificate::after, .fa-sharp-duotone.fa-certificate::after {
  content: "\f0a3\f0a3"; }

.fasds.fa-crystal-ball::after, .fa-sharp-duotone.fa-crystal-ball::after {
  content: "\e362\e362"; }

.fasds.fa-reply-all::after, .fa-sharp-duotone.fa-reply-all::after {
  content: "\f122\f122"; }

.fasds.fa-mail-reply-all::after, .fa-sharp-duotone.fa-mail-reply-all::after {
  content: "\f122\f122"; }

.fasds.fa-suitcase::after, .fa-sharp-duotone.fa-suitcase::after {
  content: "\f0f2\f0f2"; }

.fasds.fa-person-skating::after, .fa-sharp-duotone.fa-person-skating::after {
  content: "\f7c5\f7c5"; }

.fasds.fa-skating::after, .fa-sharp-duotone.fa-skating::after {
  content: "\f7c5\f7c5"; }

.fasds.fa-star-shooting::after, .fa-sharp-duotone.fa-star-shooting::after {
  content: "\e036\e036"; }

.fasds.fa-binary-lock::after, .fa-sharp-duotone.fa-binary-lock::after {
  content: "\e33d\e33d"; }

.fasds.fa-filter-circle-dollar::after, .fa-sharp-duotone.fa-filter-circle-dollar::after {
  content: "\f662\f662"; }

.fasds.fa-funnel-dollar::after, .fa-sharp-duotone.fa-funnel-dollar::after {
  content: "\f662\f662"; }

.fasds.fa-camera-retro::after, .fa-sharp-duotone.fa-camera-retro::after {
  content: "\f083\f083"; }

.fasds.fa-circle-arrow-down::after, .fa-sharp-duotone.fa-circle-arrow-down::after {
  content: "\f0ab\f0ab"; }

.fasds.fa-arrow-circle-down::after, .fa-sharp-duotone.fa-arrow-circle-down::after {
  content: "\f0ab\f0ab"; }

.fasds.fa-comment-pen::after, .fa-sharp-duotone.fa-comment-pen::after {
  content: "\f4ae\f4ae"; }

.fasds.fa-comment-edit::after, .fa-sharp-duotone.fa-comment-edit::after {
  content: "\f4ae\f4ae"; }

.fasds.fa-file-import::after, .fa-sharp-duotone.fa-file-import::after {
  content: "\f56f\f56f"; }

.fasds.fa-arrow-right-to-file::after, .fa-sharp-duotone.fa-arrow-right-to-file::after {
  content: "\f56f\f56f"; }

.fasds.fa-banjo::after, .fa-sharp-duotone.fa-banjo::after {
  content: "\f8a3\f8a3"; }

.fasds.fa-square-arrow-up-right::after, .fa-sharp-duotone.fa-square-arrow-up-right::after {
  content: "\f14c\f14c"; }

.fasds.fa-external-link-square::after, .fa-sharp-duotone.fa-external-link-square::after {
  content: "\f14c\f14c"; }

.fasds.fa-light-emergency-on::after, .fa-sharp-duotone.fa-light-emergency-on::after {
  content: "\e420\e420"; }

.fasds.fa-kerning::after, .fa-sharp-duotone.fa-kerning::after {
  content: "\f86f\f86f"; }

.fasds.fa-box-open::after, .fa-sharp-duotone.fa-box-open::after {
  content: "\f49e\f49e"; }

.fasds.fa-square-f::after, .fa-sharp-duotone.fa-square-f::after {
  content: "\e270\e270"; }

.fasds.fa-scroll::after, .fa-sharp-duotone.fa-scroll::after {
  content: "\f70e\f70e"; }

.fasds.fa-spa::after, .fa-sharp-duotone.fa-spa::after {
  content: "\f5bb\f5bb"; }

.fasds.fa-arrow-left-from-line::after, .fa-sharp-duotone.fa-arrow-left-from-line::after {
  content: "\f344\f344"; }

.fasds.fa-arrow-from-right::after, .fa-sharp-duotone.fa-arrow-from-right::after {
  content: "\f344\f344"; }

.fasds.fa-strawberry::after, .fa-sharp-duotone.fa-strawberry::after {
  content: "\e32b\e32b"; }

.fasds.fa-location-pin-lock::after, .fa-sharp-duotone.fa-location-pin-lock::after {
  content: "\e51f\e51f"; }

.fasds.fa-pause::after, .fa-sharp-duotone.fa-pause::after {
  content: "\f04c\f04c"; }

.fasds.fa-clock-eight-thirty::after, .fa-sharp-duotone.fa-clock-eight-thirty::after {
  content: "\e346\e346"; }

.fasds.fa-plane-engines::after, .fa-sharp-duotone.fa-plane-engines::after {
  content: "\f3de\f3de"; }

.fasds.fa-plane-alt::after, .fa-sharp-duotone.fa-plane-alt::after {
  content: "\f3de\f3de"; }

.fasds.fa-hill-avalanche::after, .fa-sharp-duotone.fa-hill-avalanche::after {
  content: "\e507\e507"; }

.fasds.fa-temperature-empty::after, .fa-sharp-duotone.fa-temperature-empty::after {
  content: "\f2cb\f2cb"; }

.fasds.fa-temperature-0::after, .fa-sharp-duotone.fa-temperature-0::after {
  content: "\f2cb\f2cb"; }

.fasds.fa-thermometer-0::after, .fa-sharp-duotone.fa-thermometer-0::after {
  content: "\f2cb\f2cb"; }

.fasds.fa-thermometer-empty::after, .fa-sharp-duotone.fa-thermometer-empty::after {
  content: "\f2cb\f2cb"; }

.fasds.fa-bomb::after, .fa-sharp-duotone.fa-bomb::after {
  content: "\f1e2\f1e2"; }

.fasds.fa-gauge-low::after, .fa-sharp-duotone.fa-gauge-low::after {
  content: "\f627\f627"; }

.fasds.fa-tachometer-alt-slow::after, .fa-sharp-duotone.fa-tachometer-alt-slow::after {
  content: "\f627\f627"; }

.fasds.fa-registered::after, .fa-sharp-duotone.fa-registered::after {
  content: "\f25d\f25d"; }

.fasds.fa-trash-can-plus::after, .fa-sharp-duotone.fa-trash-can-plus::after {
  content: "\e2ac\e2ac"; }

.fasds.fa-address-card::after, .fa-sharp-duotone.fa-address-card::after {
  content: "\f2bb\f2bb"; }

.fasds.fa-contact-card::after, .fa-sharp-duotone.fa-contact-card::after {
  content: "\f2bb\f2bb"; }

.fasds.fa-vcard::after, .fa-sharp-duotone.fa-vcard::after {
  content: "\f2bb\f2bb"; }

.fasds.fa-scale-unbalanced-flip::after, .fa-sharp-duotone.fa-scale-unbalanced-flip::after {
  content: "\f516\f516"; }

.fasds.fa-balance-scale-right::after, .fa-sharp-duotone.fa-balance-scale-right::after {
  content: "\f516\f516"; }

.fasds.fa-globe-snow::after, .fa-sharp-duotone.fa-globe-snow::after {
  content: "\f7a3\f7a3"; }

.fasds.fa-subscript::after, .fa-sharp-duotone.fa-subscript::after {
  content: "\f12c\f12c"; }

.fasds.fa-diamond-turn-right::after, .fa-sharp-duotone.fa-diamond-turn-right::after {
  content: "\f5eb\f5eb"; }

.fasds.fa-directions::after, .fa-sharp-duotone.fa-directions::after {
  content: "\f5eb\f5eb"; }

.fasds.fa-integral::after, .fa-sharp-duotone.fa-integral::after {
  content: "\f667\f667"; }

.fasds.fa-burst::after, .fa-sharp-duotone.fa-burst::after {
  content: "\e4dc\e4dc"; }

.fasds.fa-house-laptop::after, .fa-sharp-duotone.fa-house-laptop::after {
  content: "\e066\e066"; }

.fasds.fa-laptop-house::after, .fa-sharp-duotone.fa-laptop-house::after {
  content: "\e066\e066"; }

.fasds.fa-face-tired::after, .fa-sharp-duotone.fa-face-tired::after {
  content: "\f5c8\f5c8"; }

.fasds.fa-tired::after, .fa-sharp-duotone.fa-tired::after {
  content: "\f5c8\f5c8"; }

.fasds.fa-money-bills::after, .fa-sharp-duotone.fa-money-bills::after {
  content: "\e1f3\e1f3"; }

.fasds.fa-blinds-raised::after, .fa-sharp-duotone.fa-blinds-raised::after {
  content: "\f8fd\f8fd"; }

.fasds.fa-smog::after, .fa-sharp-duotone.fa-smog::after {
  content: "\f75f\f75f"; }

.fasds.fa-ufo-beam::after, .fa-sharp-duotone.fa-ufo-beam::after {
  content: "\e048\e048"; }

.fasds.fa-hydra::after, .fa-sharp-duotone.fa-hydra::after {
  content: "\e686\e686"; }

.fasds.fa-circle-caret-up::after, .fa-sharp-duotone.fa-circle-caret-up::after {
  content: "\f331\f331"; }

.fasds.fa-caret-circle-up::after, .fa-sharp-duotone.fa-caret-circle-up::after {
  content: "\f331\f331"; }

.fasds.fa-user-vneck-hair-long::after, .fa-sharp-duotone.fa-user-vneck-hair-long::after {
  content: "\e463\e463"; }

.fasds.fa-square-a-lock::after, .fa-sharp-duotone.fa-square-a-lock::after {
  content: "\e44d\e44d"; }

.fasds.fa-crutch::after, .fa-sharp-duotone.fa-crutch::after {
  content: "\f7f7\f7f7"; }

.fasds.fa-gas-pump-slash::after, .fa-sharp-duotone.fa-gas-pump-slash::after {
  content: "\f5f4\f5f4"; }

.fasds.fa-cloud-arrow-up::after, .fa-sharp-duotone.fa-cloud-arrow-up::after {
  content: "\f0ee\f0ee"; }

.fasds.fa-cloud-upload::after, .fa-sharp-duotone.fa-cloud-upload::after {
  content: "\f0ee\f0ee"; }

.fasds.fa-cloud-upload-alt::after, .fa-sharp-duotone.fa-cloud-upload-alt::after {
  content: "\f0ee\f0ee"; }

.fasds.fa-palette::after, .fa-sharp-duotone.fa-palette::after {
  content: "\f53f\f53f"; }

.fasds.fa-transporter-4::after, .fa-sharp-duotone.fa-transporter-4::after {
  content: "\e2a5\e2a5"; }

.fasds.fa-chart-mixed-up-circle-currency::after, .fa-sharp-duotone.fa-chart-mixed-up-circle-currency::after {
  content: "\e5d8\e5d8"; }

.fasds.fa-objects-align-right::after, .fa-sharp-duotone.fa-objects-align-right::after {
  content: "\e3bf\e3bf"; }

.fasds.fa-arrows-turn-right::after, .fa-sharp-duotone.fa-arrows-turn-right::after {
  content: "\e4c0\e4c0"; }

.fasds.fa-vest::after, .fa-sharp-duotone.fa-vest::after {
  content: "\e085\e085"; }

.fasds.fa-pig::after, .fa-sharp-duotone.fa-pig::after {
  content: "\f706\f706"; }

.fasds.fa-inbox-full::after, .fa-sharp-duotone.fa-inbox-full::after {
  content: "\e1ba\e1ba"; }

.fasds.fa-circle-envelope::after, .fa-sharp-duotone.fa-circle-envelope::after {
  content: "\e10c\e10c"; }

.fasds.fa-envelope-circle::after, .fa-sharp-duotone.fa-envelope-circle::after {
  content: "\e10c\e10c"; }

.fasds.fa-triangle-person-digging::after, .fa-sharp-duotone.fa-triangle-person-digging::after {
  content: "\f85d\f85d"; }

.fasds.fa-construction::after, .fa-sharp-duotone.fa-construction::after {
  content: "\f85d\f85d"; }

.fasds.fa-ferry::after, .fa-sharp-duotone.fa-ferry::after {
  content: "\e4ea\e4ea"; }

.fasds.fa-bullseye-arrow::after, .fa-sharp-duotone.fa-bullseye-arrow::after {
  content: "\f648\f648"; }

.fasds.fa-arrows-down-to-people::after, .fa-sharp-duotone.fa-arrows-down-to-people::after {
  content: "\e4b9\e4b9"; }

.fasds.fa-seedling::after, .fa-sharp-duotone.fa-seedling::after {
  content: "\f4d8\f4d8"; }

.fasds.fa-sprout::after, .fa-sharp-duotone.fa-sprout::after {
  content: "\f4d8\f4d8"; }

.fasds.fa-clock-seven::after, .fa-sharp-duotone.fa-clock-seven::after {
  content: "\e350\e350"; }

.fasds.fa-left-right::after, .fa-sharp-duotone.fa-left-right::after {
  content: "\f337\f337"; }

.fasds.fa-arrows-alt-h::after, .fa-sharp-duotone.fa-arrows-alt-h::after {
  content: "\f337\f337"; }

.fasds.fa-boxes-packing::after, .fa-sharp-duotone.fa-boxes-packing::after {
  content: "\e4c7\e4c7"; }

.fasds.fa-circle-arrow-left::after, .fa-sharp-duotone.fa-circle-arrow-left::after {
  content: "\f0a8\f0a8"; }

.fasds.fa-arrow-circle-left::after, .fa-sharp-duotone.fa-arrow-circle-left::after {
  content: "\f0a8\f0a8"; }

.fasds.fa-flashlight::after, .fa-sharp-duotone.fa-flashlight::after {
  content: "\f8b8\f8b8"; }

.fasds.fa-file-jpg::after, .fa-sharp-duotone.fa-file-jpg::after {
  content: "\e646\e646"; }

.fasds.fa-group-arrows-rotate::after, .fa-sharp-duotone.fa-group-arrows-rotate::after {
  content: "\e4f6\e4f6"; }

.fasds.fa-bowl-food::after, .fa-sharp-duotone.fa-bowl-food::after {
  content: "\e4c6\e4c6"; }

.fasds.fa-square-9::after, .fa-sharp-duotone.fa-square-9::after {
  content: "\e25e\e25e"; }

.fasds.fa-candy-cane::after, .fa-sharp-duotone.fa-candy-cane::after {
  content: "\f786\f786"; }

.fasds.fa-arrow-down-wide-short::after, .fa-sharp-duotone.fa-arrow-down-wide-short::after {
  content: "\f160\f160"; }

.fasds.fa-sort-amount-asc::after, .fa-sharp-duotone.fa-sort-amount-asc::after {
  content: "\f160\f160"; }

.fasds.fa-sort-amount-down::after, .fa-sharp-duotone.fa-sort-amount-down::after {
  content: "\f160\f160"; }

.fasds.fa-square-dollar::after, .fa-sharp-duotone.fa-square-dollar::after {
  content: "\f2e9\f2e9"; }

.fasds.fa-dollar-square::after, .fa-sharp-duotone.fa-dollar-square::after {
  content: "\f2e9\f2e9"; }

.fasds.fa-usd-square::after, .fa-sharp-duotone.fa-usd-square::after {
  content: "\f2e9\f2e9"; }

.fasds.fa-phone-arrow-right::after, .fa-sharp-duotone.fa-phone-arrow-right::after {
  content: "\e5be\e5be"; }

.fasds.fa-hand-holding-seedling::after, .fa-sharp-duotone.fa-hand-holding-seedling::after {
  content: "\f4bf\f4bf"; }

.fasds.fa-message-check::after, .fa-sharp-duotone.fa-message-check::after {
  content: "\f4a2\f4a2"; }

.fasds.fa-comment-alt-check::after, .fa-sharp-duotone.fa-comment-alt-check::after {
  content: "\f4a2\f4a2"; }

.fasds.fa-cloud-bolt::after, .fa-sharp-duotone.fa-cloud-bolt::after {
  content: "\f76c\f76c"; }

.fasds.fa-thunderstorm::after, .fa-sharp-duotone.fa-thunderstorm::after {
  content: "\f76c\f76c"; }

.fasds.fa-chart-line-up-down::after, .fa-sharp-duotone.fa-chart-line-up-down::after {
  content: "\e5d7\e5d7"; }

.fasds.fa-text-slash::after, .fa-sharp-duotone.fa-text-slash::after {
  content: "\f87d\f87d"; }

.fasds.fa-remove-format::after, .fa-sharp-duotone.fa-remove-format::after {
  content: "\f87d\f87d"; }

.fasds.fa-watch::after, .fa-sharp-duotone.fa-watch::after {
  content: "\f2e1\f2e1"; }

.fasds.fa-circle-down-left::after, .fa-sharp-duotone.fa-circle-down-left::after {
  content: "\e107\e107"; }

.fasds.fa-text::after, .fa-sharp-duotone.fa-text::after {
  content: "\f893\f893"; }

.fasds.fa-projector::after, .fa-sharp-duotone.fa-projector::after {
  content: "\f8d6\f8d6"; }

.fasds.fa-face-smile-wink::after, .fa-sharp-duotone.fa-face-smile-wink::after {
  content: "\f4da\f4da"; }

.fasds.fa-smile-wink::after, .fa-sharp-duotone.fa-smile-wink::after {
  content: "\f4da\f4da"; }

.fasds.fa-tombstone-blank::after, .fa-sharp-duotone.fa-tombstone-blank::after {
  content: "\f721\f721"; }

.fasds.fa-tombstone-alt::after, .fa-sharp-duotone.fa-tombstone-alt::after {
  content: "\f721\f721"; }

.fasds.fa-chess-king-piece::after, .fa-sharp-duotone.fa-chess-king-piece::after {
  content: "\f440\f440"; }

.fasds.fa-chess-king-alt::after, .fa-sharp-duotone.fa-chess-king-alt::after {
  content: "\f440\f440"; }

.fasds.fa-circle-6::after, .fa-sharp-duotone.fa-circle-6::after {
  content: "\e0f3\e0f3"; }

.fasds.fa-waves-sine::after, .fa-sharp-duotone.fa-waves-sine::after {
  content: "\e65d\e65d"; }

.fasds.fa-left::after, .fa-sharp-duotone.fa-left::after {
  content: "\f355\f355"; }

.fasds.fa-arrow-alt-left::after, .fa-sharp-duotone.fa-arrow-alt-left::after {
  content: "\f355\f355"; }

.fasds.fa-file-word::after, .fa-sharp-duotone.fa-file-word::after {
  content: "\f1c2\f1c2"; }

.fasds.fa-file-powerpoint::after, .fa-sharp-duotone.fa-file-powerpoint::after {
  content: "\f1c4\f1c4"; }

.fasds.fa-square-down::after, .fa-sharp-duotone.fa-square-down::after {
  content: "\f350\f350"; }

.fasds.fa-arrow-alt-square-down::after, .fa-sharp-duotone.fa-arrow-alt-square-down::after {
  content: "\f350\f350"; }

.fasds.fa-objects-align-center-vertical::after, .fa-sharp-duotone.fa-objects-align-center-vertical::after {
  content: "\e3bd\e3bd"; }

.fasds.fa-arrows-left-right::after, .fa-sharp-duotone.fa-arrows-left-right::after {
  content: "\f07e\f07e"; }

.fasds.fa-arrows-h::after, .fa-sharp-duotone.fa-arrows-h::after {
  content: "\f07e\f07e"; }

.fasds.fa-house-lock::after, .fa-sharp-duotone.fa-house-lock::after {
  content: "\e510\e510"; }

.fasds.fa-cloud-arrow-down::after, .fa-sharp-duotone.fa-cloud-arrow-down::after {
  content: "\f0ed\f0ed"; }

.fasds.fa-cloud-download::after, .fa-sharp-duotone.fa-cloud-download::after {
  content: "\f0ed\f0ed"; }

.fasds.fa-cloud-download-alt::after, .fa-sharp-duotone.fa-cloud-download-alt::after {
  content: "\f0ed\f0ed"; }

.fasds.fa-wreath::after, .fa-sharp-duotone.fa-wreath::after {
  content: "\f7e2\f7e2"; }

.fasds.fa-children::after, .fa-sharp-duotone.fa-children::after {
  content: "\e4e1\e4e1"; }

.fasds.fa-meter-droplet::after, .fa-sharp-duotone.fa-meter-droplet::after {
  content: "\e1ea\e1ea"; }

.fasds.fa-chalkboard::after, .fa-sharp-duotone.fa-chalkboard::after {
  content: "\f51b\f51b"; }

.fasds.fa-blackboard::after, .fa-sharp-duotone.fa-blackboard::after {
  content: "\f51b\f51b"; }

.fasds.fa-user-large-slash::after, .fa-sharp-duotone.fa-user-large-slash::after {
  content: "\f4fa\f4fa"; }

.fasds.fa-user-alt-slash::after, .fa-sharp-duotone.fa-user-alt-slash::after {
  content: "\f4fa\f4fa"; }

.fasds.fa-signal-strong::after, .fa-sharp-duotone.fa-signal-strong::after {
  content: "\f68f\f68f"; }

.fasds.fa-signal-4::after, .fa-sharp-duotone.fa-signal-4::after {
  content: "\f68f\f68f"; }

.fasds.fa-lollipop::after, .fa-sharp-duotone.fa-lollipop::after {
  content: "\e424\e424"; }

.fasds.fa-lollypop::after, .fa-sharp-duotone.fa-lollypop::after {
  content: "\e424\e424"; }

.fasds.fa-list-tree::after, .fa-sharp-duotone.fa-list-tree::after {
  content: "\e1d2\e1d2"; }

.fasds.fa-envelope-open::after, .fa-sharp-duotone.fa-envelope-open::after {
  content: "\f2b6\f2b6"; }

.fasds.fa-draw-circle::after, .fa-sharp-duotone.fa-draw-circle::after {
  content: "\f5ed\f5ed"; }

.fasds.fa-cat-space::after, .fa-sharp-duotone.fa-cat-space::after {
  content: "\e001\e001"; }

.fasds.fa-handshake-simple-slash::after, .fa-sharp-duotone.fa-handshake-simple-slash::after {
  content: "\e05f\e05f"; }

.fasds.fa-handshake-alt-slash::after, .fa-sharp-duotone.fa-handshake-alt-slash::after {
  content: "\e05f\e05f"; }

.fasds.fa-rabbit-running::after, .fa-sharp-duotone.fa-rabbit-running::after {
  content: "\f709\f709"; }

.fasds.fa-rabbit-fast::after, .fa-sharp-duotone.fa-rabbit-fast::after {
  content: "\f709\f709"; }

.fasds.fa-memo-pad::after, .fa-sharp-duotone.fa-memo-pad::after {
  content: "\e1da\e1da"; }

.fasds.fa-mattress-pillow::after, .fa-sharp-duotone.fa-mattress-pillow::after {
  content: "\e525\e525"; }

.fasds.fa-alarm-plus::after, .fa-sharp-duotone.fa-alarm-plus::after {
  content: "\f844\f844"; }

.fasds.fa-alicorn::after, .fa-sharp-duotone.fa-alicorn::after {
  content: "\f6b0\f6b0"; }

.fasds.fa-comment-question::after, .fa-sharp-duotone.fa-comment-question::after {
  content: "\e14b\e14b"; }

.fasds.fa-gingerbread-man::after, .fa-sharp-duotone.fa-gingerbread-man::after {
  content: "\f79d\f79d"; }

.fasds.fa-guarani-sign::after, .fa-sharp-duotone.fa-guarani-sign::after {
  content: "\e19a\e19a"; }

.fasds.fa-burger-fries::after, .fa-sharp-duotone.fa-burger-fries::after {
  content: "\e0cd\e0cd"; }

.fasds.fa-mug-tea::after, .fa-sharp-duotone.fa-mug-tea::after {
  content: "\f875\f875"; }

.fasds.fa-border-top::after, .fa-sharp-duotone.fa-border-top::after {
  content: "\f855\f855"; }

.fasds.fa-arrows-rotate::after, .fa-sharp-duotone.fa-arrows-rotate::after {
  content: "\f021\f021"; }

.fasds.fa-refresh::after, .fa-sharp-duotone.fa-refresh::after {
  content: "\f021\f021"; }

.fasds.fa-sync::after, .fa-sharp-duotone.fa-sync::after {
  content: "\f021\f021"; }

.fasds.fa-circle-book-open::after, .fa-sharp-duotone.fa-circle-book-open::after {
  content: "\e0ff\e0ff"; }

.fasds.fa-book-circle::after, .fa-sharp-duotone.fa-book-circle::after {
  content: "\e0ff\e0ff"; }

.fasds.fa-arrows-to-dotted-line::after, .fa-sharp-duotone.fa-arrows-to-dotted-line::after {
  content: "\e0a6\e0a6"; }

.fasds.fa-fire-extinguisher::after, .fa-sharp-duotone.fa-fire-extinguisher::after {
  content: "\f134\f134"; }

.fasds.fa-magnifying-glass-arrows-rotate::after, .fa-sharp-duotone.fa-magnifying-glass-arrows-rotate::after {
  content: "\e65e\e65e"; }

.fasds.fa-garage-open::after, .fa-sharp-duotone.fa-garage-open::after {
  content: "\e00b\e00b"; }

.fasds.fa-shelves-empty::after, .fa-sharp-duotone.fa-shelves-empty::after {
  content: "\e246\e246"; }

.fasds.fa-cruzeiro-sign::after, .fa-sharp-duotone.fa-cruzeiro-sign::after {
  content: "\e152\e152"; }

.fasds.fa-watch-apple::after, .fa-sharp-duotone.fa-watch-apple::after {
  content: "\e2cb\e2cb"; }

.fasds.fa-watch-calculator::after, .fa-sharp-duotone.fa-watch-calculator::after {
  content: "\f8f0\f8f0"; }

.fasds.fa-list-dropdown::after, .fa-sharp-duotone.fa-list-dropdown::after {
  content: "\e1cf\e1cf"; }

.fasds.fa-cabinet-filing::after, .fa-sharp-duotone.fa-cabinet-filing::after {
  content: "\f64b\f64b"; }

.fasds.fa-burger-soda::after, .fa-sharp-duotone.fa-burger-soda::after {
  content: "\f858\f858"; }

.fasds.fa-square-arrow-up::after, .fa-sharp-duotone.fa-square-arrow-up::after {
  content: "\f33c\f33c"; }

.fasds.fa-arrow-square-up::after, .fa-sharp-duotone.fa-arrow-square-up::after {
  content: "\f33c\f33c"; }

.fasds.fa-greater-than-equal::after, .fa-sharp-duotone.fa-greater-than-equal::after {
  content: "\f532\f532"; }

.fasds.fa-pallet-box::after, .fa-sharp-duotone.fa-pallet-box::after {
  content: "\e208\e208"; }

.fasds.fa-face-confounded::after, .fa-sharp-duotone.fa-face-confounded::after {
  content: "\e36c\e36c"; }

.fasds.fa-shield-halved::after, .fa-sharp-duotone.fa-shield-halved::after {
  content: "\f3ed\f3ed"; }

.fasds.fa-shield-alt::after, .fa-sharp-duotone.fa-shield-alt::after {
  content: "\f3ed\f3ed"; }

.fasds.fa-truck-plow::after, .fa-sharp-duotone.fa-truck-plow::after {
  content: "\f7de\f7de"; }

.fasds.fa-book-atlas::after, .fa-sharp-duotone.fa-book-atlas::after {
  content: "\f558\f558"; }

.fasds.fa-atlas::after, .fa-sharp-duotone.fa-atlas::after {
  content: "\f558\f558"; }

.fasds.fa-virus::after, .fa-sharp-duotone.fa-virus::after {
  content: "\e074\e074"; }

.fasds.fa-grid-round-2::after, .fa-sharp-duotone.fa-grid-round-2::after {
  content: "\e5db\e5db"; }

.fasds.fa-comment-middle-top::after, .fa-sharp-duotone.fa-comment-middle-top::after {
  content: "\e14a\e14a"; }

.fasds.fa-wave::after, .fa-sharp-duotone.fa-wave::after {
  content: "\e65b\e65b"; }

.fasds.fa-envelope-circle-check::after, .fa-sharp-duotone.fa-envelope-circle-check::after {
  content: "\e4e8\e4e8"; }

.fasds.fa-layer-group::after, .fa-sharp-duotone.fa-layer-group::after {
  content: "\f5fd\f5fd"; }

.fasds.fa-restroom-simple::after, .fa-sharp-duotone.fa-restroom-simple::after {
  content: "\e23a\e23a"; }

.fasds.fa-arrows-to-dot::after, .fa-sharp-duotone.fa-arrows-to-dot::after {
  content: "\e4be\e4be"; }

.fasds.fa-border-outer::after, .fa-sharp-duotone.fa-border-outer::after {
  content: "\f851\f851"; }

.fasds.fa-hashtag-lock::after, .fa-sharp-duotone.fa-hashtag-lock::after {
  content: "\e415\e415"; }

.fasds.fa-clock-two-thirty::after, .fa-sharp-duotone.fa-clock-two-thirty::after {
  content: "\e35b\e35b"; }

.fasds.fa-archway::after, .fa-sharp-duotone.fa-archway::after {
  content: "\f557\f557"; }

.fasds.fa-heart-circle-check::after, .fa-sharp-duotone.fa-heart-circle-check::after {
  content: "\e4fd\e4fd"; }

.fasds.fa-house-chimney-crack::after, .fa-sharp-duotone.fa-house-chimney-crack::after {
  content: "\f6f1\f6f1"; }

.fasds.fa-house-damage::after, .fa-sharp-duotone.fa-house-damage::after {
  content: "\f6f1\f6f1"; }

.fasds.fa-file-zipper::after, .fa-sharp-duotone.fa-file-zipper::after {
  content: "\f1c6\f1c6"; }

.fasds.fa-file-archive::after, .fa-sharp-duotone.fa-file-archive::after {
  content: "\f1c6\f1c6"; }

.fasds.fa-ticket-perforated::after, .fa-sharp-duotone.fa-ticket-perforated::after {
  content: "\e63e\e63e"; }

.fasds.fa-heart-half::after, .fa-sharp-duotone.fa-heart-half::after {
  content: "\e1ab\e1ab"; }

.fasds.fa-comment-check::after, .fa-sharp-duotone.fa-comment-check::after {
  content: "\f4ac\f4ac"; }

.fasds.fa-square::after, .fa-sharp-duotone.fa-square::after {
  content: "\f0c8\f0c8"; }

.fasds.fa-memo::after, .fa-sharp-duotone.fa-memo::after {
  content: "\e1d8\e1d8"; }

.fasds.fa-martini-glass-empty::after, .fa-sharp-duotone.fa-martini-glass-empty::after {
  content: "\f000\f000"; }

.fasds.fa-glass-martini::after, .fa-sharp-duotone.fa-glass-martini::after {
  content: "\f000\f000"; }

.fasds.fa-couch::after, .fa-sharp-duotone.fa-couch::after {
  content: "\f4b8\f4b8"; }

.fasds.fa-cedi-sign::after, .fa-sharp-duotone.fa-cedi-sign::after {
  content: "\e0df\e0df"; }

.fasds.fa-italic::after, .fa-sharp-duotone.fa-italic::after {
  content: "\f033\f033"; }

.fasds.fa-glass-citrus::after, .fa-sharp-duotone.fa-glass-citrus::after {
  content: "\f869\f869"; }

.fasds.fa-calendar-lines-pen::after, .fa-sharp-duotone.fa-calendar-lines-pen::after {
  content: "\e472\e472"; }

.fasds.fa-table-cells-column-lock::after, .fa-sharp-duotone.fa-table-cells-column-lock::after {
  content: "\e678\e678"; }

.fasds.fa-church::after, .fa-sharp-duotone.fa-church::after {
  content: "\f51d\f51d"; }

.fasds.fa-person-snowmobiling::after, .fa-sharp-duotone.fa-person-snowmobiling::after {
  content: "\f7d1\f7d1"; }

.fasds.fa-snowmobile::after, .fa-sharp-duotone.fa-snowmobile::after {
  content: "\f7d1\f7d1"; }

.fasds.fa-face-hushed::after, .fa-sharp-duotone.fa-face-hushed::after {
  content: "\e37b\e37b"; }

.fasds.fa-comments-dollar::after, .fa-sharp-duotone.fa-comments-dollar::after {
  content: "\f653\f653"; }

.fasds.fa-tickets-simple::after, .fa-sharp-duotone.fa-tickets-simple::after {
  content: "\e659\e659"; }

.fasds.fa-pickaxe::after, .fa-sharp-duotone.fa-pickaxe::after {
  content: "\e5bf\e5bf"; }

.fasds.fa-link-simple-slash::after, .fa-sharp-duotone.fa-link-simple-slash::after {
  content: "\e1ce\e1ce"; }

.fasds.fa-democrat::after, .fa-sharp-duotone.fa-democrat::after {
  content: "\f747\f747"; }

.fasds.fa-face-confused::after, .fa-sharp-duotone.fa-face-confused::after {
  content: "\e36d\e36d"; }

.fasds.fa-pinball::after, .fa-sharp-duotone.fa-pinball::after {
  content: "\e229\e229"; }

.fasds.fa-z::after, .fa-sharp-duotone.fa-z::after {
  content: "\5a\5a"; }

.fasds.fa-person-skiing::after, .fa-sharp-duotone.fa-person-skiing::after {
  content: "\f7c9\f7c9"; }

.fasds.fa-skiing::after, .fa-sharp-duotone.fa-skiing::after {
  content: "\f7c9\f7c9"; }

.fasds.fa-deer::after, .fa-sharp-duotone.fa-deer::after {
  content: "\f78e\f78e"; }

.fasds.fa-input-pipe::after, .fa-sharp-duotone.fa-input-pipe::after {
  content: "\e1be\e1be"; }

.fasds.fa-road-lock::after, .fa-sharp-duotone.fa-road-lock::after {
  content: "\e567\e567"; }

.fasds.fa-a::after, .fa-sharp-duotone.fa-a::after {
  content: "\41\41"; }

.fasds.fa-bookmark-slash::after, .fa-sharp-duotone.fa-bookmark-slash::after {
  content: "\e0c2\e0c2"; }

.fasds.fa-temperature-arrow-down::after, .fa-sharp-duotone.fa-temperature-arrow-down::after {
  content: "\e03f\e03f"; }

.fasds.fa-temperature-down::after, .fa-sharp-duotone.fa-temperature-down::after {
  content: "\e03f\e03f"; }

.fasds.fa-mace::after, .fa-sharp-duotone.fa-mace::after {
  content: "\f6f8\f6f8"; }

.fasds.fa-feather-pointed::after, .fa-sharp-duotone.fa-feather-pointed::after {
  content: "\f56b\f56b"; }

.fasds.fa-feather-alt::after, .fa-sharp-duotone.fa-feather-alt::after {
  content: "\f56b\f56b"; }

.fasds.fa-sausage::after, .fa-sharp-duotone.fa-sausage::after {
  content: "\f820\f820"; }

.fasds.fa-trash-can-clock::after, .fa-sharp-duotone.fa-trash-can-clock::after {
  content: "\e2aa\e2aa"; }

.fasds.fa-p::after, .fa-sharp-duotone.fa-p::after {
  content: "\50\50"; }

.fasds.fa-broom-wide::after, .fa-sharp-duotone.fa-broom-wide::after {
  content: "\e5d1\e5d1"; }

.fasds.fa-snowflake::after, .fa-sharp-duotone.fa-snowflake::after {
  content: "\f2dc\f2dc"; }

.fasds.fa-stomach::after, .fa-sharp-duotone.fa-stomach::after {
  content: "\f623\f623"; }

.fasds.fa-newspaper::after, .fa-sharp-duotone.fa-newspaper::after {
  content: "\f1ea\f1ea"; }

.fasds.fa-rectangle-ad::after, .fa-sharp-duotone.fa-rectangle-ad::after {
  content: "\f641\f641"; }

.fasds.fa-ad::after, .fa-sharp-duotone.fa-ad::after {
  content: "\f641\f641"; }

.fasds.fa-guitar-electric::after, .fa-sharp-duotone.fa-guitar-electric::after {
  content: "\f8be\f8be"; }

.fasds.fa-arrow-turn-down-right::after, .fa-sharp-duotone.fa-arrow-turn-down-right::after {
  content: "\e3d6\e3d6"; }

.fasds.fa-moon-cloud::after, .fa-sharp-duotone.fa-moon-cloud::after {
  content: "\f754\f754"; }

.fasds.fa-bread-slice-butter::after, .fa-sharp-duotone.fa-bread-slice-butter::after {
  content: "\e3e1\e3e1"; }

.fasds.fa-circle-arrow-right::after, .fa-sharp-duotone.fa-circle-arrow-right::after {
  content: "\f0a9\f0a9"; }

.fasds.fa-arrow-circle-right::after, .fa-sharp-duotone.fa-arrow-circle-right::after {
  content: "\f0a9\f0a9"; }

.fasds.fa-user-group-crown::after, .fa-sharp-duotone.fa-user-group-crown::after {
  content: "\f6a5\f6a5"; }

.fasds.fa-users-crown::after, .fa-sharp-duotone.fa-users-crown::after {
  content: "\f6a5\f6a5"; }

.fasds.fa-circle-i::after, .fa-sharp-duotone.fa-circle-i::after {
  content: "\e111\e111"; }

.fasds.fa-toilet-paper-check::after, .fa-sharp-duotone.fa-toilet-paper-check::after {
  content: "\e5b2\e5b2"; }

.fasds.fa-filter-circle-xmark::after, .fa-sharp-duotone.fa-filter-circle-xmark::after {
  content: "\e17b\e17b"; }

.fasds.fa-locust::after, .fa-sharp-duotone.fa-locust::after {
  content: "\e520\e520"; }

.fasds.fa-sort::after, .fa-sharp-duotone.fa-sort::after {
  content: "\f0dc\f0dc"; }

.fasds.fa-unsorted::after, .fa-sharp-duotone.fa-unsorted::after {
  content: "\f0dc\f0dc"; }

.fasds.fa-list-ol::after, .fa-sharp-duotone.fa-list-ol::after {
  content: "\f0cb\f0cb"; }

.fasds.fa-list-1-2::after, .fa-sharp-duotone.fa-list-1-2::after {
  content: "\f0cb\f0cb"; }

.fasds.fa-list-numeric::after, .fa-sharp-duotone.fa-list-numeric::after {
  content: "\f0cb\f0cb"; }

.fasds.fa-chart-waterfall::after, .fa-sharp-duotone.fa-chart-waterfall::after {
  content: "\e0eb\e0eb"; }

.fasds.fa-sparkle::after, .fa-sharp-duotone.fa-sparkle::after {
  content: "\e5d6\e5d6"; }

.fasds.fa-face-party::after, .fa-sharp-duotone.fa-face-party::after {
  content: "\e383\e383"; }

.fasds.fa-kidneys::after, .fa-sharp-duotone.fa-kidneys::after {
  content: "\f5fb\f5fb"; }

.fasds.fa-wifi-exclamation::after, .fa-sharp-duotone.fa-wifi-exclamation::after {
  content: "\e2cf\e2cf"; }

.fasds.fa-chart-network::after, .fa-sharp-duotone.fa-chart-network::after {
  content: "\f78a\f78a"; }

.fasds.fa-person-dress-burst::after, .fa-sharp-duotone.fa-person-dress-burst::after {
  content: "\e544\e544"; }

.fasds.fa-dice-d4::after, .fa-sharp-duotone.fa-dice-d4::after {
  content: "\f6d0\f6d0"; }

.fasds.fa-money-check-dollar::after, .fa-sharp-duotone.fa-money-check-dollar::after {
  content: "\f53d\f53d"; }

.fasds.fa-money-check-alt::after, .fa-sharp-duotone.fa-money-check-alt::after {
  content: "\f53d\f53d"; }

.fasds.fa-vector-square::after, .fa-sharp-duotone.fa-vector-square::after {
  content: "\f5cb\f5cb"; }

.fasds.fa-bread-slice::after, .fa-sharp-duotone.fa-bread-slice::after {
  content: "\f7ec\f7ec"; }

.fasds.fa-language::after, .fa-sharp-duotone.fa-language::after {
  content: "\f1ab\f1ab"; }

.fasds.fa-wheat-awn-slash::after, .fa-sharp-duotone.fa-wheat-awn-slash::after {
  content: "\e338\e338"; }

.fasds.fa-face-kiss-wink-heart::after, .fa-sharp-duotone.fa-face-kiss-wink-heart::after {
  content: "\f598\f598"; }

.fasds.fa-kiss-wink-heart::after, .fa-sharp-duotone.fa-kiss-wink-heart::after {
  content: "\f598\f598"; }

.fasds.fa-dagger::after, .fa-sharp-duotone.fa-dagger::after {
  content: "\f6cb\f6cb"; }

.fasds.fa-podium::after, .fa-sharp-duotone.fa-podium::after {
  content: "\f680\f680"; }

.fasds.fa-diamonds-4::after, .fa-sharp-duotone.fa-diamonds-4::after {
  content: "\e68b\e68b"; }

.fasds.fa-memo-circle-check::after, .fa-sharp-duotone.fa-memo-circle-check::after {
  content: "\e1d9\e1d9"; }

.fasds.fa-route-highway::after, .fa-sharp-duotone.fa-route-highway::after {
  content: "\f61a\f61a"; }

.fasds.fa-down-to-line::after, .fa-sharp-duotone.fa-down-to-line::after {
  content: "\f34a\f34a"; }

.fasds.fa-arrow-alt-to-bottom::after, .fa-sharp-duotone.fa-arrow-alt-to-bottom::after {
  content: "\f34a\f34a"; }

.fasds.fa-filter::after, .fa-sharp-duotone.fa-filter::after {
  content: "\f0b0\f0b0"; }

.fasds.fa-square-g::after, .fa-sharp-duotone.fa-square-g::after {
  content: "\e271\e271"; }

.fasds.fa-circle-phone::after, .fa-sharp-duotone.fa-circle-phone::after {
  content: "\e11b\e11b"; }

.fasds.fa-phone-circle::after, .fa-sharp-duotone.fa-phone-circle::after {
  content: "\e11b\e11b"; }

.fasds.fa-clipboard-prescription::after, .fa-sharp-duotone.fa-clipboard-prescription::after {
  content: "\f5e8\f5e8"; }

.fasds.fa-user-nurse-hair::after, .fa-sharp-duotone.fa-user-nurse-hair::after {
  content: "\e45d\e45d"; }

.fasds.fa-question::after, .fa-sharp-duotone.fa-question::after {
  content: "\3f\3f"; }

.fasds.fa-file-signature::after, .fa-sharp-duotone.fa-file-signature::after {
  content: "\f573\f573"; }

.fasds.fa-toggle-large-on::after, .fa-sharp-duotone.fa-toggle-large-on::after {
  content: "\e5b1\e5b1"; }

.fasds.fa-up-down-left-right::after, .fa-sharp-duotone.fa-up-down-left-right::after {
  content: "\f0b2\f0b2"; }

.fasds.fa-arrows-alt::after, .fa-sharp-duotone.fa-arrows-alt::after {
  content: "\f0b2\f0b2"; }

.fasds.fa-dryer-heat::after, .fa-sharp-duotone.fa-dryer-heat::after {
  content: "\f862\f862"; }

.fasds.fa-dryer-alt::after, .fa-sharp-duotone.fa-dryer-alt::after {
  content: "\f862\f862"; }

.fasds.fa-house-chimney-user::after, .fa-sharp-duotone.fa-house-chimney-user::after {
  content: "\e065\e065"; }

.fasds.fa-hand-holding-heart::after, .fa-sharp-duotone.fa-hand-holding-heart::after {
  content: "\f4be\f4be"; }

.fasds.fa-arrow-up-small-big::after, .fa-sharp-duotone.fa-arrow-up-small-big::after {
  content: "\f88f\f88f"; }

.fasds.fa-sort-size-up-alt::after, .fa-sharp-duotone.fa-sort-size-up-alt::after {
  content: "\f88f\f88f"; }

.fasds.fa-train-track::after, .fa-sharp-duotone.fa-train-track::after {
  content: "\e453\e453"; }

.fasds.fa-puzzle-piece::after, .fa-sharp-duotone.fa-puzzle-piece::after {
  content: "\f12e\f12e"; }

.fasds.fa-money-check::after, .fa-sharp-duotone.fa-money-check::after {
  content: "\f53c\f53c"; }

.fasds.fa-star-half-stroke::after, .fa-sharp-duotone.fa-star-half-stroke::after {
  content: "\f5c0\f5c0"; }

.fasds.fa-star-half-alt::after, .fa-sharp-duotone.fa-star-half-alt::after {
  content: "\f5c0\f5c0"; }

.fasds.fa-file-exclamation::after, .fa-sharp-duotone.fa-file-exclamation::after {
  content: "\f31a\f31a"; }

.fasds.fa-code::after, .fa-sharp-duotone.fa-code::after {
  content: "\f121\f121"; }

.fasds.fa-whiskey-glass::after, .fa-sharp-duotone.fa-whiskey-glass::after {
  content: "\f7a0\f7a0"; }

.fasds.fa-glass-whiskey::after, .fa-sharp-duotone.fa-glass-whiskey::after {
  content: "\f7a0\f7a0"; }

.fasds.fa-moon-stars::after, .fa-sharp-duotone.fa-moon-stars::after {
  content: "\f755\f755"; }

.fasds.fa-building-circle-exclamation::after, .fa-sharp-duotone.fa-building-circle-exclamation::after {
  content: "\e4d3\e4d3"; }

.fasds.fa-clothes-hanger::after, .fa-sharp-duotone.fa-clothes-hanger::after {
  content: "\e136\e136"; }

.fasds.fa-mobile-notch::after, .fa-sharp-duotone.fa-mobile-notch::after {
  content: "\e1ee\e1ee"; }

.fasds.fa-mobile-iphone::after, .fa-sharp-duotone.fa-mobile-iphone::after {
  content: "\e1ee\e1ee"; }

.fasds.fa-magnifying-glass-chart::after, .fa-sharp-duotone.fa-magnifying-glass-chart::after {
  content: "\e522\e522"; }

.fasds.fa-arrow-up-right-from-square::after, .fa-sharp-duotone.fa-arrow-up-right-from-square::after {
  content: "\f08e\f08e"; }

.fasds.fa-external-link::after, .fa-sharp-duotone.fa-external-link::after {
  content: "\f08e\f08e"; }

.fasds.fa-cubes-stacked::after, .fa-sharp-duotone.fa-cubes-stacked::after {
  content: "\e4e6\e4e6"; }

.fasds.fa-images-user::after, .fa-sharp-duotone.fa-images-user::after {
  content: "\e1b9\e1b9"; }

.fasds.fa-won-sign::after, .fa-sharp-duotone.fa-won-sign::after {
  content: "\f159\f159"; }

.fasds.fa-krw::after, .fa-sharp-duotone.fa-krw::after {
  content: "\f159\f159"; }

.fasds.fa-won::after, .fa-sharp-duotone.fa-won::after {
  content: "\f159\f159"; }

.fasds.fa-image-polaroid-user::after, .fa-sharp-duotone.fa-image-polaroid-user::after {
  content: "\e1b6\e1b6"; }

.fasds.fa-virus-covid::after, .fa-sharp-duotone.fa-virus-covid::after {
  content: "\e4a8\e4a8"; }

.fasds.fa-square-ellipsis::after, .fa-sharp-duotone.fa-square-ellipsis::after {
  content: "\e26e\e26e"; }

.fasds.fa-pie::after, .fa-sharp-duotone.fa-pie::after {
  content: "\f705\f705"; }

.fasds.fa-chess-knight-piece::after, .fa-sharp-duotone.fa-chess-knight-piece::after {
  content: "\f442\f442"; }

.fasds.fa-chess-knight-alt::after, .fa-sharp-duotone.fa-chess-knight-alt::after {
  content: "\f442\f442"; }

.fasds.fa-austral-sign::after, .fa-sharp-duotone.fa-austral-sign::after {
  content: "\e0a9\e0a9"; }

.fasds.fa-cloud-plus::after, .fa-sharp-duotone.fa-cloud-plus::after {
  content: "\e35e\e35e"; }

.fasds.fa-f::after, .fa-sharp-duotone.fa-f::after {
  content: "\46\46"; }

.fasds.fa-leaf::after, .fa-sharp-duotone.fa-leaf::after {
  content: "\f06c\f06c"; }

.fasds.fa-bed-bunk::after, .fa-sharp-duotone.fa-bed-bunk::after {
  content: "\f8f8\f8f8"; }

.fasds.fa-road::after, .fa-sharp-duotone.fa-road::after {
  content: "\f018\f018"; }

.fasds.fa-taxi::after, .fa-sharp-duotone.fa-taxi::after {
  content: "\f1ba\f1ba"; }

.fasds.fa-cab::after, .fa-sharp-duotone.fa-cab::after {
  content: "\f1ba\f1ba"; }

.fasds.fa-person-circle-plus::after, .fa-sharp-duotone.fa-person-circle-plus::after {
  content: "\e541\e541"; }

.fasds.fa-chart-pie::after, .fa-sharp-duotone.fa-chart-pie::after {
  content: "\f200\f200"; }

.fasds.fa-pie-chart::after, .fa-sharp-duotone.fa-pie-chart::after {
  content: "\f200\f200"; }

.fasds.fa-bolt-lightning::after, .fa-sharp-duotone.fa-bolt-lightning::after {
  content: "\e0b7\e0b7"; }

.fasds.fa-clock-eight::after, .fa-sharp-duotone.fa-clock-eight::after {
  content: "\e345\e345"; }

.fasds.fa-sack-xmark::after, .fa-sharp-duotone.fa-sack-xmark::after {
  content: "\e56a\e56a"; }

.fasds.fa-file-xls::after, .fa-sharp-duotone.fa-file-xls::after {
  content: "\e64d\e64d"; }

.fasds.fa-file-excel::after, .fa-sharp-duotone.fa-file-excel::after {
  content: "\f1c3\f1c3"; }

.fasds.fa-file-contract::after, .fa-sharp-duotone.fa-file-contract::after {
  content: "\f56c\f56c"; }

.fasds.fa-fish-fins::after, .fa-sharp-duotone.fa-fish-fins::after {
  content: "\e4f2\e4f2"; }

.fasds.fa-circle-q::after, .fa-sharp-duotone.fa-circle-q::after {
  content: "\e11e\e11e"; }

.fasds.fa-building-flag::after, .fa-sharp-duotone.fa-building-flag::after {
  content: "\e4d5\e4d5"; }

.fasds.fa-face-grin-beam::after, .fa-sharp-duotone.fa-face-grin-beam::after {
  content: "\f582\f582"; }

.fasds.fa-grin-beam::after, .fa-sharp-duotone.fa-grin-beam::after {
  content: "\f582\f582"; }

.fasds.fa-object-ungroup::after, .fa-sharp-duotone.fa-object-ungroup::after {
  content: "\f248\f248"; }

.fasds.fa-face-disguise::after, .fa-sharp-duotone.fa-face-disguise::after {
  content: "\e370\e370"; }

.fasds.fa-circle-arrow-down-right::after, .fa-sharp-duotone.fa-circle-arrow-down-right::after {
  content: "\e0fa\e0fa"; }

.fasds.fa-alien-8bit::after, .fa-sharp-duotone.fa-alien-8bit::after {
  content: "\f8f6\f8f6"; }

.fasds.fa-alien-monster::after, .fa-sharp-duotone.fa-alien-monster::after {
  content: "\f8f6\f8f6"; }

.fasds.fa-hand-point-ribbon::after, .fa-sharp-duotone.fa-hand-point-ribbon::after {
  content: "\e1a6\e1a6"; }

.fasds.fa-poop::after, .fa-sharp-duotone.fa-poop::after {
  content: "\f619\f619"; }

.fasds.fa-object-exclude::after, .fa-sharp-duotone.fa-object-exclude::after {
  content: "\e49c\e49c"; }

.fasds.fa-telescope::after, .fa-sharp-duotone.fa-telescope::after {
  content: "\e03e\e03e"; }

.fasds.fa-location-pin::after, .fa-sharp-duotone.fa-location-pin::after {
  content: "\f041\f041"; }

.fasds.fa-map-marker::after, .fa-sharp-duotone.fa-map-marker::after {
  content: "\f041\f041"; }

.fasds.fa-square-list::after, .fa-sharp-duotone.fa-square-list::after {
  content: "\e489\e489"; }

.fasds.fa-kaaba::after, .fa-sharp-duotone.fa-kaaba::after {
  content: "\f66b\f66b"; }

.fasds.fa-toilet-paper::after, .fa-sharp-duotone.fa-toilet-paper::after {
  content: "\f71e\f71e"; }

.fasds.fa-helmet-safety::after, .fa-sharp-duotone.fa-helmet-safety::after {
  content: "\f807\f807"; }

.fasds.fa-hard-hat::after, .fa-sharp-duotone.fa-hard-hat::after {
  content: "\f807\f807"; }

.fasds.fa-hat-hard::after, .fa-sharp-duotone.fa-hat-hard::after {
  content: "\f807\f807"; }

.fasds.fa-comment-code::after, .fa-sharp-duotone.fa-comment-code::after {
  content: "\e147\e147"; }

.fasds.fa-sim-cards::after, .fa-sharp-duotone.fa-sim-cards::after {
  content: "\e251\e251"; }

.fasds.fa-starship::after, .fa-sharp-duotone.fa-starship::after {
  content: "\e039\e039"; }

.fasds.fa-eject::after, .fa-sharp-duotone.fa-eject::after {
  content: "\f052\f052"; }

.fasds.fa-circle-right::after, .fa-sharp-duotone.fa-circle-right::after {
  content: "\f35a\f35a"; }

.fasds.fa-arrow-alt-circle-right::after, .fa-sharp-duotone.fa-arrow-alt-circle-right::after {
  content: "\f35a\f35a"; }

.fasds.fa-plane-circle-check::after, .fa-sharp-duotone.fa-plane-circle-check::after {
  content: "\e555\e555"; }

.fasds.fa-seal::after, .fa-sharp-duotone.fa-seal::after {
  content: "\e241\e241"; }

.fasds.fa-user-cowboy::after, .fa-sharp-duotone.fa-user-cowboy::after {
  content: "\f8ea\f8ea"; }

.fasds.fa-hexagon-vertical-nft::after, .fa-sharp-duotone.fa-hexagon-vertical-nft::after {
  content: "\e505\e505"; }

.fasds.fa-face-rolling-eyes::after, .fa-sharp-duotone.fa-face-rolling-eyes::after {
  content: "\f5a5\f5a5"; }

.fasds.fa-meh-rolling-eyes::after, .fa-sharp-duotone.fa-meh-rolling-eyes::after {
  content: "\f5a5\f5a5"; }

.fasds.fa-bread-loaf::after, .fa-sharp-duotone.fa-bread-loaf::after {
  content: "\f7eb\f7eb"; }

.fasds.fa-rings-wedding::after, .fa-sharp-duotone.fa-rings-wedding::after {
  content: "\f81b\f81b"; }

.fasds.fa-object-group::after, .fa-sharp-duotone.fa-object-group::after {
  content: "\f247\f247"; }

.fasds.fa-french-fries::after, .fa-sharp-duotone.fa-french-fries::after {
  content: "\f803\f803"; }

.fasds.fa-chart-line::after, .fa-sharp-duotone.fa-chart-line::after {
  content: "\f201\f201"; }

.fasds.fa-line-chart::after, .fa-sharp-duotone.fa-line-chart::after {
  content: "\f201\f201"; }

.fasds.fa-calendar-arrow-down::after, .fa-sharp-duotone.fa-calendar-arrow-down::after {
  content: "\e0d0\e0d0"; }

.fasds.fa-calendar-download::after, .fa-sharp-duotone.fa-calendar-download::after {
  content: "\e0d0\e0d0"; }

.fasds.fa-send-back::after, .fa-sharp-duotone.fa-send-back::after {
  content: "\f87e\f87e"; }

.fasds.fa-mask-ventilator::after, .fa-sharp-duotone.fa-mask-ventilator::after {
  content: "\e524\e524"; }

.fasds.fa-tickets::after, .fa-sharp-duotone.fa-tickets::after {
  content: "\e658\e658"; }

.fasds.fa-signature-lock::after, .fa-sharp-duotone.fa-signature-lock::after {
  content: "\e3ca\e3ca"; }

.fasds.fa-arrow-right::after, .fa-sharp-duotone.fa-arrow-right::after {
  content: "\f061\f061"; }

.fasds.fa-signs-post::after, .fa-sharp-duotone.fa-signs-post::after {
  content: "\f277\f277"; }

.fasds.fa-map-signs::after, .fa-sharp-duotone.fa-map-signs::after {
  content: "\f277\f277"; }

.fasds.fa-octagon-plus::after, .fa-sharp-duotone.fa-octagon-plus::after {
  content: "\f301\f301"; }

.fasds.fa-plus-octagon::after, .fa-sharp-duotone.fa-plus-octagon::after {
  content: "\f301\f301"; }

.fasds.fa-cash-register::after, .fa-sharp-duotone.fa-cash-register::after {
  content: "\f788\f788"; }

.fasds.fa-person-circle-question::after, .fa-sharp-duotone.fa-person-circle-question::after {
  content: "\e542\e542"; }

.fasds.fa-melon-slice::after, .fa-sharp-duotone.fa-melon-slice::after {
  content: "\e311\e311"; }

.fasds.fa-space-station-moon::after, .fa-sharp-duotone.fa-space-station-moon::after {
  content: "\e033\e033"; }

.fasds.fa-message-smile::after, .fa-sharp-duotone.fa-message-smile::after {
  content: "\f4aa\f4aa"; }

.fasds.fa-comment-alt-smile::after, .fa-sharp-duotone.fa-comment-alt-smile::after {
  content: "\f4aa\f4aa"; }

.fasds.fa-cup-straw::after, .fa-sharp-duotone.fa-cup-straw::after {
  content: "\e363\e363"; }

.fasds.fa-left-from-line::after, .fa-sharp-duotone.fa-left-from-line::after {
  content: "\f348\f348"; }

.fasds.fa-arrow-alt-from-right::after, .fa-sharp-duotone.fa-arrow-alt-from-right::after {
  content: "\f348\f348"; }

.fasds.fa-h::after, .fa-sharp-duotone.fa-h::after {
  content: "\48\48"; }

.fasds.fa-basket-shopping-simple::after, .fa-sharp-duotone.fa-basket-shopping-simple::after {
  content: "\e0af\e0af"; }

.fasds.fa-shopping-basket-alt::after, .fa-sharp-duotone.fa-shopping-basket-alt::after {
  content: "\e0af\e0af"; }

.fasds.fa-hands-holding-heart::after, .fa-sharp-duotone.fa-hands-holding-heart::after {
  content: "\f4c3\f4c3"; }

.fasds.fa-hands-heart::after, .fa-sharp-duotone.fa-hands-heart::after {
  content: "\f4c3\f4c3"; }

.fasds.fa-clock-nine::after, .fa-sharp-duotone.fa-clock-nine::after {
  content: "\e34c\e34c"; }

.fasds.fa-hammer-brush::after, .fa-sharp-duotone.fa-hammer-brush::after {
  content: "\e620\e620"; }

.fasds.fa-tarp::after, .fa-sharp-duotone.fa-tarp::after {
  content: "\e57b\e57b"; }

.fasds.fa-face-sleepy::after, .fa-sharp-duotone.fa-face-sleepy::after {
  content: "\e38e\e38e"; }

.fasds.fa-hand-horns::after, .fa-sharp-duotone.fa-hand-horns::after {
  content: "\e1a9\e1a9"; }

.fasds.fa-screwdriver-wrench::after, .fa-sharp-duotone.fa-screwdriver-wrench::after {
  content: "\f7d9\f7d9"; }

.fasds.fa-tools::after, .fa-sharp-duotone.fa-tools::after {
  content: "\f7d9\f7d9"; }

.fasds.fa-arrows-to-eye::after, .fa-sharp-duotone.fa-arrows-to-eye::after {
  content: "\e4bf\e4bf"; }

.fasds.fa-circle-three-quarters::after, .fa-sharp-duotone.fa-circle-three-quarters::after {
  content: "\e125\e125"; }

.fasds.fa-trophy-star::after, .fa-sharp-duotone.fa-trophy-star::after {
  content: "\f2eb\f2eb"; }

.fasds.fa-trophy-alt::after, .fa-sharp-duotone.fa-trophy-alt::after {
  content: "\f2eb\f2eb"; }

.fasds.fa-plug-circle-bolt::after, .fa-sharp-duotone.fa-plug-circle-bolt::after {
  content: "\e55b\e55b"; }

.fasds.fa-face-thermometer::after, .fa-sharp-duotone.fa-face-thermometer::after {
  content: "\e39a\e39a"; }

.fasds.fa-grid-round-4::after, .fa-sharp-duotone.fa-grid-round-4::after {
  content: "\e5dd\e5dd"; }

.fasds.fa-sign-posts-wrench::after, .fa-sharp-duotone.fa-sign-posts-wrench::after {
  content: "\e626\e626"; }

.fasds.fa-shirt-running::after, .fa-sharp-duotone.fa-shirt-running::after {
  content: "\e3c8\e3c8"; }

.fasds.fa-book-circle-arrow-up::after, .fa-sharp-duotone.fa-book-circle-arrow-up::after {
  content: "\e0bd\e0bd"; }

.fasds.fa-face-nauseated::after, .fa-sharp-duotone.fa-face-nauseated::after {
  content: "\e381\e381"; }

.fasds.fa-heart::after, .fa-sharp-duotone.fa-heart::after {
  content: "\f004\f004"; }

.fasds.fa-file-chart-pie::after, .fa-sharp-duotone.fa-file-chart-pie::after {
  content: "\f65a\f65a"; }

.fasds.fa-mars-and-venus::after, .fa-sharp-duotone.fa-mars-and-venus::after {
  content: "\f224\f224"; }

.fasds.fa-house-user::after, .fa-sharp-duotone.fa-house-user::after {
  content: "\e1b0\e1b0"; }

.fasds.fa-home-user::after, .fa-sharp-duotone.fa-home-user::after {
  content: "\e1b0\e1b0"; }

.fasds.fa-circle-arrow-down-left::after, .fa-sharp-duotone.fa-circle-arrow-down-left::after {
  content: "\e0f9\e0f9"; }

.fasds.fa-dumpster-fire::after, .fa-sharp-duotone.fa-dumpster-fire::after {
  content: "\f794\f794"; }

.fasds.fa-hexagon-minus::after, .fa-sharp-duotone.fa-hexagon-minus::after {
  content: "\f307\f307"; }

.fasds.fa-minus-hexagon::after, .fa-sharp-duotone.fa-minus-hexagon::after {
  content: "\f307\f307"; }

.fasds.fa-left-to-line::after, .fa-sharp-duotone.fa-left-to-line::after {
  content: "\f34b\f34b"; }

.fasds.fa-arrow-alt-to-left::after, .fa-sharp-duotone.fa-arrow-alt-to-left::after {
  content: "\f34b\f34b"; }

.fasds.fa-house-crack::after, .fa-sharp-duotone.fa-house-crack::after {
  content: "\e3b1\e3b1"; }

.fasds.fa-paw-simple::after, .fa-sharp-duotone.fa-paw-simple::after {
  content: "\f701\f701"; }

.fasds.fa-paw-alt::after, .fa-sharp-duotone.fa-paw-alt::after {
  content: "\f701\f701"; }

.fasds.fa-arrow-left-long-to-line::after, .fa-sharp-duotone.fa-arrow-left-long-to-line::after {
  content: "\e3d4\e3d4"; }

.fasds.fa-brackets-round::after, .fa-sharp-duotone.fa-brackets-round::after {
  content: "\e0c5\e0c5"; }

.fasds.fa-parentheses::after, .fa-sharp-duotone.fa-parentheses::after {
  content: "\e0c5\e0c5"; }

.fasds.fa-martini-glass-citrus::after, .fa-sharp-duotone.fa-martini-glass-citrus::after {
  content: "\f561\f561"; }

.fasds.fa-cocktail::after, .fa-sharp-duotone.fa-cocktail::after {
  content: "\f561\f561"; }

.fasds.fa-user-shakespeare::after, .fa-sharp-duotone.fa-user-shakespeare::after {
  content: "\e2c2\e2c2"; }

.fasds.fa-arrow-right-to-arc::after, .fa-sharp-duotone.fa-arrow-right-to-arc::after {
  content: "\e4b2\e4b2"; }

.fasds.fa-face-surprise::after, .fa-sharp-duotone.fa-face-surprise::after {
  content: "\f5c2\f5c2"; }

.fasds.fa-surprise::after, .fa-sharp-duotone.fa-surprise::after {
  content: "\f5c2\f5c2"; }

.fasds.fa-bottle-water::after, .fa-sharp-duotone.fa-bottle-water::after {
  content: "\e4c5\e4c5"; }

.fasds.fa-circle-pause::after, .fa-sharp-duotone.fa-circle-pause::after {
  content: "\f28b\f28b"; }

.fasds.fa-pause-circle::after, .fa-sharp-duotone.fa-pause-circle::after {
  content: "\f28b\f28b"; }

.fasds.fa-gauge-circle-plus::after, .fa-sharp-duotone.fa-gauge-circle-plus::after {
  content: "\e498\e498"; }

.fasds.fa-folders::after, .fa-sharp-duotone.fa-folders::after {
  content: "\f660\f660"; }

.fasds.fa-angel::after, .fa-sharp-duotone.fa-angel::after {
  content: "\f779\f779"; }

.fasds.fa-value-absolute::after, .fa-sharp-duotone.fa-value-absolute::after {
  content: "\f6a6\f6a6"; }

.fasds.fa-rabbit::after, .fa-sharp-duotone.fa-rabbit::after {
  content: "\f708\f708"; }

.fasds.fa-toilet-paper-slash::after, .fa-sharp-duotone.fa-toilet-paper-slash::after {
  content: "\e072\e072"; }

.fasds.fa-circle-euro::after, .fa-sharp-duotone.fa-circle-euro::after {
  content: "\e5ce\e5ce"; }

.fasds.fa-apple-whole::after, .fa-sharp-duotone.fa-apple-whole::after {
  content: "\f5d1\f5d1"; }

.fasds.fa-apple-alt::after, .fa-sharp-duotone.fa-apple-alt::after {
  content: "\f5d1\f5d1"; }

.fasds.fa-kitchen-set::after, .fa-sharp-duotone.fa-kitchen-set::after {
  content: "\e51a\e51a"; }

.fasds.fa-diamond-half::after, .fa-sharp-duotone.fa-diamond-half::after {
  content: "\e5b7\e5b7"; }

.fasds.fa-lock-keyhole::after, .fa-sharp-duotone.fa-lock-keyhole::after {
  content: "\f30d\f30d"; }

.fasds.fa-lock-alt::after, .fa-sharp-duotone.fa-lock-alt::after {
  content: "\f30d\f30d"; }

.fasds.fa-r::after, .fa-sharp-duotone.fa-r::after {
  content: "\52\52"; }

.fasds.fa-temperature-quarter::after, .fa-sharp-duotone.fa-temperature-quarter::after {
  content: "\f2ca\f2ca"; }

.fasds.fa-temperature-1::after, .fa-sharp-duotone.fa-temperature-1::after {
  content: "\f2ca\f2ca"; }

.fasds.fa-thermometer-1::after, .fa-sharp-duotone.fa-thermometer-1::after {
  content: "\f2ca\f2ca"; }

.fasds.fa-thermometer-quarter::after, .fa-sharp-duotone.fa-thermometer-quarter::after {
  content: "\f2ca\f2ca"; }

.fasds.fa-square-info::after, .fa-sharp-duotone.fa-square-info::after {
  content: "\f30f\f30f"; }

.fasds.fa-info-square::after, .fa-sharp-duotone.fa-info-square::after {
  content: "\f30f\f30f"; }

.fasds.fa-wifi-slash::after, .fa-sharp-duotone.fa-wifi-slash::after {
  content: "\f6ac\f6ac"; }

.fasds.fa-toilet-paper-xmark::after, .fa-sharp-duotone.fa-toilet-paper-xmark::after {
  content: "\e5b3\e5b3"; }

.fasds.fa-hands-holding-dollar::after, .fa-sharp-duotone.fa-hands-holding-dollar::after {
  content: "\f4c5\f4c5"; }

.fasds.fa-hands-usd::after, .fa-sharp-duotone.fa-hands-usd::after {
  content: "\f4c5\f4c5"; }

.fasds.fa-cube::after, .fa-sharp-duotone.fa-cube::after {
  content: "\f1b2\f1b2"; }

.fasds.fa-arrow-down-triangle-square::after, .fa-sharp-duotone.fa-arrow-down-triangle-square::after {
  content: "\f888\f888"; }

.fasds.fa-sort-shapes-down::after, .fa-sharp-duotone.fa-sort-shapes-down::after {
  content: "\f888\f888"; }

.fasds.fa-bitcoin-sign::after, .fa-sharp-duotone.fa-bitcoin-sign::after {
  content: "\e0b4\e0b4"; }

.fasds.fa-shutters::after, .fa-sharp-duotone.fa-shutters::after {
  content: "\e449\e449"; }

.fasds.fa-shield-dog::after, .fa-sharp-duotone.fa-shield-dog::after {
  content: "\e573\e573"; }

.fasds.fa-solar-panel::after, .fa-sharp-duotone.fa-solar-panel::after {
  content: "\f5ba\f5ba"; }

.fasds.fa-lock-open::after, .fa-sharp-duotone.fa-lock-open::after {
  content: "\f3c1\f3c1"; }

.fasds.fa-table-tree::after, .fa-sharp-duotone.fa-table-tree::after {
  content: "\e293\e293"; }

.fasds.fa-house-chimney-heart::after, .fa-sharp-duotone.fa-house-chimney-heart::after {
  content: "\e1b2\e1b2"; }

.fasds.fa-tally-3::after, .fa-sharp-duotone.fa-tally-3::after {
  content: "\e296\e296"; }

.fasds.fa-elevator::after, .fa-sharp-duotone.fa-elevator::after {
  content: "\e16d\e16d"; }

.fasds.fa-money-bill-transfer::after, .fa-sharp-duotone.fa-money-bill-transfer::after {
  content: "\e528\e528"; }

.fasds.fa-money-bill-trend-up::after, .fa-sharp-duotone.fa-money-bill-trend-up::after {
  content: "\e529\e529"; }

.fasds.fa-house-flood-water-circle-arrow-right::after, .fa-sharp-duotone.fa-house-flood-water-circle-arrow-right::after {
  content: "\e50f\e50f"; }

.fasds.fa-square-poll-horizontal::after, .fa-sharp-duotone.fa-square-poll-horizontal::after {
  content: "\f682\f682"; }

.fasds.fa-poll-h::after, .fa-sharp-duotone.fa-poll-h::after {
  content: "\f682\f682"; }

.fasds.fa-circle::after, .fa-sharp-duotone.fa-circle::after {
  content: "\f111\f111"; }

.fasds.fa-left-to-bracket::after, .fa-sharp-duotone.fa-left-to-bracket::after {
  content: "\e66d\e66d"; }

.fasds.fa-cart-circle-exclamation::after, .fa-sharp-duotone.fa-cart-circle-exclamation::after {
  content: "\e3f2\e3f2"; }

.fasds.fa-sword::after, .fa-sharp-duotone.fa-sword::after {
  content: "\f71c\f71c"; }

.fasds.fa-backward-fast::after, .fa-sharp-duotone.fa-backward-fast::after {
  content: "\f049\f049"; }

.fasds.fa-fast-backward::after, .fa-sharp-duotone.fa-fast-backward::after {
  content: "\f049\f049"; }

.fasds.fa-recycle::after, .fa-sharp-duotone.fa-recycle::after {
  content: "\f1b8\f1b8"; }

.fasds.fa-user-astronaut::after, .fa-sharp-duotone.fa-user-astronaut::after {
  content: "\f4fb\f4fb"; }

.fasds.fa-interrobang::after, .fa-sharp-duotone.fa-interrobang::after {
  content: "\e5ba\e5ba"; }

.fasds.fa-plane-slash::after, .fa-sharp-duotone.fa-plane-slash::after {
  content: "\e069\e069"; }

.fasds.fa-circle-dashed::after, .fa-sharp-duotone.fa-circle-dashed::after {
  content: "\e105\e105"; }

.fasds.fa-trademark::after, .fa-sharp-duotone.fa-trademark::after {
  content: "\f25c\f25c"; }

.fasds.fa-basketball::after, .fa-sharp-duotone.fa-basketball::after {
  content: "\f434\f434"; }

.fasds.fa-basketball-ball::after, .fa-sharp-duotone.fa-basketball-ball::after {
  content: "\f434\f434"; }

.fasds.fa-fork-knife::after, .fa-sharp-duotone.fa-fork-knife::after {
  content: "\f2e6\f2e6"; }

.fasds.fa-utensils-alt::after, .fa-sharp-duotone.fa-utensils-alt::after {
  content: "\f2e6\f2e6"; }

.fasds.fa-satellite-dish::after, .fa-sharp-duotone.fa-satellite-dish::after {
  content: "\f7c0\f7c0"; }

.fasds.fa-badge-check::after, .fa-sharp-duotone.fa-badge-check::after {
  content: "\f336\f336"; }

.fasds.fa-circle-up::after, .fa-sharp-duotone.fa-circle-up::after {
  content: "\f35b\f35b"; }

.fasds.fa-arrow-alt-circle-up::after, .fa-sharp-duotone.fa-arrow-alt-circle-up::after {
  content: "\f35b\f35b"; }

.fasds.fa-slider::after, .fa-sharp-duotone.fa-slider::after {
  content: "\e252\e252"; }

.fasds.fa-mobile-screen-button::after, .fa-sharp-duotone.fa-mobile-screen-button::after {
  content: "\f3cd\f3cd"; }

.fasds.fa-mobile-alt::after, .fa-sharp-duotone.fa-mobile-alt::after {
  content: "\f3cd\f3cd"; }

.fasds.fa-clock-one-thirty::after, .fa-sharp-duotone.fa-clock-one-thirty::after {
  content: "\e34f\e34f"; }

.fasds.fa-inbox-out::after, .fa-sharp-duotone.fa-inbox-out::after {
  content: "\f311\f311"; }

.fasds.fa-inbox-arrow-up::after, .fa-sharp-duotone.fa-inbox-arrow-up::after {
  content: "\f311\f311"; }

.fasds.fa-cloud-slash::after, .fa-sharp-duotone.fa-cloud-slash::after {
  content: "\e137\e137"; }

.fasds.fa-volume-high::after, .fa-sharp-duotone.fa-volume-high::after {
  content: "\f028\f028"; }

.fasds.fa-volume-up::after, .fa-sharp-duotone.fa-volume-up::after {
  content: "\f028\f028"; }

.fasds.fa-users-rays::after, .fa-sharp-duotone.fa-users-rays::after {
  content: "\e593\e593"; }

.fasds.fa-wallet::after, .fa-sharp-duotone.fa-wallet::after {
  content: "\f555\f555"; }

.fasds.fa-octagon-check::after, .fa-sharp-duotone.fa-octagon-check::after {
  content: "\e426\e426"; }

.fasds.fa-flatbread-stuffed::after, .fa-sharp-duotone.fa-flatbread-stuffed::after {
  content: "\e40c\e40c"; }

.fasds.fa-clipboard-check::after, .fa-sharp-duotone.fa-clipboard-check::after {
  content: "\f46c\f46c"; }

.fasds.fa-cart-circle-plus::after, .fa-sharp-duotone.fa-cart-circle-plus::after {
  content: "\e3f3\e3f3"; }

.fasds.fa-truck-clock::after, .fa-sharp-duotone.fa-truck-clock::after {
  content: "\f48c\f48c"; }

.fasds.fa-shipping-timed::after, .fa-sharp-duotone.fa-shipping-timed::after {
  content: "\f48c\f48c"; }

.fasds.fa-pool-8-ball::after, .fa-sharp-duotone.fa-pool-8-ball::after {
  content: "\e3c5\e3c5"; }

.fasds.fa-file-audio::after, .fa-sharp-duotone.fa-file-audio::after {
  content: "\f1c7\f1c7"; }

.fasds.fa-turn-down-left::after, .fa-sharp-duotone.fa-turn-down-left::after {
  content: "\e331\e331"; }

.fasds.fa-lock-hashtag::after, .fa-sharp-duotone.fa-lock-hashtag::after {
  content: "\e423\e423"; }

.fasds.fa-chart-radar::after, .fa-sharp-duotone.fa-chart-radar::after {
  content: "\e0e7\e0e7"; }

.fasds.fa-staff::after, .fa-sharp-duotone.fa-staff::after {
  content: "\f71b\f71b"; }

.fasds.fa-burger::after, .fa-sharp-duotone.fa-burger::after {
  content: "\f805\f805"; }

.fasds.fa-hamburger::after, .fa-sharp-duotone.fa-hamburger::after {
  content: "\f805\f805"; }

.fasds.fa-utility-pole::after, .fa-sharp-duotone.fa-utility-pole::after {
  content: "\e2c3\e2c3"; }

.fasds.fa-transporter-6::after, .fa-sharp-duotone.fa-transporter-6::after {
  content: "\e2a7\e2a7"; }

.fasds.fa-arrow-turn-left::after, .fa-sharp-duotone.fa-arrow-turn-left::after {
  content: "\e632\e632"; }

.fasds.fa-wrench::after, .fa-sharp-duotone.fa-wrench::after {
  content: "\f0ad\f0ad"; }

.fasds.fa-bugs::after, .fa-sharp-duotone.fa-bugs::after {
  content: "\e4d0\e4d0"; }

.fasds.fa-vector-polygon::after, .fa-sharp-duotone.fa-vector-polygon::after {
  content: "\e2c7\e2c7"; }

.fasds.fa-diagram-nested::after, .fa-sharp-duotone.fa-diagram-nested::after {
  content: "\e157\e157"; }

.fasds.fa-rupee-sign::after, .fa-sharp-duotone.fa-rupee-sign::after {
  content: "\f156\f156"; }

.fasds.fa-rupee::after, .fa-sharp-duotone.fa-rupee::after {
  content: "\f156\f156"; }

.fasds.fa-file-image::after, .fa-sharp-duotone.fa-file-image::after {
  content: "\f1c5\f1c5"; }

.fasds.fa-circle-question::after, .fa-sharp-duotone.fa-circle-question::after {
  content: "\f059\f059"; }

.fasds.fa-question-circle::after, .fa-sharp-duotone.fa-question-circle::after {
  content: "\f059\f059"; }

.fasds.fa-tickets-perforated::after, .fa-sharp-duotone.fa-tickets-perforated::after {
  content: "\e63f\e63f"; }

.fasds.fa-image-user::after, .fa-sharp-duotone.fa-image-user::after {
  content: "\e1b8\e1b8"; }

.fasds.fa-buoy::after, .fa-sharp-duotone.fa-buoy::after {
  content: "\e5b5\e5b5"; }

.fasds.fa-plane-departure::after, .fa-sharp-duotone.fa-plane-departure::after {
  content: "\f5b0\f5b0"; }

.fasds.fa-handshake-slash::after, .fa-sharp-duotone.fa-handshake-slash::after {
  content: "\e060\e060"; }

.fasds.fa-book-bookmark::after, .fa-sharp-duotone.fa-book-bookmark::after {
  content: "\e0bb\e0bb"; }

.fasds.fa-border-center-h::after, .fa-sharp-duotone.fa-border-center-h::after {
  content: "\f89c\f89c"; }

.fasds.fa-can-food::after, .fa-sharp-duotone.fa-can-food::after {
  content: "\e3e6\e3e6"; }

.fasds.fa-typewriter::after, .fa-sharp-duotone.fa-typewriter::after {
  content: "\f8e7\f8e7"; }

.fasds.fa-arrow-right-from-arc::after, .fa-sharp-duotone.fa-arrow-right-from-arc::after {
  content: "\e4b1\e4b1"; }

.fasds.fa-circle-k::after, .fa-sharp-duotone.fa-circle-k::after {
  content: "\e113\e113"; }

.fasds.fa-face-hand-over-mouth::after, .fa-sharp-duotone.fa-face-hand-over-mouth::after {
  content: "\e378\e378"; }

.fasds.fa-popcorn::after, .fa-sharp-duotone.fa-popcorn::after {
  content: "\f819\f819"; }

.fasds.fa-house-water::after, .fa-sharp-duotone.fa-house-water::after {
  content: "\f74f\f74f"; }

.fasds.fa-house-flood::after, .fa-sharp-duotone.fa-house-flood::after {
  content: "\f74f\f74f"; }

.fasds.fa-object-subtract::after, .fa-sharp-duotone.fa-object-subtract::after {
  content: "\e49e\e49e"; }

.fasds.fa-code-branch::after, .fa-sharp-duotone.fa-code-branch::after {
  content: "\f126\f126"; }

.fasds.fa-warehouse-full::after, .fa-sharp-duotone.fa-warehouse-full::after {
  content: "\f495\f495"; }

.fasds.fa-warehouse-alt::after, .fa-sharp-duotone.fa-warehouse-alt::after {
  content: "\f495\f495"; }

.fasds.fa-hat-cowboy::after, .fa-sharp-duotone.fa-hat-cowboy::after {
  content: "\f8c0\f8c0"; }

.fasds.fa-bridge::after, .fa-sharp-duotone.fa-bridge::after {
  content: "\e4c8\e4c8"; }

.fasds.fa-phone-flip::after, .fa-sharp-duotone.fa-phone-flip::after {
  content: "\f879\f879"; }

.fasds.fa-phone-alt::after, .fa-sharp-duotone.fa-phone-alt::after {
  content: "\f879\f879"; }

.fasds.fa-arrow-down-from-dotted-line::after, .fa-sharp-duotone.fa-arrow-down-from-dotted-line::after {
  content: "\e090\e090"; }

.fasds.fa-file-doc::after, .fa-sharp-duotone.fa-file-doc::after {
  content: "\e5ed\e5ed"; }

.fasds.fa-square-quarters::after, .fa-sharp-duotone.fa-square-quarters::after {
  content: "\e44e\e44e"; }

.fasds.fa-truck-front::after, .fa-sharp-duotone.fa-truck-front::after {
  content: "\e2b7\e2b7"; }

.fasds.fa-cat::after, .fa-sharp-duotone.fa-cat::after {
  content: "\f6be\f6be"; }

.fasds.fa-trash-xmark::after, .fa-sharp-duotone.fa-trash-xmark::after {
  content: "\e2b4\e2b4"; }

.fasds.fa-circle-caret-left::after, .fa-sharp-duotone.fa-circle-caret-left::after {
  content: "\f32e\f32e"; }

.fasds.fa-caret-circle-left::after, .fa-sharp-duotone.fa-caret-circle-left::after {
  content: "\f32e\f32e"; }

.fasds.fa-files::after, .fa-sharp-duotone.fa-files::after {
  content: "\e178\e178"; }

.fasds.fa-anchor-circle-exclamation::after, .fa-sharp-duotone.fa-anchor-circle-exclamation::after {
  content: "\e4ab\e4ab"; }

.fasds.fa-face-clouds::after, .fa-sharp-duotone.fa-face-clouds::after {
  content: "\e47d\e47d"; }

.fasds.fa-user-crown::after, .fa-sharp-duotone.fa-user-crown::after {
  content: "\f6a4\f6a4"; }

.fasds.fa-basket-shopping-plus::after, .fa-sharp-duotone.fa-basket-shopping-plus::after {
  content: "\e653\e653"; }

.fasds.fa-truck-field::after, .fa-sharp-duotone.fa-truck-field::after {
  content: "\e58d\e58d"; }

.fasds.fa-route::after, .fa-sharp-duotone.fa-route::after {
  content: "\f4d7\f4d7"; }

.fasds.fa-cart-circle-check::after, .fa-sharp-duotone.fa-cart-circle-check::after {
  content: "\e3f1\e3f1"; }

.fasds.fa-clipboard-question::after, .fa-sharp-duotone.fa-clipboard-question::after {
  content: "\e4e3\e4e3"; }

.fasds.fa-panorama::after, .fa-sharp-duotone.fa-panorama::after {
  content: "\e209\e209"; }

.fasds.fa-comment-medical::after, .fa-sharp-duotone.fa-comment-medical::after {
  content: "\f7f5\f7f5"; }

.fasds.fa-teeth-open::after, .fa-sharp-duotone.fa-teeth-open::after {
  content: "\f62f\f62f"; }

.fasds.fa-user-tie-hair-long::after, .fa-sharp-duotone.fa-user-tie-hair-long::after {
  content: "\e460\e460"; }

.fasds.fa-file-circle-minus::after, .fa-sharp-duotone.fa-file-circle-minus::after {
  content: "\e4ed\e4ed"; }

.fasds.fa-head-side-medical::after, .fa-sharp-duotone.fa-head-side-medical::after {
  content: "\f809\f809"; }

.fasds.fa-arrow-turn-right::after, .fa-sharp-duotone.fa-arrow-turn-right::after {
  content: "\e635\e635"; }

.fasds.fa-tags::after, .fa-sharp-duotone.fa-tags::after {
  content: "\f02c\f02c"; }

.fasds.fa-wine-glass::after, .fa-sharp-duotone.fa-wine-glass::after {
  content: "\f4e3\f4e3"; }

.fasds.fa-forward-fast::after, .fa-sharp-duotone.fa-forward-fast::after {
  content: "\f050\f050"; }

.fasds.fa-fast-forward::after, .fa-sharp-duotone.fa-fast-forward::after {
  content: "\f050\f050"; }

.fasds.fa-face-meh-blank::after, .fa-sharp-duotone.fa-face-meh-blank::after {
  content: "\f5a4\f5a4"; }

.fasds.fa-meh-blank::after, .fa-sharp-duotone.fa-meh-blank::after {
  content: "\f5a4\f5a4"; }

.fasds.fa-user-robot::after, .fa-sharp-duotone.fa-user-robot::after {
  content: "\e04b\e04b"; }

.fasds.fa-square-parking::after, .fa-sharp-duotone.fa-square-parking::after {
  content: "\f540\f540"; }

.fasds.fa-parking::after, .fa-sharp-duotone.fa-parking::after {
  content: "\f540\f540"; }

.fasds.fa-card-diamond::after, .fa-sharp-duotone.fa-card-diamond::after {
  content: "\e3ea\e3ea"; }

.fasds.fa-face-zipper::after, .fa-sharp-duotone.fa-face-zipper::after {
  content: "\e3a5\e3a5"; }

.fasds.fa-face-raised-eyebrow::after, .fa-sharp-duotone.fa-face-raised-eyebrow::after {
  content: "\e388\e388"; }

.fasds.fa-house-signal::after, .fa-sharp-duotone.fa-house-signal::after {
  content: "\e012\e012"; }

.fasds.fa-square-chevron-up::after, .fa-sharp-duotone.fa-square-chevron-up::after {
  content: "\f32c\f32c"; }

.fasds.fa-chevron-square-up::after, .fa-sharp-duotone.fa-chevron-square-up::after {
  content: "\f32c\f32c"; }

.fasds.fa-bars-progress::after, .fa-sharp-duotone.fa-bars-progress::after {
  content: "\f828\f828"; }

.fasds.fa-tasks-alt::after, .fa-sharp-duotone.fa-tasks-alt::after {
  content: "\f828\f828"; }

.fasds.fa-faucet-drip::after, .fa-sharp-duotone.fa-faucet-drip::after {
  content: "\e006\e006"; }

.fasds.fa-arrows-to-line::after, .fa-sharp-duotone.fa-arrows-to-line::after {
  content: "\e0a7\e0a7"; }

.fasds.fa-dolphin::after, .fa-sharp-duotone.fa-dolphin::after {
  content: "\e168\e168"; }

.fasds.fa-arrow-up-right::after, .fa-sharp-duotone.fa-arrow-up-right::after {
  content: "\e09f\e09f"; }

.fasds.fa-circle-r::after, .fa-sharp-duotone.fa-circle-r::after {
  content: "\e120\e120"; }

.fasds.fa-cart-flatbed::after, .fa-sharp-duotone.fa-cart-flatbed::after {
  content: "\f474\f474"; }

.fasds.fa-dolly-flatbed::after, .fa-sharp-duotone.fa-dolly-flatbed::after {
  content: "\f474\f474"; }

.fasds.fa-ban-smoking::after, .fa-sharp-duotone.fa-ban-smoking::after {
  content: "\f54d\f54d"; }

.fasds.fa-smoking-ban::after, .fa-sharp-duotone.fa-smoking-ban::after {
  content: "\f54d\f54d"; }

.fasds.fa-circle-sort-up::after, .fa-sharp-duotone.fa-circle-sort-up::after {
  content: "\e032\e032"; }

.fasds.fa-sort-circle-up::after, .fa-sharp-duotone.fa-sort-circle-up::after {
  content: "\e032\e032"; }

.fasds.fa-terminal::after, .fa-sharp-duotone.fa-terminal::after {
  content: "\f120\f120"; }

.fasds.fa-mobile-button::after, .fa-sharp-duotone.fa-mobile-button::after {
  content: "\f10b\f10b"; }

.fasds.fa-house-medical-flag::after, .fa-sharp-duotone.fa-house-medical-flag::after {
  content: "\e514\e514"; }

.fasds.fa-basket-shopping::after, .fa-sharp-duotone.fa-basket-shopping::after {
  content: "\f291\f291"; }

.fasds.fa-shopping-basket::after, .fa-sharp-duotone.fa-shopping-basket::after {
  content: "\f291\f291"; }

.fasds.fa-tape::after, .fa-sharp-duotone.fa-tape::after {
  content: "\f4db\f4db"; }

.fasds.fa-chestnut::after, .fa-sharp-duotone.fa-chestnut::after {
  content: "\e3f6\e3f6"; }

.fasds.fa-bus-simple::after, .fa-sharp-duotone.fa-bus-simple::after {
  content: "\f55e\f55e"; }

.fasds.fa-bus-alt::after, .fa-sharp-duotone.fa-bus-alt::after {
  content: "\f55e\f55e"; }

.fasds.fa-eye::after, .fa-sharp-duotone.fa-eye::after {
  content: "\f06e\f06e"; }

.fasds.fa-face-sad-cry::after, .fa-sharp-duotone.fa-face-sad-cry::after {
  content: "\f5b3\f5b3"; }

.fasds.fa-sad-cry::after, .fa-sharp-duotone.fa-sad-cry::after {
  content: "\f5b3\f5b3"; }

.fasds.fa-heat::after, .fa-sharp-duotone.fa-heat::after {
  content: "\e00c\e00c"; }

.fasds.fa-ticket-airline::after, .fa-sharp-duotone.fa-ticket-airline::after {
  content: "\e29a\e29a"; }

.fasds.fa-ticket-perforated-plane::after, .fa-sharp-duotone.fa-ticket-perforated-plane::after {
  content: "\e29a\e29a"; }

.fasds.fa-ticket-plane::after, .fa-sharp-duotone.fa-ticket-plane::after {
  content: "\e29a\e29a"; }

.fasds.fa-boot-heeled::after, .fa-sharp-duotone.fa-boot-heeled::after {
  content: "\e33f\e33f"; }

.fasds.fa-arrows-minimize::after, .fa-sharp-duotone.fa-arrows-minimize::after {
  content: "\e0a5\e0a5"; }

.fasds.fa-compress-arrows::after, .fa-sharp-duotone.fa-compress-arrows::after {
  content: "\e0a5\e0a5"; }

.fasds.fa-audio-description::after, .fa-sharp-duotone.fa-audio-description::after {
  content: "\f29e\f29e"; }

.fasds.fa-person-military-to-person::after, .fa-sharp-duotone.fa-person-military-to-person::after {
  content: "\e54c\e54c"; }

.fasds.fa-file-shield::after, .fa-sharp-duotone.fa-file-shield::after {
  content: "\e4f0\e4f0"; }

.fasds.fa-hexagon::after, .fa-sharp-duotone.fa-hexagon::after {
  content: "\f312\f312"; }

.fasds.fa-manhole::after, .fa-sharp-duotone.fa-manhole::after {
  content: "\e1d6\e1d6"; }

.fasds.fa-user-slash::after, .fa-sharp-duotone.fa-user-slash::after {
  content: "\f506\f506"; }

.fasds.fa-pen::after, .fa-sharp-duotone.fa-pen::after {
  content: "\f304\f304"; }

.fasds.fa-tower-observation::after, .fa-sharp-duotone.fa-tower-observation::after {
  content: "\e586\e586"; }

.fasds.fa-floppy-disks::after, .fa-sharp-duotone.fa-floppy-disks::after {
  content: "\e183\e183"; }

.fasds.fa-toilet-paper-blank-under::after, .fa-sharp-duotone.fa-toilet-paper-blank-under::after {
  content: "\e29f\e29f"; }

.fasds.fa-toilet-paper-reverse-alt::after, .fa-sharp-duotone.fa-toilet-paper-reverse-alt::after {
  content: "\e29f\e29f"; }

.fasds.fa-file-code::after, .fa-sharp-duotone.fa-file-code::after {
  content: "\f1c9\f1c9"; }

.fasds.fa-signal::after, .fa-sharp-duotone.fa-signal::after {
  content: "\f012\f012"; }

.fasds.fa-signal-5::after, .fa-sharp-duotone.fa-signal-5::after {
  content: "\f012\f012"; }

.fasds.fa-signal-perfect::after, .fa-sharp-duotone.fa-signal-perfect::after {
  content: "\f012\f012"; }

.fasds.fa-pump::after, .fa-sharp-duotone.fa-pump::after {
  content: "\e442\e442"; }

.fasds.fa-bus::after, .fa-sharp-duotone.fa-bus::after {
  content: "\f207\f207"; }

.fasds.fa-heart-circle-xmark::after, .fa-sharp-duotone.fa-heart-circle-xmark::after {
  content: "\e501\e501"; }

.fasds.fa-arrow-up-left-from-circle::after, .fa-sharp-duotone.fa-arrow-up-left-from-circle::after {
  content: "\e09e\e09e"; }

.fasds.fa-house-chimney::after, .fa-sharp-duotone.fa-house-chimney::after {
  content: "\e3af\e3af"; }

.fasds.fa-home-lg::after, .fa-sharp-duotone.fa-home-lg::after {
  content: "\e3af\e3af"; }

.fasds.fa-window-maximize::after, .fa-sharp-duotone.fa-window-maximize::after {
  content: "\f2d0\f2d0"; }

.fasds.fa-dryer::after, .fa-sharp-duotone.fa-dryer::after {
  content: "\f861\f861"; }

.fasds.fa-face-frown::after, .fa-sharp-duotone.fa-face-frown::after {
  content: "\f119\f119"; }

.fasds.fa-frown::after, .fa-sharp-duotone.fa-frown::after {
  content: "\f119\f119"; }

.fasds.fa-chess-bishop-piece::after, .fa-sharp-duotone.fa-chess-bishop-piece::after {
  content: "\f43b\f43b"; }

.fasds.fa-chess-bishop-alt::after, .fa-sharp-duotone.fa-chess-bishop-alt::after {
  content: "\f43b\f43b"; }

.fasds.fa-shirt-tank-top::after, .fa-sharp-duotone.fa-shirt-tank-top::after {
  content: "\e3c9\e3c9"; }

.fasds.fa-diploma::after, .fa-sharp-duotone.fa-diploma::after {
  content: "\f5ea\f5ea"; }

.fasds.fa-scroll-ribbon::after, .fa-sharp-duotone.fa-scroll-ribbon::after {
  content: "\f5ea\f5ea"; }

.fasds.fa-screencast::after, .fa-sharp-duotone.fa-screencast::after {
  content: "\e23e\e23e"; }

.fasds.fa-walker::after, .fa-sharp-duotone.fa-walker::after {
  content: "\f831\f831"; }

.fasds.fa-prescription::after, .fa-sharp-duotone.fa-prescription::after {
  content: "\f5b1\f5b1"; }

.fasds.fa-shop::after, .fa-sharp-duotone.fa-shop::after {
  content: "\f54f\f54f"; }

.fasds.fa-store-alt::after, .fa-sharp-duotone.fa-store-alt::after {
  content: "\f54f\f54f"; }

.fasds.fa-floppy-disk::after, .fa-sharp-duotone.fa-floppy-disk::after {
  content: "\f0c7\f0c7"; }

.fasds.fa-save::after, .fa-sharp-duotone.fa-save::after {
  content: "\f0c7\f0c7"; }

.fasds.fa-vihara::after, .fa-sharp-duotone.fa-vihara::after {
  content: "\f6a7\f6a7"; }

.fasds.fa-face-kiss-closed-eyes::after, .fa-sharp-duotone.fa-face-kiss-closed-eyes::after {
  content: "\e37d\e37d"; }

.fasds.fa-scale-unbalanced::after, .fa-sharp-duotone.fa-scale-unbalanced::after {
  content: "\f515\f515"; }

.fasds.fa-balance-scale-left::after, .fa-sharp-duotone.fa-balance-scale-left::after {
  content: "\f515\f515"; }

.fasds.fa-file-user::after, .fa-sharp-duotone.fa-file-user::after {
  content: "\f65c\f65c"; }

.fasds.fa-user-police-tie::after, .fa-sharp-duotone.fa-user-police-tie::after {
  content: "\e334\e334"; }

.fasds.fa-face-tongue-money::after, .fa-sharp-duotone.fa-face-tongue-money::after {
  content: "\e39d\e39d"; }

.fasds.fa-tennis-ball::after, .fa-sharp-duotone.fa-tennis-ball::after {
  content: "\f45e\f45e"; }

.fasds.fa-square-l::after, .fa-sharp-duotone.fa-square-l::after {
  content: "\e275\e275"; }

.fasds.fa-sort-up::after, .fa-sharp-duotone.fa-sort-up::after {
  content: "\f0de\f0de"; }

.fasds.fa-sort-asc::after, .fa-sharp-duotone.fa-sort-asc::after {
  content: "\f0de\f0de"; }

.fasds.fa-calendar-arrow-up::after, .fa-sharp-duotone.fa-calendar-arrow-up::after {
  content: "\e0d1\e0d1"; }

.fasds.fa-calendar-upload::after, .fa-sharp-duotone.fa-calendar-upload::after {
  content: "\e0d1\e0d1"; }

.fasds.fa-comment-dots::after, .fa-sharp-duotone.fa-comment-dots::after {
  content: "\f4ad\f4ad"; }

.fasds.fa-commenting::after, .fa-sharp-duotone.fa-commenting::after {
  content: "\f4ad\f4ad"; }

.fasds.fa-plant-wilt::after, .fa-sharp-duotone.fa-plant-wilt::after {
  content: "\e5aa\e5aa"; }

.fasds.fa-scarf::after, .fa-sharp-duotone.fa-scarf::after {
  content: "\f7c1\f7c1"; }

.fasds.fa-album-circle-plus::after, .fa-sharp-duotone.fa-album-circle-plus::after {
  content: "\e48c\e48c"; }

.fasds.fa-user-nurse-hair-long::after, .fa-sharp-duotone.fa-user-nurse-hair-long::after {
  content: "\e45e\e45e"; }

.fasds.fa-diamond::after, .fa-sharp-duotone.fa-diamond::after {
  content: "\f219\f219"; }

.fasds.fa-square-left::after, .fa-sharp-duotone.fa-square-left::after {
  content: "\f351\f351"; }

.fasds.fa-arrow-alt-square-left::after, .fa-sharp-duotone.fa-arrow-alt-square-left::after {
  content: "\f351\f351"; }

.fasds.fa-face-grin-squint::after, .fa-sharp-duotone.fa-face-grin-squint::after {
  content: "\f585\f585"; }

.fasds.fa-grin-squint::after, .fa-sharp-duotone.fa-grin-squint::after {
  content: "\f585\f585"; }

.fasds.fa-circle-ellipsis-vertical::after, .fa-sharp-duotone.fa-circle-ellipsis-vertical::after {
  content: "\e10b\e10b"; }

.fasds.fa-hand-holding-dollar::after, .fa-sharp-duotone.fa-hand-holding-dollar::after {
  content: "\f4c0\f4c0"; }

.fasds.fa-hand-holding-usd::after, .fa-sharp-duotone.fa-hand-holding-usd::after {
  content: "\f4c0\f4c0"; }

.fasds.fa-grid-dividers::after, .fa-sharp-duotone.fa-grid-dividers::after {
  content: "\e3ad\e3ad"; }

.fasds.fa-bacterium::after, .fa-sharp-duotone.fa-bacterium::after {
  content: "\e05a\e05a"; }

.fasds.fa-hand-pointer::after, .fa-sharp-duotone.fa-hand-pointer::after {
  content: "\f25a\f25a"; }

.fasds.fa-drum-steelpan::after, .fa-sharp-duotone.fa-drum-steelpan::after {
  content: "\f56a\f56a"; }

.fasds.fa-hand-scissors::after, .fa-sharp-duotone.fa-hand-scissors::after {
  content: "\f257\f257"; }

.fasds.fa-hands-praying::after, .fa-sharp-duotone.fa-hands-praying::after {
  content: "\f684\f684"; }

.fasds.fa-praying-hands::after, .fa-sharp-duotone.fa-praying-hands::after {
  content: "\f684\f684"; }

.fasds.fa-face-pensive::after, .fa-sharp-duotone.fa-face-pensive::after {
  content: "\e384\e384"; }

.fasds.fa-user-music::after, .fa-sharp-duotone.fa-user-music::after {
  content: "\f8eb\f8eb"; }

.fasds.fa-arrow-rotate-right::after, .fa-sharp-duotone.fa-arrow-rotate-right::after {
  content: "\f01e\f01e"; }

.fasds.fa-arrow-right-rotate::after, .fa-sharp-duotone.fa-arrow-right-rotate::after {
  content: "\f01e\f01e"; }

.fasds.fa-arrow-rotate-forward::after, .fa-sharp-duotone.fa-arrow-rotate-forward::after {
  content: "\f01e\f01e"; }

.fasds.fa-redo::after, .fa-sharp-duotone.fa-redo::after {
  content: "\f01e\f01e"; }

.fasds.fa-messages-dollar::after, .fa-sharp-duotone.fa-messages-dollar::after {
  content: "\f652\f652"; }

.fasds.fa-comments-alt-dollar::after, .fa-sharp-duotone.fa-comments-alt-dollar::after {
  content: "\f652\f652"; }

.fasds.fa-sensor-on::after, .fa-sharp-duotone.fa-sensor-on::after {
  content: "\e02b\e02b"; }

.fasds.fa-balloon::after, .fa-sharp-duotone.fa-balloon::after {
  content: "\e2e3\e2e3"; }

.fasds.fa-biohazard::after, .fa-sharp-duotone.fa-biohazard::after {
  content: "\f780\f780"; }

.fasds.fa-chess-queen-piece::after, .fa-sharp-duotone.fa-chess-queen-piece::after {
  content: "\f446\f446"; }

.fasds.fa-chess-queen-alt::after, .fa-sharp-duotone.fa-chess-queen-alt::after {
  content: "\f446\f446"; }

.fasds.fa-location-crosshairs::after, .fa-sharp-duotone.fa-location-crosshairs::after {
  content: "\f601\f601"; }

.fasds.fa-location::after, .fa-sharp-duotone.fa-location::after {
  content: "\f601\f601"; }

.fasds.fa-mars-double::after, .fa-sharp-duotone.fa-mars-double::after {
  content: "\f227\f227"; }

.fasds.fa-left-from-bracket::after, .fa-sharp-duotone.fa-left-from-bracket::after {
  content: "\e66c\e66c"; }

.fasds.fa-house-person-leave::after, .fa-sharp-duotone.fa-house-person-leave::after {
  content: "\e00f\e00f"; }

.fasds.fa-house-leave::after, .fa-sharp-duotone.fa-house-leave::after {
  content: "\e00f\e00f"; }

.fasds.fa-house-person-depart::after, .fa-sharp-duotone.fa-house-person-depart::after {
  content: "\e00f\e00f"; }

.fasds.fa-ruler-triangle::after, .fa-sharp-duotone.fa-ruler-triangle::after {
  content: "\f61c\f61c"; }

.fasds.fa-card-club::after, .fa-sharp-duotone.fa-card-club::after {
  content: "\e3e9\e3e9"; }

.fasds.fa-child-dress::after, .fa-sharp-duotone.fa-child-dress::after {
  content: "\e59c\e59c"; }

.fasds.fa-users-between-lines::after, .fa-sharp-duotone.fa-users-between-lines::after {
  content: "\e591\e591"; }

.fasds.fa-lungs-virus::after, .fa-sharp-duotone.fa-lungs-virus::after {
  content: "\e067\e067"; }

.fasds.fa-spinner-third::after, .fa-sharp-duotone.fa-spinner-third::after {
  content: "\f3f4\f3f4"; }

.fasds.fa-face-grin-tears::after, .fa-sharp-duotone.fa-face-grin-tears::after {
  content: "\f588\f588"; }

.fasds.fa-grin-tears::after, .fa-sharp-duotone.fa-grin-tears::after {
  content: "\f588\f588"; }

.fasds.fa-phone::after, .fa-sharp-duotone.fa-phone::after {
  content: "\f095\f095"; }

.fasds.fa-computer-mouse-scrollwheel::after, .fa-sharp-duotone.fa-computer-mouse-scrollwheel::after {
  content: "\f8cd\f8cd"; }

.fasds.fa-mouse-alt::after, .fa-sharp-duotone.fa-mouse-alt::after {
  content: "\f8cd\f8cd"; }

.fasds.fa-calendar-xmark::after, .fa-sharp-duotone.fa-calendar-xmark::after {
  content: "\f273\f273"; }

.fasds.fa-calendar-times::after, .fa-sharp-duotone.fa-calendar-times::after {
  content: "\f273\f273"; }

.fasds.fa-child-reaching::after, .fa-sharp-duotone.fa-child-reaching::after {
  content: "\e59d\e59d"; }

.fasds.fa-table-layout::after, .fa-sharp-duotone.fa-table-layout::after {
  content: "\e290\e290"; }

.fasds.fa-narwhal::after, .fa-sharp-duotone.fa-narwhal::after {
  content: "\f6fe\f6fe"; }

.fasds.fa-ramp-loading::after, .fa-sharp-duotone.fa-ramp-loading::after {
  content: "\f4d4\f4d4"; }

.fasds.fa-calendar-circle-plus::after, .fa-sharp-duotone.fa-calendar-circle-plus::after {
  content: "\e470\e470"; }

.fasds.fa-toothbrush::after, .fa-sharp-duotone.fa-toothbrush::after {
  content: "\f635\f635"; }

.fasds.fa-border-inner::after, .fa-sharp-duotone.fa-border-inner::after {
  content: "\f84e\f84e"; }

.fasds.fa-paw-claws::after, .fa-sharp-duotone.fa-paw-claws::after {
  content: "\f702\f702"; }

.fasds.fa-kiwi-fruit::after, .fa-sharp-duotone.fa-kiwi-fruit::after {
  content: "\e30c\e30c"; }

.fasds.fa-traffic-light-slow::after, .fa-sharp-duotone.fa-traffic-light-slow::after {
  content: "\f639\f639"; }

.fasds.fa-rectangle-code::after, .fa-sharp-duotone.fa-rectangle-code::after {
  content: "\e322\e322"; }

.fasds.fa-head-side-virus::after, .fa-sharp-duotone.fa-head-side-virus::after {
  content: "\e064\e064"; }

.fasds.fa-keyboard-brightness::after, .fa-sharp-duotone.fa-keyboard-brightness::after {
  content: "\e1c0\e1c0"; }

.fasds.fa-books-medical::after, .fa-sharp-duotone.fa-books-medical::after {
  content: "\f7e8\f7e8"; }

.fasds.fa-lightbulb-slash::after, .fa-sharp-duotone.fa-lightbulb-slash::after {
  content: "\f673\f673"; }

.fasds.fa-house-blank::after, .fa-sharp-duotone.fa-house-blank::after {
  content: "\e487\e487"; }

.fasds.fa-home-blank::after, .fa-sharp-duotone.fa-home-blank::after {
  content: "\e487\e487"; }

.fasds.fa-square-5::after, .fa-sharp-duotone.fa-square-5::after {
  content: "\e25a\e25a"; }

.fasds.fa-square-heart::after, .fa-sharp-duotone.fa-square-heart::after {
  content: "\f4c8\f4c8"; }

.fasds.fa-heart-square::after, .fa-sharp-duotone.fa-heart-square::after {
  content: "\f4c8\f4c8"; }

.fasds.fa-puzzle::after, .fa-sharp-duotone.fa-puzzle::after {
  content: "\e443\e443"; }

.fasds.fa-user-gear::after, .fa-sharp-duotone.fa-user-gear::after {
  content: "\f4fe\f4fe"; }

.fasds.fa-user-cog::after, .fa-sharp-duotone.fa-user-cog::after {
  content: "\f4fe\f4fe"; }

.fasds.fa-pipe-circle-check::after, .fa-sharp-duotone.fa-pipe-circle-check::after {
  content: "\e436\e436"; }

.fasds.fa-arrow-up-1-9::after, .fa-sharp-duotone.fa-arrow-up-1-9::after {
  content: "\f163\f163"; }

.fasds.fa-sort-numeric-up::after, .fa-sharp-duotone.fa-sort-numeric-up::after {
  content: "\f163\f163"; }

.fasds.fa-octagon-exclamation::after, .fa-sharp-duotone.fa-octagon-exclamation::after {
  content: "\e204\e204"; }

.fasds.fa-dial-low::after, .fa-sharp-duotone.fa-dial-low::after {
  content: "\e15d\e15d"; }

.fasds.fa-door-closed::after, .fa-sharp-duotone.fa-door-closed::after {
  content: "\f52a\f52a"; }

.fasds.fa-laptop-mobile::after, .fa-sharp-duotone.fa-laptop-mobile::after {
  content: "\f87a\f87a"; }

.fasds.fa-phone-laptop::after, .fa-sharp-duotone.fa-phone-laptop::after {
  content: "\f87a\f87a"; }

.fasds.fa-conveyor-belt-boxes::after, .fa-sharp-duotone.fa-conveyor-belt-boxes::after {
  content: "\f46f\f46f"; }

.fasds.fa-conveyor-belt-alt::after, .fa-sharp-duotone.fa-conveyor-belt-alt::after {
  content: "\f46f\f46f"; }

.fasds.fa-shield-virus::after, .fa-sharp-duotone.fa-shield-virus::after {
  content: "\e06c\e06c"; }

.fasds.fa-starfighter-twin-ion-engine-advanced::after, .fa-sharp-duotone.fa-starfighter-twin-ion-engine-advanced::after {
  content: "\e28e\e28e"; }

.fasds.fa-starfighter-alt-advanced::after, .fa-sharp-duotone.fa-starfighter-alt-advanced::after {
  content: "\e28e\e28e"; }

.fasds.fa-dice-six::after, .fa-sharp-duotone.fa-dice-six::after {
  content: "\f526\f526"; }

.fasds.fa-starfighter-twin-ion-engine::after, .fa-sharp-duotone.fa-starfighter-twin-ion-engine::after {
  content: "\e038\e038"; }

.fasds.fa-starfighter-alt::after, .fa-sharp-duotone.fa-starfighter-alt::after {
  content: "\e038\e038"; }

.fasds.fa-rocket-launch::after, .fa-sharp-duotone.fa-rocket-launch::after {
  content: "\e027\e027"; }

.fasds.fa-mosquito-net::after, .fa-sharp-duotone.fa-mosquito-net::after {
  content: "\e52c\e52c"; }

.fasds.fa-vent-damper::after, .fa-sharp-duotone.fa-vent-damper::after {
  content: "\e465\e465"; }

.fasds.fa-bridge-water::after, .fa-sharp-duotone.fa-bridge-water::after {
  content: "\e4ce\e4ce"; }

.fasds.fa-ban-bug::after, .fa-sharp-duotone.fa-ban-bug::after {
  content: "\f7f9\f7f9"; }

.fasds.fa-debug::after, .fa-sharp-duotone.fa-debug::after {
  content: "\f7f9\f7f9"; }

.fasds.fa-person-booth::after, .fa-sharp-duotone.fa-person-booth::after {
  content: "\f756\f756"; }

.fasds.fa-text-width::after, .fa-sharp-duotone.fa-text-width::after {
  content: "\f035\f035"; }

.fasds.fa-garage-car::after, .fa-sharp-duotone.fa-garage-car::after {
  content: "\e00a\e00a"; }

.fasds.fa-square-kanban::after, .fa-sharp-duotone.fa-square-kanban::after {
  content: "\e488\e488"; }

.fasds.fa-hat-wizard::after, .fa-sharp-duotone.fa-hat-wizard::after {
  content: "\f6e8\f6e8"; }

.fasds.fa-chart-kanban::after, .fa-sharp-duotone.fa-chart-kanban::after {
  content: "\e64f\e64f"; }

.fasds.fa-pen-fancy::after, .fa-sharp-duotone.fa-pen-fancy::after {
  content: "\f5ac\f5ac"; }

.fasds.fa-coffee-pot::after, .fa-sharp-duotone.fa-coffee-pot::after {
  content: "\e002\e002"; }

.fasds.fa-mouse-field::after, .fa-sharp-duotone.fa-mouse-field::after {
  content: "\e5a8\e5a8"; }

.fasds.fa-person-digging::after, .fa-sharp-duotone.fa-person-digging::after {
  content: "\f85e\f85e"; }

.fasds.fa-digging::after, .fa-sharp-duotone.fa-digging::after {
  content: "\f85e\f85e"; }

.fasds.fa-shower-down::after, .fa-sharp-duotone.fa-shower-down::after {
  content: "\e24d\e24d"; }

.fasds.fa-shower-alt::after, .fa-sharp-duotone.fa-shower-alt::after {
  content: "\e24d\e24d"; }

.fasds.fa-box-circle-check::after, .fa-sharp-duotone.fa-box-circle-check::after {
  content: "\e0c4\e0c4"; }

.fasds.fa-brightness::after, .fa-sharp-duotone.fa-brightness::after {
  content: "\e0c9\e0c9"; }

.fasds.fa-car-side-bolt::after, .fa-sharp-duotone.fa-car-side-bolt::after {
  content: "\e344\e344"; }

.fasds.fa-file-xml::after, .fa-sharp-duotone.fa-file-xml::after {
  content: "\e654\e654"; }

.fasds.fa-ornament::after, .fa-sharp-duotone.fa-ornament::after {
  content: "\f7b8\f7b8"; }

.fasds.fa-phone-arrow-down-left::after, .fa-sharp-duotone.fa-phone-arrow-down-left::after {
  content: "\e223\e223"; }

.fasds.fa-phone-arrow-down::after, .fa-sharp-duotone.fa-phone-arrow-down::after {
  content: "\e223\e223"; }

.fasds.fa-phone-incoming::after, .fa-sharp-duotone.fa-phone-incoming::after {
  content: "\e223\e223"; }

.fasds.fa-cloud-word::after, .fa-sharp-duotone.fa-cloud-word::after {
  content: "\e138\e138"; }

.fasds.fa-hand-fingers-crossed::after, .fa-sharp-duotone.fa-hand-fingers-crossed::after {
  content: "\e1a3\e1a3"; }

.fasds.fa-trash::after, .fa-sharp-duotone.fa-trash::after {
  content: "\f1f8\f1f8"; }

.fasds.fa-gauge-simple::after, .fa-sharp-duotone.fa-gauge-simple::after {
  content: "\f629\f629"; }

.fasds.fa-gauge-simple-med::after, .fa-sharp-duotone.fa-gauge-simple-med::after {
  content: "\f629\f629"; }

.fasds.fa-tachometer-average::after, .fa-sharp-duotone.fa-tachometer-average::after {
  content: "\f629\f629"; }

.fasds.fa-arrow-down-small-big::after, .fa-sharp-duotone.fa-arrow-down-small-big::after {
  content: "\f88d\f88d"; }

.fasds.fa-sort-size-down-alt::after, .fa-sharp-duotone.fa-sort-size-down-alt::after {
  content: "\f88d\f88d"; }

.fasds.fa-book-medical::after, .fa-sharp-duotone.fa-book-medical::after {
  content: "\f7e6\f7e6"; }

.fasds.fa-face-melting::after, .fa-sharp-duotone.fa-face-melting::after {
  content: "\e483\e483"; }

.fasds.fa-poo::after, .fa-sharp-duotone.fa-poo::after {
  content: "\f2fe\f2fe"; }

.fasds.fa-pen-clip-slash::after, .fa-sharp-duotone.fa-pen-clip-slash::after {
  content: "\e20f\e20f"; }

.fasds.fa-pen-alt-slash::after, .fa-sharp-duotone.fa-pen-alt-slash::after {
  content: "\e20f\e20f"; }

.fasds.fa-quote-right::after, .fa-sharp-duotone.fa-quote-right::after {
  content: "\f10e\f10e"; }

.fasds.fa-quote-right-alt::after, .fa-sharp-duotone.fa-quote-right-alt::after {
  content: "\f10e\f10e"; }

.fasds.fa-scroll-old::after, .fa-sharp-duotone.fa-scroll-old::after {
  content: "\f70f\f70f"; }

.fasds.fa-guitars::after, .fa-sharp-duotone.fa-guitars::after {
  content: "\f8bf\f8bf"; }

.fasds.fa-phone-xmark::after, .fa-sharp-duotone.fa-phone-xmark::after {
  content: "\e227\e227"; }

.fasds.fa-hose::after, .fa-sharp-duotone.fa-hose::after {
  content: "\e419\e419"; }

.fasds.fa-clock-six::after, .fa-sharp-duotone.fa-clock-six::after {
  content: "\e352\e352"; }

.fasds.fa-shirt::after, .fa-sharp-duotone.fa-shirt::after {
  content: "\f553\f553"; }

.fasds.fa-t-shirt::after, .fa-sharp-duotone.fa-t-shirt::after {
  content: "\f553\f553"; }

.fasds.fa-tshirt::after, .fa-sharp-duotone.fa-tshirt::after {
  content: "\f553\f553"; }

.fasds.fa-billboard::after, .fa-sharp-duotone.fa-billboard::after {
  content: "\e5cd\e5cd"; }

.fasds.fa-square-r::after, .fa-sharp-duotone.fa-square-r::after {
  content: "\e27c\e27c"; }

.fasds.fa-cubes::after, .fa-sharp-duotone.fa-cubes::after {
  content: "\f1b3\f1b3"; }

.fasds.fa-envelope-open-dollar::after, .fa-sharp-duotone.fa-envelope-open-dollar::after {
  content: "\f657\f657"; }

.fasds.fa-divide::after, .fa-sharp-duotone.fa-divide::after {
  content: "\f529\f529"; }

.fasds.fa-sun-cloud::after, .fa-sharp-duotone.fa-sun-cloud::after {
  content: "\f763\f763"; }

.fasds.fa-lamp-floor::after, .fa-sharp-duotone.fa-lamp-floor::after {
  content: "\e015\e015"; }

.fasds.fa-square-7::after, .fa-sharp-duotone.fa-square-7::after {
  content: "\e25c\e25c"; }

.fasds.fa-tenge-sign::after, .fa-sharp-duotone.fa-tenge-sign::after {
  content: "\f7d7\f7d7"; }

.fasds.fa-tenge::after, .fa-sharp-duotone.fa-tenge::after {
  content: "\f7d7\f7d7"; }

.fasds.fa-headphones::after, .fa-sharp-duotone.fa-headphones::after {
  content: "\f025\f025"; }

.fasds.fa-hands-holding::after, .fa-sharp-duotone.fa-hands-holding::after {
  content: "\f4c2\f4c2"; }

.fasds.fa-campfire::after, .fa-sharp-duotone.fa-campfire::after {
  content: "\f6ba\f6ba"; }

.fasds.fa-circle-ampersand::after, .fa-sharp-duotone.fa-circle-ampersand::after {
  content: "\e0f8\e0f8"; }

.fasds.fa-snowflakes::after, .fa-sharp-duotone.fa-snowflakes::after {
  content: "\f7cf\f7cf"; }

.fasds.fa-hands-clapping::after, .fa-sharp-duotone.fa-hands-clapping::after {
  content: "\e1a8\e1a8"; }

.fasds.fa-republican::after, .fa-sharp-duotone.fa-republican::after {
  content: "\f75e\f75e"; }

.fasds.fa-leaf-maple::after, .fa-sharp-duotone.fa-leaf-maple::after {
  content: "\f6f6\f6f6"; }

.fasds.fa-arrow-left::after, .fa-sharp-duotone.fa-arrow-left::after {
  content: "\f060\f060"; }

.fasds.fa-person-circle-xmark::after, .fa-sharp-duotone.fa-person-circle-xmark::after {
  content: "\e543\e543"; }

.fasds.fa-ruler::after, .fa-sharp-duotone.fa-ruler::after {
  content: "\f545\f545"; }

.fasds.fa-arrow-left-from-bracket::after, .fa-sharp-duotone.fa-arrow-left-from-bracket::after {
  content: "\e668\e668"; }

.fasds.fa-cup-straw-swoosh::after, .fa-sharp-duotone.fa-cup-straw-swoosh::after {
  content: "\e364\e364"; }

.fasds.fa-temperature-sun::after, .fa-sharp-duotone.fa-temperature-sun::after {
  content: "\f76a\f76a"; }

.fasds.fa-temperature-hot::after, .fa-sharp-duotone.fa-temperature-hot::after {
  content: "\f76a\f76a"; }

.fasds.fa-align-left::after, .fa-sharp-duotone.fa-align-left::after {
  content: "\f036\f036"; }

.fasds.fa-dice-d6::after, .fa-sharp-duotone.fa-dice-d6::after {
  content: "\f6d1\f6d1"; }

.fasds.fa-restroom::after, .fa-sharp-duotone.fa-restroom::after {
  content: "\f7bd\f7bd"; }

.fasds.fa-high-definition::after, .fa-sharp-duotone.fa-high-definition::after {
  content: "\e1ae\e1ae"; }

.fasds.fa-rectangle-hd::after, .fa-sharp-duotone.fa-rectangle-hd::after {
  content: "\e1ae\e1ae"; }

.fasds.fa-j::after, .fa-sharp-duotone.fa-j::after {
  content: "\4a\4a"; }

.fasds.fa-galaxy::after, .fa-sharp-duotone.fa-galaxy::after {
  content: "\e008\e008"; }

.fasds.fa-users-viewfinder::after, .fa-sharp-duotone.fa-users-viewfinder::after {
  content: "\e595\e595"; }

.fasds.fa-file-video::after, .fa-sharp-duotone.fa-file-video::after {
  content: "\f1c8\f1c8"; }

.fasds.fa-cherries::after, .fa-sharp-duotone.fa-cherries::after {
  content: "\e0ec\e0ec"; }

.fasds.fa-up-right-from-square::after, .fa-sharp-duotone.fa-up-right-from-square::after {
  content: "\f35d\f35d"; }

.fasds.fa-external-link-alt::after, .fa-sharp-duotone.fa-external-link-alt::after {
  content: "\f35d\f35d"; }

.fasds.fa-circle-sort::after, .fa-sharp-duotone.fa-circle-sort::after {
  content: "\e030\e030"; }

.fasds.fa-sort-circle::after, .fa-sharp-duotone.fa-sort-circle::after {
  content: "\e030\e030"; }

.fasds.fa-table-cells::after, .fa-sharp-duotone.fa-table-cells::after {
  content: "\f00a\f00a"; }

.fasds.fa-th::after, .fa-sharp-duotone.fa-th::after {
  content: "\f00a\f00a"; }

.fasds.fa-bag-shopping-minus::after, .fa-sharp-duotone.fa-bag-shopping-minus::after {
  content: "\e650\e650"; }

.fasds.fa-file-pdf::after, .fa-sharp-duotone.fa-file-pdf::after {
  content: "\f1c1\f1c1"; }

.fasds.fa-siren::after, .fa-sharp-duotone.fa-siren::after {
  content: "\e02d\e02d"; }

.fasds.fa-arrow-up-to-dotted-line::after, .fa-sharp-duotone.fa-arrow-up-to-dotted-line::after {
  content: "\e0a1\e0a1"; }

.fasds.fa-image-landscape::after, .fa-sharp-duotone.fa-image-landscape::after {
  content: "\e1b5\e1b5"; }

.fasds.fa-landscape::after, .fa-sharp-duotone.fa-landscape::after {
  content: "\e1b5\e1b5"; }

.fasds.fa-tank-water::after, .fa-sharp-duotone.fa-tank-water::after {
  content: "\e452\e452"; }

.fasds.fa-curling-stone::after, .fa-sharp-duotone.fa-curling-stone::after {
  content: "\f44a\f44a"; }

.fasds.fa-curling::after, .fa-sharp-duotone.fa-curling::after {
  content: "\f44a\f44a"; }

.fasds.fa-gamepad-modern::after, .fa-sharp-duotone.fa-gamepad-modern::after {
  content: "\e5a2\e5a2"; }

.fasds.fa-gamepad-alt::after, .fa-sharp-duotone.fa-gamepad-alt::after {
  content: "\e5a2\e5a2"; }

.fasds.fa-messages-question::after, .fa-sharp-duotone.fa-messages-question::after {
  content: "\e1e7\e1e7"; }

.fasds.fa-book-bible::after, .fa-sharp-duotone.fa-book-bible::after {
  content: "\f647\f647"; }

.fasds.fa-bible::after, .fa-sharp-duotone.fa-bible::after {
  content: "\f647\f647"; }

.fasds.fa-o::after, .fa-sharp-duotone.fa-o::after {
  content: "\4f\4f"; }

.fasds.fa-suitcase-medical::after, .fa-sharp-duotone.fa-suitcase-medical::after {
  content: "\f0fa\f0fa"; }

.fasds.fa-medkit::after, .fa-sharp-duotone.fa-medkit::after {
  content: "\f0fa\f0fa"; }

.fasds.fa-briefcase-arrow-right::after, .fa-sharp-duotone.fa-briefcase-arrow-right::after {
  content: "\e2f2\e2f2"; }

.fasds.fa-expand-wide::after, .fa-sharp-duotone.fa-expand-wide::after {
  content: "\f320\f320"; }

.fasds.fa-clock-eleven-thirty::after, .fa-sharp-duotone.fa-clock-eleven-thirty::after {
  content: "\e348\e348"; }

.fasds.fa-rv::after, .fa-sharp-duotone.fa-rv::after {
  content: "\f7be\f7be"; }

.fasds.fa-user-secret::after, .fa-sharp-duotone.fa-user-secret::after {
  content: "\f21b\f21b"; }

.fasds.fa-otter::after, .fa-sharp-duotone.fa-otter::after {
  content: "\f700\f700"; }

.fasds.fa-dreidel::after, .fa-sharp-duotone.fa-dreidel::after {
  content: "\f792\f792"; }

.fasds.fa-person-dress::after, .fa-sharp-duotone.fa-person-dress::after {
  content: "\f182\f182"; }

.fasds.fa-female::after, .fa-sharp-duotone.fa-female::after {
  content: "\f182\f182"; }

.fasds.fa-comment-dollar::after, .fa-sharp-duotone.fa-comment-dollar::after {
  content: "\f651\f651"; }

.fasds.fa-business-time::after, .fa-sharp-duotone.fa-business-time::after {
  content: "\f64a\f64a"; }

.fasds.fa-briefcase-clock::after, .fa-sharp-duotone.fa-briefcase-clock::after {
  content: "\f64a\f64a"; }

.fasds.fa-flower-tulip::after, .fa-sharp-duotone.fa-flower-tulip::after {
  content: "\f801\f801"; }

.fasds.fa-people-pants-simple::after, .fa-sharp-duotone.fa-people-pants-simple::after {
  content: "\e21a\e21a"; }

.fasds.fa-cloud-drizzle::after, .fa-sharp-duotone.fa-cloud-drizzle::after {
  content: "\f738\f738"; }

.fasds.fa-table-cells-large::after, .fa-sharp-duotone.fa-table-cells-large::after {
  content: "\f009\f009"; }

.fasds.fa-th-large::after, .fa-sharp-duotone.fa-th-large::after {
  content: "\f009\f009"; }

.fasds.fa-book-tanakh::after, .fa-sharp-duotone.fa-book-tanakh::after {
  content: "\f827\f827"; }

.fasds.fa-tanakh::after, .fa-sharp-duotone.fa-tanakh::after {
  content: "\f827\f827"; }

.fasds.fa-solar-system::after, .fa-sharp-duotone.fa-solar-system::after {
  content: "\e02f\e02f"; }

.fasds.fa-seal-question::after, .fa-sharp-duotone.fa-seal-question::after {
  content: "\e243\e243"; }

.fasds.fa-phone-volume::after, .fa-sharp-duotone.fa-phone-volume::after {
  content: "\f2a0\f2a0"; }

.fasds.fa-volume-control-phone::after, .fa-sharp-duotone.fa-volume-control-phone::after {
  content: "\f2a0\f2a0"; }

.fasds.fa-disc-drive::after, .fa-sharp-duotone.fa-disc-drive::after {
  content: "\f8b5\f8b5"; }

.fasds.fa-hat-cowboy-side::after, .fa-sharp-duotone.fa-hat-cowboy-side::after {
  content: "\f8c1\f8c1"; }

.fasds.fa-table-rows::after, .fa-sharp-duotone.fa-table-rows::after {
  content: "\e292\e292"; }

.fasds.fa-rows::after, .fa-sharp-duotone.fa-rows::after {
  content: "\e292\e292"; }

.fasds.fa-location-exclamation::after, .fa-sharp-duotone.fa-location-exclamation::after {
  content: "\f608\f608"; }

.fasds.fa-map-marker-exclamation::after, .fa-sharp-duotone.fa-map-marker-exclamation::after {
  content: "\f608\f608"; }

.fasds.fa-face-fearful::after, .fa-sharp-duotone.fa-face-fearful::after {
  content: "\e375\e375"; }

.fasds.fa-clipboard-user::after, .fa-sharp-duotone.fa-clipboard-user::after {
  content: "\f7f3\f7f3"; }

.fasds.fa-bus-school::after, .fa-sharp-duotone.fa-bus-school::after {
  content: "\f5dd\f5dd"; }

.fasds.fa-film-slash::after, .fa-sharp-duotone.fa-film-slash::after {
  content: "\e179\e179"; }

.fasds.fa-square-arrow-down-right::after, .fa-sharp-duotone.fa-square-arrow-down-right::after {
  content: "\e262\e262"; }

.fasds.fa-book-sparkles::after, .fa-sharp-duotone.fa-book-sparkles::after {
  content: "\f6b8\f6b8"; }

.fasds.fa-book-spells::after, .fa-sharp-duotone.fa-book-spells::after {
  content: "\f6b8\f6b8"; }

.fasds.fa-washing-machine::after, .fa-sharp-duotone.fa-washing-machine::after {
  content: "\f898\f898"; }

.fasds.fa-washer::after, .fa-sharp-duotone.fa-washer::after {
  content: "\f898\f898"; }

.fasds.fa-child::after, .fa-sharp-duotone.fa-child::after {
  content: "\f1ae\f1ae"; }

.fasds.fa-lira-sign::after, .fa-sharp-duotone.fa-lira-sign::after {
  content: "\f195\f195"; }

.fasds.fa-user-visor::after, .fa-sharp-duotone.fa-user-visor::after {
  content: "\e04c\e04c"; }

.fasds.fa-file-plus-minus::after, .fa-sharp-duotone.fa-file-plus-minus::after {
  content: "\e177\e177"; }

.fasds.fa-chess-clock-flip::after, .fa-sharp-duotone.fa-chess-clock-flip::after {
  content: "\f43e\f43e"; }

.fasds.fa-chess-clock-alt::after, .fa-sharp-duotone.fa-chess-clock-alt::after {
  content: "\f43e\f43e"; }

.fasds.fa-satellite::after, .fa-sharp-duotone.fa-satellite::after {
  content: "\f7bf\f7bf"; }

.fasds.fa-truck-fire::after, .fa-sharp-duotone.fa-truck-fire::after {
  content: "\e65a\e65a"; }

.fasds.fa-plane-lock::after, .fa-sharp-duotone.fa-plane-lock::after {
  content: "\e558\e558"; }

.fasds.fa-steering-wheel::after, .fa-sharp-duotone.fa-steering-wheel::after {
  content: "\f622\f622"; }

.fasds.fa-tag::after, .fa-sharp-duotone.fa-tag::after {
  content: "\f02b\f02b"; }

.fasds.fa-stretcher::after, .fa-sharp-duotone.fa-stretcher::after {
  content: "\f825\f825"; }

.fasds.fa-book-section::after, .fa-sharp-duotone.fa-book-section::after {
  content: "\e0c1\e0c1"; }

.fasds.fa-book-law::after, .fa-sharp-duotone.fa-book-law::after {
  content: "\e0c1\e0c1"; }

.fasds.fa-inboxes::after, .fa-sharp-duotone.fa-inboxes::after {
  content: "\e1bb\e1bb"; }

.fasds.fa-coffee-bean::after, .fa-sharp-duotone.fa-coffee-bean::after {
  content: "\e13e\e13e"; }

.fasds.fa-circle-yen::after, .fa-sharp-duotone.fa-circle-yen::after {
  content: "\e5d0\e5d0"; }

.fasds.fa-brackets-curly::after, .fa-sharp-duotone.fa-brackets-curly::after {
  content: "\f7ea\f7ea"; }

.fasds.fa-ellipsis-stroke-vertical::after, .fa-sharp-duotone.fa-ellipsis-stroke-vertical::after {
  content: "\f39c\f39c"; }

.fasds.fa-ellipsis-v-alt::after, .fa-sharp-duotone.fa-ellipsis-v-alt::after {
  content: "\f39c\f39c"; }

.fasds.fa-comment::after, .fa-sharp-duotone.fa-comment::after {
  content: "\f075\f075"; }

.fasds.fa-square-1::after, .fa-sharp-duotone.fa-square-1::after {
  content: "\e256\e256"; }

.fasds.fa-cake-candles::after, .fa-sharp-duotone.fa-cake-candles::after {
  content: "\f1fd\f1fd"; }

.fasds.fa-birthday-cake::after, .fa-sharp-duotone.fa-birthday-cake::after {
  content: "\f1fd\f1fd"; }

.fasds.fa-cake::after, .fa-sharp-duotone.fa-cake::after {
  content: "\f1fd\f1fd"; }

.fasds.fa-head-side::after, .fa-sharp-duotone.fa-head-side::after {
  content: "\f6e9\f6e9"; }

.fasds.fa-truck-ladder::after, .fa-sharp-duotone.fa-truck-ladder::after {
  content: "\e657\e657"; }

.fasds.fa-envelope::after, .fa-sharp-duotone.fa-envelope::after {
  content: "\f0e0\f0e0"; }

.fasds.fa-dolly-empty::after, .fa-sharp-duotone.fa-dolly-empty::after {
  content: "\f473\f473"; }

.fasds.fa-face-tissue::after, .fa-sharp-duotone.fa-face-tissue::after {
  content: "\e39c\e39c"; }

.fasds.fa-angles-up::after, .fa-sharp-duotone.fa-angles-up::after {
  content: "\f102\f102"; }

.fasds.fa-angle-double-up::after, .fa-sharp-duotone.fa-angle-double-up::after {
  content: "\f102\f102"; }

.fasds.fa-bin-recycle::after, .fa-sharp-duotone.fa-bin-recycle::after {
  content: "\e5f7\e5f7"; }

.fasds.fa-paperclip::after, .fa-sharp-duotone.fa-paperclip::after {
  content: "\f0c6\f0c6"; }

.fasds.fa-chart-line-down::after, .fa-sharp-duotone.fa-chart-line-down::after {
  content: "\f64d\f64d"; }

.fasds.fa-arrow-right-to-city::after, .fa-sharp-duotone.fa-arrow-right-to-city::after {
  content: "\e4b3\e4b3"; }

.fasds.fa-lock-a::after, .fa-sharp-duotone.fa-lock-a::after {
  content: "\e422\e422"; }

.fasds.fa-ribbon::after, .fa-sharp-duotone.fa-ribbon::after {
  content: "\f4d6\f4d6"; }

.fasds.fa-lungs::after, .fa-sharp-duotone.fa-lungs::after {
  content: "\f604\f604"; }

.fasds.fa-person-pinball::after, .fa-sharp-duotone.fa-person-pinball::after {
  content: "\e21d\e21d"; }

.fasds.fa-arrow-up-9-1::after, .fa-sharp-duotone.fa-arrow-up-9-1::after {
  content: "\f887\f887"; }

.fasds.fa-sort-numeric-up-alt::after, .fa-sharp-duotone.fa-sort-numeric-up-alt::after {
  content: "\f887\f887"; }

.fasds.fa-apple-core::after, .fa-sharp-duotone.fa-apple-core::after {
  content: "\e08f\e08f"; }

.fasds.fa-circle-y::after, .fa-sharp-duotone.fa-circle-y::after {
  content: "\e12f\e12f"; }

.fasds.fa-h6::after, .fa-sharp-duotone.fa-h6::after {
  content: "\e413\e413"; }

.fasds.fa-litecoin-sign::after, .fa-sharp-duotone.fa-litecoin-sign::after {
  content: "\e1d3\e1d3"; }

.fasds.fa-bottle-baby::after, .fa-sharp-duotone.fa-bottle-baby::after {
  content: "\e673\e673"; }

.fasds.fa-circle-small::after, .fa-sharp-duotone.fa-circle-small::after {
  content: "\e122\e122"; }

.fasds.fa-border-none::after, .fa-sharp-duotone.fa-border-none::after {
  content: "\f850\f850"; }

.fasds.fa-arrow-turn-down-left::after, .fa-sharp-duotone.fa-arrow-turn-down-left::after {
  content: "\e2e1\e2e1"; }

.fasds.fa-circle-wifi-circle-wifi::after, .fa-sharp-duotone.fa-circle-wifi-circle-wifi::after {
  content: "\e67e\e67e"; }

.fasds.fa-circle-wifi-group::after, .fa-sharp-duotone.fa-circle-wifi-group::after {
  content: "\e67e\e67e"; }

.fasds.fa-circle-nodes::after, .fa-sharp-duotone.fa-circle-nodes::after {
  content: "\e4e2\e4e2"; }

.fasds.fa-parachute-box::after, .fa-sharp-duotone.fa-parachute-box::after {
  content: "\f4cd\f4cd"; }

.fasds.fa-reflect-horizontal::after, .fa-sharp-duotone.fa-reflect-horizontal::after {
  content: "\e664\e664"; }

.fasds.fa-message-medical::after, .fa-sharp-duotone.fa-message-medical::after {
  content: "\f7f4\f7f4"; }

.fasds.fa-comment-alt-medical::after, .fa-sharp-duotone.fa-comment-alt-medical::after {
  content: "\f7f4\f7f4"; }

.fasds.fa-rugby-ball::after, .fa-sharp-duotone.fa-rugby-ball::after {
  content: "\e3c6\e3c6"; }

.fasds.fa-comment-music::after, .fa-sharp-duotone.fa-comment-music::after {
  content: "\f8b0\f8b0"; }

.fasds.fa-indent::after, .fa-sharp-duotone.fa-indent::after {
  content: "\f03c\f03c"; }

.fasds.fa-tree-deciduous::after, .fa-sharp-duotone.fa-tree-deciduous::after {
  content: "\f400\f400"; }

.fasds.fa-tree-alt::after, .fa-sharp-duotone.fa-tree-alt::after {
  content: "\f400\f400"; }

.fasds.fa-puzzle-piece-simple::after, .fa-sharp-duotone.fa-puzzle-piece-simple::after {
  content: "\e231\e231"; }

.fasds.fa-puzzle-piece-alt::after, .fa-sharp-duotone.fa-puzzle-piece-alt::after {
  content: "\e231\e231"; }

.fasds.fa-truck-field-un::after, .fa-sharp-duotone.fa-truck-field-un::after {
  content: "\e58e\e58e"; }

.fasds.fa-nfc-trash::after, .fa-sharp-duotone.fa-nfc-trash::after {
  content: "\e1fd\e1fd"; }

.fasds.fa-hourglass::after, .fa-sharp-duotone.fa-hourglass::after {
  content: "\f254\f254"; }

.fasds.fa-hourglass-empty::after, .fa-sharp-duotone.fa-hourglass-empty::after {
  content: "\f254\f254"; }

.fasds.fa-mountain::after, .fa-sharp-duotone.fa-mountain::after {
  content: "\f6fc\f6fc"; }

.fasds.fa-file-xmark::after, .fa-sharp-duotone.fa-file-xmark::after {
  content: "\f317\f317"; }

.fasds.fa-file-times::after, .fa-sharp-duotone.fa-file-times::after {
  content: "\f317\f317"; }

.fasds.fa-house-heart::after, .fa-sharp-duotone.fa-house-heart::after {
  content: "\f4c9\f4c9"; }

.fasds.fa-home-heart::after, .fa-sharp-duotone.fa-home-heart::after {
  content: "\f4c9\f4c9"; }

.fasds.fa-house-chimney-blank::after, .fa-sharp-duotone.fa-house-chimney-blank::after {
  content: "\e3b0\e3b0"; }

.fasds.fa-meter-bolt::after, .fa-sharp-duotone.fa-meter-bolt::after {
  content: "\e1e9\e1e9"; }

.fasds.fa-user-doctor::after, .fa-sharp-duotone.fa-user-doctor::after {
  content: "\f0f0\f0f0"; }

.fasds.fa-user-md::after, .fa-sharp-duotone.fa-user-md::after {
  content: "\f0f0\f0f0"; }

.fasds.fa-slash-back::after, .fa-sharp-duotone.fa-slash-back::after {
  content: "\5c\5c"; }

.fasds.fa-circle-info::after, .fa-sharp-duotone.fa-circle-info::after {
  content: "\f05a\f05a"; }

.fasds.fa-info-circle::after, .fa-sharp-duotone.fa-info-circle::after {
  content: "\f05a\f05a"; }

.fasds.fa-fishing-rod::after, .fa-sharp-duotone.fa-fishing-rod::after {
  content: "\e3a8\e3a8"; }

.fasds.fa-hammer-crash::after, .fa-sharp-duotone.fa-hammer-crash::after {
  content: "\e414\e414"; }

.fasds.fa-message-heart::after, .fa-sharp-duotone.fa-message-heart::after {
  content: "\e5c9\e5c9"; }

.fasds.fa-cloud-meatball::after, .fa-sharp-duotone.fa-cloud-meatball::after {
  content: "\f73b\f73b"; }

.fasds.fa-camera-polaroid::after, .fa-sharp-duotone.fa-camera-polaroid::after {
  content: "\f8aa\f8aa"; }

.fasds.fa-camera::after, .fa-sharp-duotone.fa-camera::after {
  content: "\f030\f030"; }

.fasds.fa-camera-alt::after, .fa-sharp-duotone.fa-camera-alt::after {
  content: "\f030\f030"; }

.fasds.fa-square-virus::after, .fa-sharp-duotone.fa-square-virus::after {
  content: "\e578\e578"; }

.fasds.fa-cart-arrow-up::after, .fa-sharp-duotone.fa-cart-arrow-up::after {
  content: "\e3ee\e3ee"; }

.fasds.fa-meteor::after, .fa-sharp-duotone.fa-meteor::after {
  content: "\f753\f753"; }

.fasds.fa-car-on::after, .fa-sharp-duotone.fa-car-on::after {
  content: "\e4dd\e4dd"; }

.fasds.fa-sleigh::after, .fa-sharp-duotone.fa-sleigh::after {
  content: "\f7cc\f7cc"; }

.fasds.fa-arrow-down-1-9::after, .fa-sharp-duotone.fa-arrow-down-1-9::after {
  content: "\f162\f162"; }

.fasds.fa-sort-numeric-asc::after, .fa-sharp-duotone.fa-sort-numeric-asc::after {
  content: "\f162\f162"; }

.fasds.fa-sort-numeric-down::after, .fa-sharp-duotone.fa-sort-numeric-down::after {
  content: "\f162\f162"; }

.fasds.fa-buoy-mooring::after, .fa-sharp-duotone.fa-buoy-mooring::after {
  content: "\e5b6\e5b6"; }

.fasds.fa-square-4::after, .fa-sharp-duotone.fa-square-4::after {
  content: "\e259\e259"; }

.fasds.fa-hand-holding-droplet::after, .fa-sharp-duotone.fa-hand-holding-droplet::after {
  content: "\f4c1\f4c1"; }

.fasds.fa-hand-holding-water::after, .fa-sharp-duotone.fa-hand-holding-water::after {
  content: "\f4c1\f4c1"; }

.fasds.fa-file-eps::after, .fa-sharp-duotone.fa-file-eps::after {
  content: "\e644\e644"; }

.fasds.fa-tricycle-adult::after, .fa-sharp-duotone.fa-tricycle-adult::after {
  content: "\e5c4\e5c4"; }

.fasds.fa-waveform::after, .fa-sharp-duotone.fa-waveform::after {
  content: "\f8f1\f8f1"; }

.fasds.fa-water::after, .fa-sharp-duotone.fa-water::after {
  content: "\f773\f773"; }

.fasds.fa-star-sharp-half-stroke::after, .fa-sharp-duotone.fa-star-sharp-half-stroke::after {
  content: "\e28d\e28d"; }

.fasds.fa-star-sharp-half-alt::after, .fa-sharp-duotone.fa-star-sharp-half-alt::after {
  content: "\e28d\e28d"; }

.fasds.fa-nfc-signal::after, .fa-sharp-duotone.fa-nfc-signal::after {
  content: "\e1fb\e1fb"; }

.fasds.fa-plane-prop::after, .fa-sharp-duotone.fa-plane-prop::after {
  content: "\e22b\e22b"; }

.fasds.fa-calendar-check::after, .fa-sharp-duotone.fa-calendar-check::after {
  content: "\f274\f274"; }

.fasds.fa-clock-desk::after, .fa-sharp-duotone.fa-clock-desk::after {
  content: "\e134\e134"; }

.fasds.fa-calendar-clock::after, .fa-sharp-duotone.fa-calendar-clock::after {
  content: "\e0d2\e0d2"; }

.fasds.fa-calendar-time::after, .fa-sharp-duotone.fa-calendar-time::after {
  content: "\e0d2\e0d2"; }

.fasds.fa-braille::after, .fa-sharp-duotone.fa-braille::after {
  content: "\f2a1\f2a1"; }

.fasds.fa-prescription-bottle-medical::after, .fa-sharp-duotone.fa-prescription-bottle-medical::after {
  content: "\f486\f486"; }

.fasds.fa-prescription-bottle-alt::after, .fa-sharp-duotone.fa-prescription-bottle-alt::after {
  content: "\f486\f486"; }

.fasds.fa-plate-utensils::after, .fa-sharp-duotone.fa-plate-utensils::after {
  content: "\e43b\e43b"; }

.fasds.fa-family-pants::after, .fa-sharp-duotone.fa-family-pants::after {
  content: "\e302\e302"; }

.fasds.fa-hose-reel::after, .fa-sharp-duotone.fa-hose-reel::after {
  content: "\e41a\e41a"; }

.fasds.fa-house-window::after, .fa-sharp-duotone.fa-house-window::after {
  content: "\e3b3\e3b3"; }

.fasds.fa-landmark::after, .fa-sharp-duotone.fa-landmark::after {
  content: "\f66f\f66f"; }

.fasds.fa-truck::after, .fa-sharp-duotone.fa-truck::after {
  content: "\f0d1\f0d1"; }

.fasds.fa-music-magnifying-glass::after, .fa-sharp-duotone.fa-music-magnifying-glass::after {
  content: "\e662\e662"; }

.fasds.fa-crosshairs::after, .fa-sharp-duotone.fa-crosshairs::after {
  content: "\f05b\f05b"; }

.fasds.fa-cloud-rainbow::after, .fa-sharp-duotone.fa-cloud-rainbow::after {
  content: "\f73e\f73e"; }

.fasds.fa-person-cane::after, .fa-sharp-duotone.fa-person-cane::after {
  content: "\e53c\e53c"; }

.fasds.fa-alien::after, .fa-sharp-duotone.fa-alien::after {
  content: "\f8f5\f8f5"; }

.fasds.fa-tent::after, .fa-sharp-duotone.fa-tent::after {
  content: "\e57d\e57d"; }

.fasds.fa-laptop-binary::after, .fa-sharp-duotone.fa-laptop-binary::after {
  content: "\e5e7\e5e7"; }

.fasds.fa-vest-patches::after, .fa-sharp-duotone.fa-vest-patches::after {
  content: "\e086\e086"; }

.fasds.fa-people-dress-simple::after, .fa-sharp-duotone.fa-people-dress-simple::after {
  content: "\e218\e218"; }

.fasds.fa-check-double::after, .fa-sharp-duotone.fa-check-double::after {
  content: "\f560\f560"; }

.fasds.fa-arrow-down-a-z::after, .fa-sharp-duotone.fa-arrow-down-a-z::after {
  content: "\f15d\f15d"; }

.fasds.fa-sort-alpha-asc::after, .fa-sharp-duotone.fa-sort-alpha-asc::after {
  content: "\f15d\f15d"; }

.fasds.fa-sort-alpha-down::after, .fa-sharp-duotone.fa-sort-alpha-down::after {
  content: "\f15d\f15d"; }

.fasds.fa-bowling-ball-pin::after, .fa-sharp-duotone.fa-bowling-ball-pin::after {
  content: "\e0c3\e0c3"; }

.fasds.fa-bell-school-slash::after, .fa-sharp-duotone.fa-bell-school-slash::after {
  content: "\f5d6\f5d6"; }

.fasds.fa-plus-large::after, .fa-sharp-duotone.fa-plus-large::after {
  content: "\e59e\e59e"; }

.fasds.fa-money-bill-wheat::after, .fa-sharp-duotone.fa-money-bill-wheat::after {
  content: "\e52a\e52a"; }

.fasds.fa-camera-viewfinder::after, .fa-sharp-duotone.fa-camera-viewfinder::after {
  content: "\e0da\e0da"; }

.fasds.fa-screenshot::after, .fa-sharp-duotone.fa-screenshot::after {
  content: "\e0da\e0da"; }

.fasds.fa-message-music::after, .fa-sharp-duotone.fa-message-music::after {
  content: "\f8af\f8af"; }

.fasds.fa-comment-alt-music::after, .fa-sharp-duotone.fa-comment-alt-music::after {
  content: "\f8af\f8af"; }

.fasds.fa-car-building::after, .fa-sharp-duotone.fa-car-building::after {
  content: "\f859\f859"; }

.fasds.fa-border-bottom-right::after, .fa-sharp-duotone.fa-border-bottom-right::after {
  content: "\f854\f854"; }

.fasds.fa-border-style-alt::after, .fa-sharp-duotone.fa-border-style-alt::after {
  content: "\f854\f854"; }

.fasds.fa-octagon::after, .fa-sharp-duotone.fa-octagon::after {
  content: "\f306\f306"; }

.fasds.fa-comment-arrow-up-right::after, .fa-sharp-duotone.fa-comment-arrow-up-right::after {
  content: "\e145\e145"; }

.fasds.fa-octagon-divide::after, .fa-sharp-duotone.fa-octagon-divide::after {
  content: "\e203\e203"; }

.fasds.fa-cookie::after, .fa-sharp-duotone.fa-cookie::after {
  content: "\f563\f563"; }

.fasds.fa-arrow-rotate-left::after, .fa-sharp-duotone.fa-arrow-rotate-left::after {
  content: "\f0e2\f0e2"; }

.fasds.fa-arrow-left-rotate::after, .fa-sharp-duotone.fa-arrow-left-rotate::after {
  content: "\f0e2\f0e2"; }

.fasds.fa-arrow-rotate-back::after, .fa-sharp-duotone.fa-arrow-rotate-back::after {
  content: "\f0e2\f0e2"; }

.fasds.fa-arrow-rotate-backward::after, .fa-sharp-duotone.fa-arrow-rotate-backward::after {
  content: "\f0e2\f0e2"; }

.fasds.fa-undo::after, .fa-sharp-duotone.fa-undo::after {
  content: "\f0e2\f0e2"; }

.fasds.fa-tv-music::after, .fa-sharp-duotone.fa-tv-music::after {
  content: "\f8e6\f8e6"; }

.fasds.fa-hard-drive::after, .fa-sharp-duotone.fa-hard-drive::after {
  content: "\f0a0\f0a0"; }

.fasds.fa-hdd::after, .fa-sharp-duotone.fa-hdd::after {
  content: "\f0a0\f0a0"; }

.fasds.fa-reel::after, .fa-sharp-duotone.fa-reel::after {
  content: "\e238\e238"; }

.fasds.fa-face-grin-squint-tears::after, .fa-sharp-duotone.fa-face-grin-squint-tears::after {
  content: "\f586\f586"; }

.fasds.fa-grin-squint-tears::after, .fa-sharp-duotone.fa-grin-squint-tears::after {
  content: "\f586\f586"; }

.fasds.fa-dumbbell::after, .fa-sharp-duotone.fa-dumbbell::after {
  content: "\f44b\f44b"; }

.fasds.fa-rectangle-list::after, .fa-sharp-duotone.fa-rectangle-list::after {
  content: "\f022\f022"; }

.fasds.fa-list-alt::after, .fa-sharp-duotone.fa-list-alt::after {
  content: "\f022\f022"; }

.fasds.fa-tarp-droplet::after, .fa-sharp-duotone.fa-tarp-droplet::after {
  content: "\e57c\e57c"; }

.fasds.fa-alarm-exclamation::after, .fa-sharp-duotone.fa-alarm-exclamation::after {
  content: "\f843\f843"; }

.fasds.fa-house-medical-circle-check::after, .fa-sharp-duotone.fa-house-medical-circle-check::after {
  content: "\e511\e511"; }

.fasds.fa-traffic-cone::after, .fa-sharp-duotone.fa-traffic-cone::after {
  content: "\f636\f636"; }

.fasds.fa-grate::after, .fa-sharp-duotone.fa-grate::after {
  content: "\e193\e193"; }

.fasds.fa-arrow-down-right::after, .fa-sharp-duotone.fa-arrow-down-right::after {
  content: "\e093\e093"; }

.fasds.fa-person-skiing-nordic::after, .fa-sharp-duotone.fa-person-skiing-nordic::after {
  content: "\f7ca\f7ca"; }

.fasds.fa-skiing-nordic::after, .fa-sharp-duotone.fa-skiing-nordic::after {
  content: "\f7ca\f7ca"; }

.fasds.fa-calendar-plus::after, .fa-sharp-duotone.fa-calendar-plus::after {
  content: "\f271\f271"; }

.fasds.fa-person-from-portal::after, .fa-sharp-duotone.fa-person-from-portal::after {
  content: "\e023\e023"; }

.fasds.fa-portal-exit::after, .fa-sharp-duotone.fa-portal-exit::after {
  content: "\e023\e023"; }

.fasds.fa-plane-arrival::after, .fa-sharp-duotone.fa-plane-arrival::after {
  content: "\f5af\f5af"; }

.fasds.fa-cowbell-circle-plus::after, .fa-sharp-duotone.fa-cowbell-circle-plus::after {
  content: "\f8b4\f8b4"; }

.fasds.fa-cowbell-more::after, .fa-sharp-duotone.fa-cowbell-more::after {
  content: "\f8b4\f8b4"; }

.fasds.fa-circle-left::after, .fa-sharp-duotone.fa-circle-left::after {
  content: "\f359\f359"; }

.fasds.fa-arrow-alt-circle-left::after, .fa-sharp-duotone.fa-arrow-alt-circle-left::after {
  content: "\f359\f359"; }

.fasds.fa-distribute-spacing-vertical::after, .fa-sharp-duotone.fa-distribute-spacing-vertical::after {
  content: "\e366\e366"; }

.fasds.fa-signal-bars-fair::after, .fa-sharp-duotone.fa-signal-bars-fair::after {
  content: "\f692\f692"; }

.fasds.fa-signal-alt-2::after, .fa-sharp-duotone.fa-signal-alt-2::after {
  content: "\f692\f692"; }

.fasds.fa-sportsball::after, .fa-sharp-duotone.fa-sportsball::after {
  content: "\e44b\e44b"; }

.fasds.fa-game-console-handheld-crank::after, .fa-sharp-duotone.fa-game-console-handheld-crank::after {
  content: "\e5b9\e5b9"; }

.fasds.fa-train-subway::after, .fa-sharp-duotone.fa-train-subway::after {
  content: "\f239\f239"; }

.fasds.fa-subway::after, .fa-sharp-duotone.fa-subway::after {
  content: "\f239\f239"; }

.fasds.fa-chart-gantt::after, .fa-sharp-duotone.fa-chart-gantt::after {
  content: "\e0e4\e0e4"; }

.fasds.fa-face-smile-upside-down::after, .fa-sharp-duotone.fa-face-smile-upside-down::after {
  content: "\e395\e395"; }

.fasds.fa-ball-pile::after, .fa-sharp-duotone.fa-ball-pile::after {
  content: "\f77e\f77e"; }

.fasds.fa-badge-dollar::after, .fa-sharp-duotone.fa-badge-dollar::after {
  content: "\f645\f645"; }

.fasds.fa-money-bills-simple::after, .fa-sharp-duotone.fa-money-bills-simple::after {
  content: "\e1f4\e1f4"; }

.fasds.fa-money-bills-alt::after, .fa-sharp-duotone.fa-money-bills-alt::after {
  content: "\e1f4\e1f4"; }

.fasds.fa-list-timeline::after, .fa-sharp-duotone.fa-list-timeline::after {
  content: "\e1d1\e1d1"; }

.fasds.fa-indian-rupee-sign::after, .fa-sharp-duotone.fa-indian-rupee-sign::after {
  content: "\e1bc\e1bc"; }

.fasds.fa-indian-rupee::after, .fa-sharp-duotone.fa-indian-rupee::after {
  content: "\e1bc\e1bc"; }

.fasds.fa-inr::after, .fa-sharp-duotone.fa-inr::after {
  content: "\e1bc\e1bc"; }

.fasds.fa-crop-simple::after, .fa-sharp-duotone.fa-crop-simple::after {
  content: "\f565\f565"; }

.fasds.fa-crop-alt::after, .fa-sharp-duotone.fa-crop-alt::after {
  content: "\f565\f565"; }

.fasds.fa-money-bill-1::after, .fa-sharp-duotone.fa-money-bill-1::after {
  content: "\f3d1\f3d1"; }

.fasds.fa-money-bill-alt::after, .fa-sharp-duotone.fa-money-bill-alt::after {
  content: "\f3d1\f3d1"; }

.fasds.fa-left-long::after, .fa-sharp-duotone.fa-left-long::after {
  content: "\f30a\f30a"; }

.fasds.fa-long-arrow-alt-left::after, .fa-sharp-duotone.fa-long-arrow-alt-left::after {
  content: "\f30a\f30a"; }

.fasds.fa-keyboard-down::after, .fa-sharp-duotone.fa-keyboard-down::after {
  content: "\e1c2\e1c2"; }

.fasds.fa-circle-up-right::after, .fa-sharp-duotone.fa-circle-up-right::after {
  content: "\e129\e129"; }

.fasds.fa-cloud-bolt-moon::after, .fa-sharp-duotone.fa-cloud-bolt-moon::after {
  content: "\f76d\f76d"; }

.fasds.fa-thunderstorm-moon::after, .fa-sharp-duotone.fa-thunderstorm-moon::after {
  content: "\f76d\f76d"; }

.fasds.fa-turn-left-up::after, .fa-sharp-duotone.fa-turn-left-up::after {
  content: "\e638\e638"; }

.fasds.fa-dna::after, .fa-sharp-duotone.fa-dna::after {
  content: "\f471\f471"; }

.fasds.fa-virus-slash::after, .fa-sharp-duotone.fa-virus-slash::after {
  content: "\e075\e075"; }

.fasds.fa-bracket-round-right::after, .fa-sharp-duotone.fa-bracket-round-right::after {
  content: "\29\29"; }

.fasds.fa-circle-sterling::after, .fa-sharp-duotone.fa-circle-sterling::after {
  content: "\e5cf\e5cf"; }

.fasds.fa-circle-5::after, .fa-sharp-duotone.fa-circle-5::after {
  content: "\e0f2\e0f2"; }

.fasds.fa-minus::after, .fa-sharp-duotone.fa-minus::after {
  content: "\f068\f068"; }

.fasds.fa-subtract::after, .fa-sharp-duotone.fa-subtract::after {
  content: "\f068\f068"; }

.fasds.fa-fire-flame::after, .fa-sharp-duotone.fa-fire-flame::after {
  content: "\f6df\f6df"; }

.fasds.fa-flame::after, .fa-sharp-duotone.fa-flame::after {
  content: "\f6df\f6df"; }

.fasds.fa-right-to-line::after, .fa-sharp-duotone.fa-right-to-line::after {
  content: "\f34c\f34c"; }

.fasds.fa-arrow-alt-to-right::after, .fa-sharp-duotone.fa-arrow-alt-to-right::after {
  content: "\f34c\f34c"; }

.fasds.fa-gif::after, .fa-sharp-duotone.fa-gif::after {
  content: "\e190\e190"; }

.fasds.fa-chess::after, .fa-sharp-duotone.fa-chess::after {
  content: "\f439\f439"; }

.fasds.fa-trash-slash::after, .fa-sharp-duotone.fa-trash-slash::after {
  content: "\e2b3\e2b3"; }

.fasds.fa-arrow-left-long::after, .fa-sharp-duotone.fa-arrow-left-long::after {
  content: "\f177\f177"; }

.fasds.fa-long-arrow-left::after, .fa-sharp-duotone.fa-long-arrow-left::after {
  content: "\f177\f177"; }

.fasds.fa-plug-circle-check::after, .fa-sharp-duotone.fa-plug-circle-check::after {
  content: "\e55c\e55c"; }

.fasds.fa-font-case::after, .fa-sharp-duotone.fa-font-case::after {
  content: "\f866\f866"; }

.fasds.fa-street-view::after, .fa-sharp-duotone.fa-street-view::after {
  content: "\f21d\f21d"; }

.fasds.fa-arrow-down-left::after, .fa-sharp-duotone.fa-arrow-down-left::after {
  content: "\e091\e091"; }

.fasds.fa-franc-sign::after, .fa-sharp-duotone.fa-franc-sign::after {
  content: "\e18f\e18f"; }

.fasds.fa-flask-round-poison::after, .fa-sharp-duotone.fa-flask-round-poison::after {
  content: "\f6e0\f6e0"; }

.fasds.fa-flask-poison::after, .fa-sharp-duotone.fa-flask-poison::after {
  content: "\f6e0\f6e0"; }

.fasds.fa-volume-off::after, .fa-sharp-duotone.fa-volume-off::after {
  content: "\f026\f026"; }

.fasds.fa-book-circle-arrow-right::after, .fa-sharp-duotone.fa-book-circle-arrow-right::after {
  content: "\e0bc\e0bc"; }

.fasds.fa-chart-user::after, .fa-sharp-duotone.fa-chart-user::after {
  content: "\f6a3\f6a3"; }

.fasds.fa-user-chart::after, .fa-sharp-duotone.fa-user-chart::after {
  content: "\f6a3\f6a3"; }

.fasds.fa-hands-asl-interpreting::after, .fa-sharp-duotone.fa-hands-asl-interpreting::after {
  content: "\f2a3\f2a3"; }

.fasds.fa-american-sign-language-interpreting::after, .fa-sharp-duotone.fa-american-sign-language-interpreting::after {
  content: "\f2a3\f2a3"; }

.fasds.fa-asl-interpreting::after, .fa-sharp-duotone.fa-asl-interpreting::after {
  content: "\f2a3\f2a3"; }

.fasds.fa-hands-american-sign-language-interpreting::after, .fa-sharp-duotone.fa-hands-american-sign-language-interpreting::after {
  content: "\f2a3\f2a3"; }

.fasds.fa-presentation-screen::after, .fa-sharp-duotone.fa-presentation-screen::after {
  content: "\f685\f685"; }

.fasds.fa-presentation::after, .fa-sharp-duotone.fa-presentation::after {
  content: "\f685\f685"; }

.fasds.fa-circle-bolt::after, .fa-sharp-duotone.fa-circle-bolt::after {
  content: "\e0fe\e0fe"; }

.fasds.fa-face-smile-halo::after, .fa-sharp-duotone.fa-face-smile-halo::after {
  content: "\e38f\e38f"; }

.fasds.fa-cart-circle-arrow-down::after, .fa-sharp-duotone.fa-cart-circle-arrow-down::after {
  content: "\e3ef\e3ef"; }

.fasds.fa-house-person-return::after, .fa-sharp-duotone.fa-house-person-return::after {
  content: "\e011\e011"; }

.fasds.fa-house-person-arrive::after, .fa-sharp-duotone.fa-house-person-arrive::after {
  content: "\e011\e011"; }

.fasds.fa-house-return::after, .fa-sharp-duotone.fa-house-return::after {
  content: "\e011\e011"; }

.fasds.fa-message-xmark::after, .fa-sharp-duotone.fa-message-xmark::after {
  content: "\f4ab\f4ab"; }

.fasds.fa-comment-alt-times::after, .fa-sharp-duotone.fa-comment-alt-times::after {
  content: "\f4ab\f4ab"; }

.fasds.fa-message-times::after, .fa-sharp-duotone.fa-message-times::after {
  content: "\f4ab\f4ab"; }

.fasds.fa-file-certificate::after, .fa-sharp-duotone.fa-file-certificate::after {
  content: "\f5f3\f5f3"; }

.fasds.fa-file-award::after, .fa-sharp-duotone.fa-file-award::after {
  content: "\f5f3\f5f3"; }

.fasds.fa-user-doctor-hair-long::after, .fa-sharp-duotone.fa-user-doctor-hair-long::after {
  content: "\e459\e459"; }

.fasds.fa-camera-security::after, .fa-sharp-duotone.fa-camera-security::after {
  content: "\f8fe\f8fe"; }

.fasds.fa-camera-home::after, .fa-sharp-duotone.fa-camera-home::after {
  content: "\f8fe\f8fe"; }

.fasds.fa-gear::after, .fa-sharp-duotone.fa-gear::after {
  content: "\f013\f013"; }

.fasds.fa-cog::after, .fa-sharp-duotone.fa-cog::after {
  content: "\f013\f013"; }

.fasds.fa-droplet-slash::after, .fa-sharp-duotone.fa-droplet-slash::after {
  content: "\f5c7\f5c7"; }

.fasds.fa-tint-slash::after, .fa-sharp-duotone.fa-tint-slash::after {
  content: "\f5c7\f5c7"; }

.fasds.fa-book-heart::after, .fa-sharp-duotone.fa-book-heart::after {
  content: "\f499\f499"; }

.fasds.fa-mosque::after, .fa-sharp-duotone.fa-mosque::after {
  content: "\f678\f678"; }

.fasds.fa-duck::after, .fa-sharp-duotone.fa-duck::after {
  content: "\f6d8\f6d8"; }

.fasds.fa-mosquito::after, .fa-sharp-duotone.fa-mosquito::after {
  content: "\e52b\e52b"; }

.fasds.fa-star-of-david::after, .fa-sharp-duotone.fa-star-of-david::after {
  content: "\f69a\f69a"; }

.fasds.fa-flag-swallowtail::after, .fa-sharp-duotone.fa-flag-swallowtail::after {
  content: "\f74c\f74c"; }

.fasds.fa-flag-alt::after, .fa-sharp-duotone.fa-flag-alt::after {
  content: "\f74c\f74c"; }

.fasds.fa-person-military-rifle::after, .fa-sharp-duotone.fa-person-military-rifle::after {
  content: "\e54b\e54b"; }

.fasds.fa-car-garage::after, .fa-sharp-duotone.fa-car-garage::after {
  content: "\f5e2\f5e2"; }

.fasds.fa-cart-shopping::after, .fa-sharp-duotone.fa-cart-shopping::after {
  content: "\f07a\f07a"; }

.fasds.fa-shopping-cart::after, .fa-sharp-duotone.fa-shopping-cart::after {
  content: "\f07a\f07a"; }

.fasds.fa-book-font::after, .fa-sharp-duotone.fa-book-font::after {
  content: "\e0bf\e0bf"; }

.fasds.fa-shield-plus::after, .fa-sharp-duotone.fa-shield-plus::after {
  content: "\e24a\e24a"; }

.fasds.fa-vials::after, .fa-sharp-duotone.fa-vials::after {
  content: "\f493\f493"; }

.fasds.fa-eye-dropper-full::after, .fa-sharp-duotone.fa-eye-dropper-full::after {
  content: "\e172\e172"; }

.fasds.fa-distribute-spacing-horizontal::after, .fa-sharp-duotone.fa-distribute-spacing-horizontal::after {
  content: "\e365\e365"; }

.fasds.fa-tablet-rugged::after, .fa-sharp-duotone.fa-tablet-rugged::after {
  content: "\f48f\f48f"; }

.fasds.fa-temperature-snow::after, .fa-sharp-duotone.fa-temperature-snow::after {
  content: "\f768\f768"; }

.fasds.fa-temperature-frigid::after, .fa-sharp-duotone.fa-temperature-frigid::after {
  content: "\f768\f768"; }

.fasds.fa-moped::after, .fa-sharp-duotone.fa-moped::after {
  content: "\e3b9\e3b9"; }

.fasds.fa-face-smile-plus::after, .fa-sharp-duotone.fa-face-smile-plus::after {
  content: "\f5b9\f5b9"; }

.fasds.fa-smile-plus::after, .fa-sharp-duotone.fa-smile-plus::after {
  content: "\f5b9\f5b9"; }

.fasds.fa-radio-tuner::after, .fa-sharp-duotone.fa-radio-tuner::after {
  content: "\f8d8\f8d8"; }

.fasds.fa-radio-alt::after, .fa-sharp-duotone.fa-radio-alt::after {
  content: "\f8d8\f8d8"; }

.fasds.fa-face-swear::after, .fa-sharp-duotone.fa-face-swear::after {
  content: "\e399\e399"; }

.fasds.fa-water-arrow-down::after, .fa-sharp-duotone.fa-water-arrow-down::after {
  content: "\f774\f774"; }

.fasds.fa-water-lower::after, .fa-sharp-duotone.fa-water-lower::after {
  content: "\f774\f774"; }

.fasds.fa-scanner-touchscreen::after, .fa-sharp-duotone.fa-scanner-touchscreen::after {
  content: "\f48a\f48a"; }

.fasds.fa-circle-7::after, .fa-sharp-duotone.fa-circle-7::after {
  content: "\e0f4\e0f4"; }

.fasds.fa-plug-circle-plus::after, .fa-sharp-duotone.fa-plug-circle-plus::after {
  content: "\e55f\e55f"; }

.fasds.fa-person-ski-jumping::after, .fa-sharp-duotone.fa-person-ski-jumping::after {
  content: "\f7c7\f7c7"; }

.fasds.fa-ski-jump::after, .fa-sharp-duotone.fa-ski-jump::after {
  content: "\f7c7\f7c7"; }

.fasds.fa-place-of-worship::after, .fa-sharp-duotone.fa-place-of-worship::after {
  content: "\f67f\f67f"; }

.fasds.fa-water-arrow-up::after, .fa-sharp-duotone.fa-water-arrow-up::after {
  content: "\f775\f775"; }

.fasds.fa-water-rise::after, .fa-sharp-duotone.fa-water-rise::after {
  content: "\f775\f775"; }

.fasds.fa-waveform-lines::after, .fa-sharp-duotone.fa-waveform-lines::after {
  content: "\f8f2\f8f2"; }

.fasds.fa-waveform-path::after, .fa-sharp-duotone.fa-waveform-path::after {
  content: "\f8f2\f8f2"; }

.fasds.fa-split::after, .fa-sharp-duotone.fa-split::after {
  content: "\e254\e254"; }

.fasds.fa-film-canister::after, .fa-sharp-duotone.fa-film-canister::after {
  content: "\f8b7\f8b7"; }

.fasds.fa-film-cannister::after, .fa-sharp-duotone.fa-film-cannister::after {
  content: "\f8b7\f8b7"; }

.fasds.fa-folder-xmark::after, .fa-sharp-duotone.fa-folder-xmark::after {
  content: "\f65f\f65f"; }

.fasds.fa-folder-times::after, .fa-sharp-duotone.fa-folder-times::after {
  content: "\f65f\f65f"; }

.fasds.fa-toilet-paper-blank::after, .fa-sharp-duotone.fa-toilet-paper-blank::after {
  content: "\f71f\f71f"; }

.fasds.fa-toilet-paper-alt::after, .fa-sharp-duotone.fa-toilet-paper-alt::after {
  content: "\f71f\f71f"; }

.fasds.fa-tablet-screen::after, .fa-sharp-duotone.fa-tablet-screen::after {
  content: "\f3fc\f3fc"; }

.fasds.fa-tablet-android-alt::after, .fa-sharp-duotone.fa-tablet-android-alt::after {
  content: "\f3fc\f3fc"; }

.fasds.fa-hexagon-vertical-nft-slanted::after, .fa-sharp-duotone.fa-hexagon-vertical-nft-slanted::after {
  content: "\e506\e506"; }

.fasds.fa-folder-music::after, .fa-sharp-duotone.fa-folder-music::after {
  content: "\e18d\e18d"; }

.fasds.fa-display-medical::after, .fa-sharp-duotone.fa-display-medical::after {
  content: "\e166\e166"; }

.fasds.fa-desktop-medical::after, .fa-sharp-duotone.fa-desktop-medical::after {
  content: "\e166\e166"; }

.fasds.fa-share-all::after, .fa-sharp-duotone.fa-share-all::after {
  content: "\f367\f367"; }

.fasds.fa-peapod::after, .fa-sharp-duotone.fa-peapod::after {
  content: "\e31c\e31c"; }

.fasds.fa-chess-clock::after, .fa-sharp-duotone.fa-chess-clock::after {
  content: "\f43d\f43d"; }

.fasds.fa-axe::after, .fa-sharp-duotone.fa-axe::after {
  content: "\f6b2\f6b2"; }

.fasds.fa-square-d::after, .fa-sharp-duotone.fa-square-d::after {
  content: "\e268\e268"; }

.fasds.fa-grip-vertical::after, .fa-sharp-duotone.fa-grip-vertical::after {
  content: "\f58e\f58e"; }

.fasds.fa-mobile-signal-out::after, .fa-sharp-duotone.fa-mobile-signal-out::after {
  content: "\e1f0\e1f0"; }

.fasds.fa-arrow-turn-up::after, .fa-sharp-duotone.fa-arrow-turn-up::after {
  content: "\f148\f148"; }

.fasds.fa-level-up::after, .fa-sharp-duotone.fa-level-up::after {
  content: "\f148\f148"; }

.fasds.fa-u::after, .fa-sharp-duotone.fa-u::after {
  content: "\55\55"; }

.fasds.fa-arrow-up-from-dotted-line::after, .fa-sharp-duotone.fa-arrow-up-from-dotted-line::after {
  content: "\e09b\e09b"; }

.fasds.fa-square-root-variable::after, .fa-sharp-duotone.fa-square-root-variable::after {
  content: "\f698\f698"; }

.fasds.fa-square-root-alt::after, .fa-sharp-duotone.fa-square-root-alt::after {
  content: "\f698\f698"; }

.fasds.fa-light-switch-on::after, .fa-sharp-duotone.fa-light-switch-on::after {
  content: "\e019\e019"; }

.fasds.fa-arrow-down-arrow-up::after, .fa-sharp-duotone.fa-arrow-down-arrow-up::after {
  content: "\f883\f883"; }

.fasds.fa-sort-alt::after, .fa-sharp-duotone.fa-sort-alt::after {
  content: "\f883\f883"; }

.fasds.fa-raindrops::after, .fa-sharp-duotone.fa-raindrops::after {
  content: "\f75c\f75c"; }

.fasds.fa-dash::after, .fa-sharp-duotone.fa-dash::after {
  content: "\e404\e404"; }

.fasds.fa-minus-large::after, .fa-sharp-duotone.fa-minus-large::after {
  content: "\e404\e404"; }

.fasds.fa-clock::after, .fa-sharp-duotone.fa-clock::after {
  content: "\f017\f017"; }

.fasds.fa-clock-four::after, .fa-sharp-duotone.fa-clock-four::after {
  content: "\f017\f017"; }

.fasds.fa-input-numeric::after, .fa-sharp-duotone.fa-input-numeric::after {
  content: "\e1bd\e1bd"; }

.fasds.fa-truck-tow::after, .fa-sharp-duotone.fa-truck-tow::after {
  content: "\e2b8\e2b8"; }

.fasds.fa-backward-step::after, .fa-sharp-duotone.fa-backward-step::after {
  content: "\f048\f048"; }

.fasds.fa-step-backward::after, .fa-sharp-duotone.fa-step-backward::after {
  content: "\f048\f048"; }

.fasds.fa-pallet::after, .fa-sharp-duotone.fa-pallet::after {
  content: "\f482\f482"; }

.fasds.fa-car-bolt::after, .fa-sharp-duotone.fa-car-bolt::after {
  content: "\e341\e341"; }

.fasds.fa-arrows-maximize::after, .fa-sharp-duotone.fa-arrows-maximize::after {
  content: "\f31d\f31d"; }

.fasds.fa-expand-arrows::after, .fa-sharp-duotone.fa-expand-arrows::after {
  content: "\f31d\f31d"; }

.fasds.fa-faucet::after, .fa-sharp-duotone.fa-faucet::after {
  content: "\e005\e005"; }

.fasds.fa-cloud-sleet::after, .fa-sharp-duotone.fa-cloud-sleet::after {
  content: "\f741\f741"; }

.fasds.fa-lamp-street::after, .fa-sharp-duotone.fa-lamp-street::after {
  content: "\e1c5\e1c5"; }

.fasds.fa-list-radio::after, .fa-sharp-duotone.fa-list-radio::after {
  content: "\e1d0\e1d0"; }

.fasds.fa-pen-nib-slash::after, .fa-sharp-duotone.fa-pen-nib-slash::after {
  content: "\e4a1\e4a1"; }

.fasds.fa-baseball-bat-ball::after, .fa-sharp-duotone.fa-baseball-bat-ball::after {
  content: "\f432\f432"; }

.fasds.fa-square-up-left::after, .fa-sharp-duotone.fa-square-up-left::after {
  content: "\e282\e282"; }

.fasds.fa-overline::after, .fa-sharp-duotone.fa-overline::after {
  content: "\f876\f876"; }

.fasds.fa-s::after, .fa-sharp-duotone.fa-s::after {
  content: "\53\53"; }

.fasds.fa-timeline::after, .fa-sharp-duotone.fa-timeline::after {
  content: "\e29c\e29c"; }

.fasds.fa-keyboard::after, .fa-sharp-duotone.fa-keyboard::after {
  content: "\f11c\f11c"; }

.fasds.fa-arrows-from-dotted-line::after, .fa-sharp-duotone.fa-arrows-from-dotted-line::after {
  content: "\e0a3\e0a3"; }

.fasds.fa-usb-drive::after, .fa-sharp-duotone.fa-usb-drive::after {
  content: "\f8e9\f8e9"; }

.fasds.fa-ballot::after, .fa-sharp-duotone.fa-ballot::after {
  content: "\f732\f732"; }

.fasds.fa-caret-down::after, .fa-sharp-duotone.fa-caret-down::after {
  content: "\f0d7\f0d7"; }

.fasds.fa-location-dot-slash::after, .fa-sharp-duotone.fa-location-dot-slash::after {
  content: "\f605\f605"; }

.fasds.fa-map-marker-alt-slash::after, .fa-sharp-duotone.fa-map-marker-alt-slash::after {
  content: "\f605\f605"; }

.fasds.fa-cards::after, .fa-sharp-duotone.fa-cards::after {
  content: "\e3ed\e3ed"; }

.fasds.fa-house-chimney-medical::after, .fa-sharp-duotone.fa-house-chimney-medical::after {
  content: "\f7f2\f7f2"; }

.fasds.fa-clinic-medical::after, .fa-sharp-duotone.fa-clinic-medical::after {
  content: "\f7f2\f7f2"; }

.fasds.fa-boxing-glove::after, .fa-sharp-duotone.fa-boxing-glove::after {
  content: "\f438\f438"; }

.fasds.fa-glove-boxing::after, .fa-sharp-duotone.fa-glove-boxing::after {
  content: "\f438\f438"; }

.fasds.fa-temperature-three-quarters::after, .fa-sharp-duotone.fa-temperature-three-quarters::after {
  content: "\f2c8\f2c8"; }

.fasds.fa-temperature-3::after, .fa-sharp-duotone.fa-temperature-3::after {
  content: "\f2c8\f2c8"; }

.fasds.fa-thermometer-3::after, .fa-sharp-duotone.fa-thermometer-3::after {
  content: "\f2c8\f2c8"; }

.fasds.fa-thermometer-three-quarters::after, .fa-sharp-duotone.fa-thermometer-three-quarters::after {
  content: "\f2c8\f2c8"; }

.fasds.fa-bell-school::after, .fa-sharp-duotone.fa-bell-school::after {
  content: "\f5d5\f5d5"; }

.fasds.fa-mobile-screen::after, .fa-sharp-duotone.fa-mobile-screen::after {
  content: "\f3cf\f3cf"; }

.fasds.fa-mobile-android-alt::after, .fa-sharp-duotone.fa-mobile-android-alt::after {
  content: "\f3cf\f3cf"; }

.fasds.fa-plane-up::after, .fa-sharp-duotone.fa-plane-up::after {
  content: "\e22d\e22d"; }

.fasds.fa-folder-heart::after, .fa-sharp-duotone.fa-folder-heart::after {
  content: "\e189\e189"; }

.fasds.fa-circle-location-arrow::after, .fa-sharp-duotone.fa-circle-location-arrow::after {
  content: "\f602\f602"; }

.fasds.fa-location-circle::after, .fa-sharp-duotone.fa-location-circle::after {
  content: "\f602\f602"; }

.fasds.fa-face-head-bandage::after, .fa-sharp-duotone.fa-face-head-bandage::after {
  content: "\e37a\e37a"; }

.fasds.fa-sushi-roll::after, .fa-sharp-duotone.fa-sushi-roll::after {
  content: "\e48b\e48b"; }

.fasds.fa-maki-roll::after, .fa-sharp-duotone.fa-maki-roll::after {
  content: "\e48b\e48b"; }

.fasds.fa-makizushi::after, .fa-sharp-duotone.fa-makizushi::after {
  content: "\e48b\e48b"; }

.fasds.fa-car-bump::after, .fa-sharp-duotone.fa-car-bump::after {
  content: "\f5e0\f5e0"; }

.fasds.fa-piggy-bank::after, .fa-sharp-duotone.fa-piggy-bank::after {
  content: "\f4d3\f4d3"; }

.fasds.fa-racquet::after, .fa-sharp-duotone.fa-racquet::after {
  content: "\f45a\f45a"; }

.fasds.fa-car-mirrors::after, .fa-sharp-duotone.fa-car-mirrors::after {
  content: "\e343\e343"; }

.fasds.fa-industry-windows::after, .fa-sharp-duotone.fa-industry-windows::after {
  content: "\f3b3\f3b3"; }

.fasds.fa-industry-alt::after, .fa-sharp-duotone.fa-industry-alt::after {
  content: "\f3b3\f3b3"; }

.fasds.fa-bolt-auto::after, .fa-sharp-duotone.fa-bolt-auto::after {
  content: "\e0b6\e0b6"; }

.fasds.fa-battery-half::after, .fa-sharp-duotone.fa-battery-half::after {
  content: "\f242\f242"; }

.fasds.fa-battery-3::after, .fa-sharp-duotone.fa-battery-3::after {
  content: "\f242\f242"; }

.fasds.fa-flux-capacitor::after, .fa-sharp-duotone.fa-flux-capacitor::after {
  content: "\f8ba\f8ba"; }

.fasds.fa-mountain-city::after, .fa-sharp-duotone.fa-mountain-city::after {
  content: "\e52e\e52e"; }

.fasds.fa-coins::after, .fa-sharp-duotone.fa-coins::after {
  content: "\f51e\f51e"; }

.fasds.fa-honey-pot::after, .fa-sharp-duotone.fa-honey-pot::after {
  content: "\e418\e418"; }

.fasds.fa-olive::after, .fa-sharp-duotone.fa-olive::after {
  content: "\e316\e316"; }

.fasds.fa-khanda::after, .fa-sharp-duotone.fa-khanda::after {
  content: "\f66d\f66d"; }

.fasds.fa-filter-list::after, .fa-sharp-duotone.fa-filter-list::after {
  content: "\e17c\e17c"; }

.fasds.fa-outlet::after, .fa-sharp-duotone.fa-outlet::after {
  content: "\e01c\e01c"; }

.fasds.fa-sliders::after, .fa-sharp-duotone.fa-sliders::after {
  content: "\f1de\f1de"; }

.fasds.fa-sliders-h::after, .fa-sharp-duotone.fa-sliders-h::after {
  content: "\f1de\f1de"; }

.fasds.fa-cauldron::after, .fa-sharp-duotone.fa-cauldron::after {
  content: "\f6bf\f6bf"; }

.fasds.fa-people::after, .fa-sharp-duotone.fa-people::after {
  content: "\e216\e216"; }

.fasds.fa-folder-tree::after, .fa-sharp-duotone.fa-folder-tree::after {
  content: "\f802\f802"; }

.fasds.fa-network-wired::after, .fa-sharp-duotone.fa-network-wired::after {
  content: "\f6ff\f6ff"; }

.fasds.fa-croissant::after, .fa-sharp-duotone.fa-croissant::after {
  content: "\f7f6\f7f6"; }

.fasds.fa-map-pin::after, .fa-sharp-duotone.fa-map-pin::after {
  content: "\f276\f276"; }

.fasds.fa-hamsa::after, .fa-sharp-duotone.fa-hamsa::after {
  content: "\f665\f665"; }

.fasds.fa-cent-sign::after, .fa-sharp-duotone.fa-cent-sign::after {
  content: "\e3f5\e3f5"; }

.fasds.fa-swords-laser::after, .fa-sharp-duotone.fa-swords-laser::after {
  content: "\e03d\e03d"; }

.fasds.fa-flask::after, .fa-sharp-duotone.fa-flask::after {
  content: "\f0c3\f0c3"; }

.fasds.fa-person-pregnant::after, .fa-sharp-duotone.fa-person-pregnant::after {
  content: "\e31e\e31e"; }

.fasds.fa-square-u::after, .fa-sharp-duotone.fa-square-u::after {
  content: "\e281\e281"; }

.fasds.fa-wand-sparkles::after, .fa-sharp-duotone.fa-wand-sparkles::after {
  content: "\f72b\f72b"; }

.fasds.fa-router::after, .fa-sharp-duotone.fa-router::after {
  content: "\f8da\f8da"; }

.fasds.fa-ellipsis-vertical::after, .fa-sharp-duotone.fa-ellipsis-vertical::after {
  content: "\f142\f142"; }

.fasds.fa-ellipsis-v::after, .fa-sharp-duotone.fa-ellipsis-v::after {
  content: "\f142\f142"; }

.fasds.fa-sword-laser-alt::after, .fa-sharp-duotone.fa-sword-laser-alt::after {
  content: "\e03c\e03c"; }

.fasds.fa-ticket::after, .fa-sharp-duotone.fa-ticket::after {
  content: "\f145\f145"; }

.fasds.fa-power-off::after, .fa-sharp-duotone.fa-power-off::after {
  content: "\f011\f011"; }

.fasds.fa-coin::after, .fa-sharp-duotone.fa-coin::after {
  content: "\f85c\f85c"; }

.fasds.fa-laptop-slash::after, .fa-sharp-duotone.fa-laptop-slash::after {
  content: "\e1c7\e1c7"; }

.fasds.fa-right-long::after, .fa-sharp-duotone.fa-right-long::after {
  content: "\f30b\f30b"; }

.fasds.fa-long-arrow-alt-right::after, .fa-sharp-duotone.fa-long-arrow-alt-right::after {
  content: "\f30b\f30b"; }

.fasds.fa-circle-b::after, .fa-sharp-duotone.fa-circle-b::after {
  content: "\e0fd\e0fd"; }

.fasds.fa-person-dress-simple::after, .fa-sharp-duotone.fa-person-dress-simple::after {
  content: "\e21c\e21c"; }

.fasds.fa-pipe-collar::after, .fa-sharp-duotone.fa-pipe-collar::after {
  content: "\e437\e437"; }

.fasds.fa-lights-holiday::after, .fa-sharp-duotone.fa-lights-holiday::after {
  content: "\f7b2\f7b2"; }

.fasds.fa-citrus::after, .fa-sharp-duotone.fa-citrus::after {
  content: "\e2f4\e2f4"; }

.fasds.fa-flag-usa::after, .fa-sharp-duotone.fa-flag-usa::after {
  content: "\f74d\f74d"; }

.fasds.fa-laptop-file::after, .fa-sharp-duotone.fa-laptop-file::after {
  content: "\e51d\e51d"; }

.fasds.fa-tty::after, .fa-sharp-duotone.fa-tty::after {
  content: "\f1e4\f1e4"; }

.fasds.fa-teletype::after, .fa-sharp-duotone.fa-teletype::after {
  content: "\f1e4\f1e4"; }

.fasds.fa-chart-tree-map::after, .fa-sharp-duotone.fa-chart-tree-map::after {
  content: "\e0ea\e0ea"; }

.fasds.fa-diagram-next::after, .fa-sharp-duotone.fa-diagram-next::after {
  content: "\e476\e476"; }

.fasds.fa-person-rifle::after, .fa-sharp-duotone.fa-person-rifle::after {
  content: "\e54e\e54e"; }

.fasds.fa-clock-five-thirty::after, .fa-sharp-duotone.fa-clock-five-thirty::after {
  content: "\e34a\e34a"; }

.fasds.fa-pipe-valve::after, .fa-sharp-duotone.fa-pipe-valve::after {
  content: "\e439\e439"; }

.fasds.fa-lightbulb-message::after, .fa-sharp-duotone.fa-lightbulb-message::after {
  content: "\e687\e687"; }

.fasds.fa-arrow-up-from-arc::after, .fa-sharp-duotone.fa-arrow-up-from-arc::after {
  content: "\e4b4\e4b4"; }

.fasds.fa-face-spiral-eyes::after, .fa-sharp-duotone.fa-face-spiral-eyes::after {
  content: "\e485\e485"; }

.fasds.fa-compress-wide::after, .fa-sharp-duotone.fa-compress-wide::after {
  content: "\f326\f326"; }

.fasds.fa-circle-phone-hangup::after, .fa-sharp-duotone.fa-circle-phone-hangup::after {
  content: "\e11d\e11d"; }

.fasds.fa-phone-circle-down::after, .fa-sharp-duotone.fa-phone-circle-down::after {
  content: "\e11d\e11d"; }

.fasds.fa-gear-complex-code::after, .fa-sharp-duotone.fa-gear-complex-code::after {
  content: "\e5eb\e5eb"; }

.fasds.fa-house-medical-circle-exclamation::after, .fa-sharp-duotone.fa-house-medical-circle-exclamation::after {
  content: "\e512\e512"; }

.fasds.fa-badminton::after, .fa-sharp-duotone.fa-badminton::after {
  content: "\e33a\e33a"; }

.fasds.fa-closed-captioning::after, .fa-sharp-duotone.fa-closed-captioning::after {
  content: "\f20a\f20a"; }

.fasds.fa-person-hiking::after, .fa-sharp-duotone.fa-person-hiking::after {
  content: "\f6ec\f6ec"; }

.fasds.fa-hiking::after, .fa-sharp-duotone.fa-hiking::after {
  content: "\f6ec\f6ec"; }

.fasds.fa-right-from-line::after, .fa-sharp-duotone.fa-right-from-line::after {
  content: "\f347\f347"; }

.fasds.fa-arrow-alt-from-left::after, .fa-sharp-duotone.fa-arrow-alt-from-left::after {
  content: "\f347\f347"; }

.fasds.fa-venus-double::after, .fa-sharp-duotone.fa-venus-double::after {
  content: "\f226\f226"; }

.fasds.fa-images::after, .fa-sharp-duotone.fa-images::after {
  content: "\f302\f302"; }

.fasds.fa-calculator::after, .fa-sharp-duotone.fa-calculator::after {
  content: "\f1ec\f1ec"; }

.fasds.fa-shuttlecock::after, .fa-sharp-duotone.fa-shuttlecock::after {
  content: "\f45b\f45b"; }

.fasds.fa-user-hair::after, .fa-sharp-duotone.fa-user-hair::after {
  content: "\e45a\e45a"; }

.fasds.fa-eye-evil::after, .fa-sharp-duotone.fa-eye-evil::after {
  content: "\f6db\f6db"; }

.fasds.fa-people-pulling::after, .fa-sharp-duotone.fa-people-pulling::after {
  content: "\e535\e535"; }

.fasds.fa-n::after, .fa-sharp-duotone.fa-n::after {
  content: "\4e\4e"; }

.fasds.fa-swap::after, .fa-sharp-duotone.fa-swap::after {
  content: "\e609\e609"; }

.fasds.fa-garage::after, .fa-sharp-duotone.fa-garage::after {
  content: "\e009\e009"; }

.fasds.fa-cable-car::after, .fa-sharp-duotone.fa-cable-car::after {
  content: "\f7da\f7da"; }

.fasds.fa-tram::after, .fa-sharp-duotone.fa-tram::after {
  content: "\f7da\f7da"; }

.fasds.fa-shovel-snow::after, .fa-sharp-duotone.fa-shovel-snow::after {
  content: "\f7c3\f7c3"; }

.fasds.fa-cloud-rain::after, .fa-sharp-duotone.fa-cloud-rain::after {
  content: "\f73d\f73d"; }

.fasds.fa-face-lying::after, .fa-sharp-duotone.fa-face-lying::after {
  content: "\e37e\e37e"; }

.fasds.fa-sprinkler::after, .fa-sharp-duotone.fa-sprinkler::after {
  content: "\e035\e035"; }

.fasds.fa-building-circle-xmark::after, .fa-sharp-duotone.fa-building-circle-xmark::after {
  content: "\e4d4\e4d4"; }

.fasds.fa-person-sledding::after, .fa-sharp-duotone.fa-person-sledding::after {
  content: "\f7cb\f7cb"; }

.fasds.fa-sledding::after, .fa-sharp-duotone.fa-sledding::after {
  content: "\f7cb\f7cb"; }

.fasds.fa-game-console-handheld::after, .fa-sharp-duotone.fa-game-console-handheld::after {
  content: "\f8bb\f8bb"; }

.fasds.fa-ship::after, .fa-sharp-duotone.fa-ship::after {
  content: "\f21a\f21a"; }

.fasds.fa-clock-six-thirty::after, .fa-sharp-duotone.fa-clock-six-thirty::after {
  content: "\e353\e353"; }

.fasds.fa-battery-slash::after, .fa-sharp-duotone.fa-battery-slash::after {
  content: "\f377\f377"; }

.fasds.fa-tugrik-sign::after, .fa-sharp-duotone.fa-tugrik-sign::after {
  content: "\e2ba\e2ba"; }

.fasds.fa-arrows-down-to-line::after, .fa-sharp-duotone.fa-arrows-down-to-line::after {
  content: "\e4b8\e4b8"; }

.fasds.fa-download::after, .fa-sharp-duotone.fa-download::after {
  content: "\f019\f019"; }

.fasds.fa-angles-up-down::after, .fa-sharp-duotone.fa-angles-up-down::after {
  content: "\e60d\e60d"; }

.fasds.fa-shelves::after, .fa-sharp-duotone.fa-shelves::after {
  content: "\f480\f480"; }

.fasds.fa-inventory::after, .fa-sharp-duotone.fa-inventory::after {
  content: "\f480\f480"; }

.fasds.fa-cloud-snow::after, .fa-sharp-duotone.fa-cloud-snow::after {
  content: "\f742\f742"; }

.fasds.fa-face-grin::after, .fa-sharp-duotone.fa-face-grin::after {
  content: "\f580\f580"; }

.fasds.fa-grin::after, .fa-sharp-duotone.fa-grin::after {
  content: "\f580\f580"; }

.fasds.fa-delete-left::after, .fa-sharp-duotone.fa-delete-left::after {
  content: "\f55a\f55a"; }

.fasds.fa-backspace::after, .fa-sharp-duotone.fa-backspace::after {
  content: "\f55a\f55a"; }

.fasds.fa-oven::after, .fa-sharp-duotone.fa-oven::after {
  content: "\e01d\e01d"; }

.fasds.fa-cloud-binary::after, .fa-sharp-duotone.fa-cloud-binary::after {
  content: "\e601\e601"; }

.fasds.fa-eye-dropper::after, .fa-sharp-duotone.fa-eye-dropper::after {
  content: "\f1fb\f1fb"; }

.fasds.fa-eye-dropper-empty::after, .fa-sharp-duotone.fa-eye-dropper-empty::after {
  content: "\f1fb\f1fb"; }

.fasds.fa-eyedropper::after, .fa-sharp-duotone.fa-eyedropper::after {
  content: "\f1fb\f1fb"; }

.fasds.fa-comment-captions::after, .fa-sharp-duotone.fa-comment-captions::after {
  content: "\e146\e146"; }

.fasds.fa-comments-question::after, .fa-sharp-duotone.fa-comments-question::after {
  content: "\e14e\e14e"; }

.fasds.fa-scribble::after, .fa-sharp-duotone.fa-scribble::after {
  content: "\e23f\e23f"; }

.fasds.fa-rotate-exclamation::after, .fa-sharp-duotone.fa-rotate-exclamation::after {
  content: "\e23c\e23c"; }

.fasds.fa-file-circle-check::after, .fa-sharp-duotone.fa-file-circle-check::after {
  content: "\e5a0\e5a0"; }

.fasds.fa-glass::after, .fa-sharp-duotone.fa-glass::after {
  content: "\f804\f804"; }

.fasds.fa-loader::after, .fa-sharp-duotone.fa-loader::after {
  content: "\e1d4\e1d4"; }

.fasds.fa-forward::after, .fa-sharp-duotone.fa-forward::after {
  content: "\f04e\f04e"; }

.fasds.fa-user-pilot::after, .fa-sharp-duotone.fa-user-pilot::after {
  content: "\e2c0\e2c0"; }

.fasds.fa-mobile::after, .fa-sharp-duotone.fa-mobile::after {
  content: "\f3ce\f3ce"; }

.fasds.fa-mobile-android::after, .fa-sharp-duotone.fa-mobile-android::after {
  content: "\f3ce\f3ce"; }

.fasds.fa-mobile-phone::after, .fa-sharp-duotone.fa-mobile-phone::after {
  content: "\f3ce\f3ce"; }

.fasds.fa-code-pull-request-closed::after, .fa-sharp-duotone.fa-code-pull-request-closed::after {
  content: "\e3f9\e3f9"; }

.fasds.fa-face-meh::after, .fa-sharp-duotone.fa-face-meh::after {
  content: "\f11a\f11a"; }

.fasds.fa-meh::after, .fa-sharp-duotone.fa-meh::after {
  content: "\f11a\f11a"; }

.fasds.fa-align-center::after, .fa-sharp-duotone.fa-align-center::after {
  content: "\f037\f037"; }

.fasds.fa-book-skull::after, .fa-sharp-duotone.fa-book-skull::after {
  content: "\f6b7\f6b7"; }

.fasds.fa-book-dead::after, .fa-sharp-duotone.fa-book-dead::after {
  content: "\f6b7\f6b7"; }

.fasds.fa-id-card::after, .fa-sharp-duotone.fa-id-card::after {
  content: "\f2c2\f2c2"; }

.fasds.fa-drivers-license::after, .fa-sharp-duotone.fa-drivers-license::after {
  content: "\f2c2\f2c2"; }

.fasds.fa-face-dotted::after, .fa-sharp-duotone.fa-face-dotted::after {
  content: "\e47f\e47f"; }

.fasds.fa-face-worried::after, .fa-sharp-duotone.fa-face-worried::after {
  content: "\e3a3\e3a3"; }

.fasds.fa-outdent::after, .fa-sharp-duotone.fa-outdent::after {
  content: "\f03b\f03b"; }

.fasds.fa-dedent::after, .fa-sharp-duotone.fa-dedent::after {
  content: "\f03b\f03b"; }

.fasds.fa-court-sport::after, .fa-sharp-duotone.fa-court-sport::after {
  content: "\e643\e643"; }

.fasds.fa-heart-circle-exclamation::after, .fa-sharp-duotone.fa-heart-circle-exclamation::after {
  content: "\e4fe\e4fe"; }

.fasds.fa-house::after, .fa-sharp-duotone.fa-house::after {
  content: "\f015\f015"; }

.fasds.fa-home::after, .fa-sharp-duotone.fa-home::after {
  content: "\f015\f015"; }

.fasds.fa-home-alt::after, .fa-sharp-duotone.fa-home-alt::after {
  content: "\f015\f015"; }

.fasds.fa-home-lg-alt::after, .fa-sharp-duotone.fa-home-lg-alt::after {
  content: "\f015\f015"; }

.fasds.fa-vector-circle::after, .fa-sharp-duotone.fa-vector-circle::after {
  content: "\e2c6\e2c6"; }

.fasds.fa-car-circle-bolt::after, .fa-sharp-duotone.fa-car-circle-bolt::after {
  content: "\e342\e342"; }

.fasds.fa-calendar-week::after, .fa-sharp-duotone.fa-calendar-week::after {
  content: "\f784\f784"; }

.fasds.fa-flying-disc::after, .fa-sharp-duotone.fa-flying-disc::after {
  content: "\e3a9\e3a9"; }

.fasds.fa-laptop-medical::after, .fa-sharp-duotone.fa-laptop-medical::after {
  content: "\f812\f812"; }

.fasds.fa-square-down-right::after, .fa-sharp-duotone.fa-square-down-right::after {
  content: "\e26c\e26c"; }

.fasds.fa-b::after, .fa-sharp-duotone.fa-b::after {
  content: "\42\42"; }

.fasds.fa-seat-airline::after, .fa-sharp-duotone.fa-seat-airline::after {
  content: "\e244\e244"; }

.fasds.fa-moon-over-sun::after, .fa-sharp-duotone.fa-moon-over-sun::after {
  content: "\f74a\f74a"; }

.fasds.fa-eclipse-alt::after, .fa-sharp-duotone.fa-eclipse-alt::after {
  content: "\f74a\f74a"; }

.fasds.fa-pipe::after, .fa-sharp-duotone.fa-pipe::after {
  content: "\7c\7c"; }

.fasds.fa-file-medical::after, .fa-sharp-duotone.fa-file-medical::after {
  content: "\f477\f477"; }

.fasds.fa-potato::after, .fa-sharp-duotone.fa-potato::after {
  content: "\e440\e440"; }

.fasds.fa-dice-one::after, .fa-sharp-duotone.fa-dice-one::after {
  content: "\f525\f525"; }

.fasds.fa-circle-a::after, .fa-sharp-duotone.fa-circle-a::after {
  content: "\e0f7\e0f7"; }

.fasds.fa-helmet-battle::after, .fa-sharp-duotone.fa-helmet-battle::after {
  content: "\f6eb\f6eb"; }

.fasds.fa-butter::after, .fa-sharp-duotone.fa-butter::after {
  content: "\e3e4\e3e4"; }

.fasds.fa-blanket-fire::after, .fa-sharp-duotone.fa-blanket-fire::after {
  content: "\e3da\e3da"; }

.fasds.fa-kiwi-bird::after, .fa-sharp-duotone.fa-kiwi-bird::after {
  content: "\f535\f535"; }

.fasds.fa-castle::after, .fa-sharp-duotone.fa-castle::after {
  content: "\e0de\e0de"; }

.fasds.fa-golf-club::after, .fa-sharp-duotone.fa-golf-club::after {
  content: "\f451\f451"; }

.fasds.fa-arrow-right-arrow-left::after, .fa-sharp-duotone.fa-arrow-right-arrow-left::after {
  content: "\f0ec\f0ec"; }

.fasds.fa-exchange::after, .fa-sharp-duotone.fa-exchange::after {
  content: "\f0ec\f0ec"; }

.fasds.fa-rotate-right::after, .fa-sharp-duotone.fa-rotate-right::after {
  content: "\f2f9\f2f9"; }

.fasds.fa-redo-alt::after, .fa-sharp-duotone.fa-redo-alt::after {
  content: "\f2f9\f2f9"; }

.fasds.fa-rotate-forward::after, .fa-sharp-duotone.fa-rotate-forward::after {
  content: "\f2f9\f2f9"; }

.fasds.fa-utensils::after, .fa-sharp-duotone.fa-utensils::after {
  content: "\f2e7\f2e7"; }

.fasds.fa-cutlery::after, .fa-sharp-duotone.fa-cutlery::after {
  content: "\f2e7\f2e7"; }

.fasds.fa-arrow-up-wide-short::after, .fa-sharp-duotone.fa-arrow-up-wide-short::after {
  content: "\f161\f161"; }

.fasds.fa-sort-amount-up::after, .fa-sharp-duotone.fa-sort-amount-up::after {
  content: "\f161\f161"; }

.fasds.fa-chart-pie-simple-circle-dollar::after, .fa-sharp-duotone.fa-chart-pie-simple-circle-dollar::after {
  content: "\e605\e605"; }

.fasds.fa-balloons::after, .fa-sharp-duotone.fa-balloons::after {
  content: "\e2e4\e2e4"; }

.fasds.fa-mill-sign::after, .fa-sharp-duotone.fa-mill-sign::after {
  content: "\e1ed\e1ed"; }

.fasds.fa-bowl-rice::after, .fa-sharp-duotone.fa-bowl-rice::after {
  content: "\e2eb\e2eb"; }

.fasds.fa-timeline-arrow::after, .fa-sharp-duotone.fa-timeline-arrow::after {
  content: "\e29d\e29d"; }

.fasds.fa-skull::after, .fa-sharp-duotone.fa-skull::after {
  content: "\f54c\f54c"; }

.fasds.fa-game-board-simple::after, .fa-sharp-duotone.fa-game-board-simple::after {
  content: "\f868\f868"; }

.fasds.fa-game-board-alt::after, .fa-sharp-duotone.fa-game-board-alt::after {
  content: "\f868\f868"; }

.fasds.fa-circle-video::after, .fa-sharp-duotone.fa-circle-video::after {
  content: "\e12b\e12b"; }

.fasds.fa-video-circle::after, .fa-sharp-duotone.fa-video-circle::after {
  content: "\e12b\e12b"; }

.fasds.fa-chart-scatter-bubble::after, .fa-sharp-duotone.fa-chart-scatter-bubble::after {
  content: "\e0e9\e0e9"; }

.fasds.fa-house-turret::after, .fa-sharp-duotone.fa-house-turret::after {
  content: "\e1b4\e1b4"; }

.fasds.fa-banana::after, .fa-sharp-duotone.fa-banana::after {
  content: "\e2e5\e2e5"; }

.fasds.fa-hand-holding-skull::after, .fa-sharp-duotone.fa-hand-holding-skull::after {
  content: "\e1a4\e1a4"; }

.fasds.fa-people-dress::after, .fa-sharp-duotone.fa-people-dress::after {
  content: "\e217\e217"; }

.fasds.fa-loveseat::after, .fa-sharp-duotone.fa-loveseat::after {
  content: "\f4cc\f4cc"; }

.fasds.fa-couch-small::after, .fa-sharp-duotone.fa-couch-small::after {
  content: "\f4cc\f4cc"; }

.fasds.fa-tower-broadcast::after, .fa-sharp-duotone.fa-tower-broadcast::after {
  content: "\f519\f519"; }

.fasds.fa-broadcast-tower::after, .fa-sharp-duotone.fa-broadcast-tower::after {
  content: "\f519\f519"; }

.fasds.fa-truck-pickup::after, .fa-sharp-duotone.fa-truck-pickup::after {
  content: "\f63c\f63c"; }

.fasds.fa-block-quote::after, .fa-sharp-duotone.fa-block-quote::after {
  content: "\e0b5\e0b5"; }

.fasds.fa-up-long::after, .fa-sharp-duotone.fa-up-long::after {
  content: "\f30c\f30c"; }

.fasds.fa-long-arrow-alt-up::after, .fa-sharp-duotone.fa-long-arrow-alt-up::after {
  content: "\f30c\f30c"; }

.fasds.fa-stop::after, .fa-sharp-duotone.fa-stop::after {
  content: "\f04d\f04d"; }

.fasds.fa-code-merge::after, .fa-sharp-duotone.fa-code-merge::after {
  content: "\f387\f387"; }

.fasds.fa-money-check-dollar-pen::after, .fa-sharp-duotone.fa-money-check-dollar-pen::after {
  content: "\f873\f873"; }

.fasds.fa-money-check-edit-alt::after, .fa-sharp-duotone.fa-money-check-edit-alt::after {
  content: "\f873\f873"; }

.fasds.fa-up-from-line::after, .fa-sharp-duotone.fa-up-from-line::after {
  content: "\f346\f346"; }

.fasds.fa-arrow-alt-from-bottom::after, .fa-sharp-duotone.fa-arrow-alt-from-bottom::after {
  content: "\f346\f346"; }

.fasds.fa-upload::after, .fa-sharp-duotone.fa-upload::after {
  content: "\f093\f093"; }

.fasds.fa-hurricane::after, .fa-sharp-duotone.fa-hurricane::after {
  content: "\f751\f751"; }

.fasds.fa-grid-round-2-plus::after, .fa-sharp-duotone.fa-grid-round-2-plus::after {
  content: "\e5dc\e5dc"; }

.fasds.fa-people-pants::after, .fa-sharp-duotone.fa-people-pants::after {
  content: "\e219\e219"; }

.fasds.fa-mound::after, .fa-sharp-duotone.fa-mound::after {
  content: "\e52d\e52d"; }

.fasds.fa-windsock::after, .fa-sharp-duotone.fa-windsock::after {
  content: "\f777\f777"; }

.fasds.fa-circle-half::after, .fa-sharp-duotone.fa-circle-half::after {
  content: "\e110\e110"; }

.fasds.fa-brake-warning::after, .fa-sharp-duotone.fa-brake-warning::after {
  content: "\e0c7\e0c7"; }

.fasds.fa-toilet-portable::after, .fa-sharp-duotone.fa-toilet-portable::after {
  content: "\e583\e583"; }

.fasds.fa-compact-disc::after, .fa-sharp-duotone.fa-compact-disc::after {
  content: "\f51f\f51f"; }

.fasds.fa-file-arrow-down::after, .fa-sharp-duotone.fa-file-arrow-down::after {
  content: "\f56d\f56d"; }

.fasds.fa-file-download::after, .fa-sharp-duotone.fa-file-download::after {
  content: "\f56d\f56d"; }

.fasds.fa-saxophone-fire::after, .fa-sharp-duotone.fa-saxophone-fire::after {
  content: "\f8db\f8db"; }

.fasds.fa-sax-hot::after, .fa-sharp-duotone.fa-sax-hot::after {
  content: "\f8db\f8db"; }

.fasds.fa-camera-web-slash::after, .fa-sharp-duotone.fa-camera-web-slash::after {
  content: "\f833\f833"; }

.fasds.fa-webcam-slash::after, .fa-sharp-duotone.fa-webcam-slash::after {
  content: "\f833\f833"; }

.fasds.fa-folder-medical::after, .fa-sharp-duotone.fa-folder-medical::after {
  content: "\e18c\e18c"; }

.fasds.fa-folder-gear::after, .fa-sharp-duotone.fa-folder-gear::after {
  content: "\e187\e187"; }

.fasds.fa-folder-cog::after, .fa-sharp-duotone.fa-folder-cog::after {
  content: "\e187\e187"; }

.fasds.fa-hand-wave::after, .fa-sharp-duotone.fa-hand-wave::after {
  content: "\e1a7\e1a7"; }

.fasds.fa-arrow-up-arrow-down::after, .fa-sharp-duotone.fa-arrow-up-arrow-down::after {
  content: "\e099\e099"; }

.fasds.fa-sort-up-down::after, .fa-sharp-duotone.fa-sort-up-down::after {
  content: "\e099\e099"; }

.fasds.fa-caravan::after, .fa-sharp-duotone.fa-caravan::after {
  content: "\f8ff\f8ff"; }

.fasds.fa-shield-cat::after, .fa-sharp-duotone.fa-shield-cat::after {
  content: "\e572\e572"; }

.fasds.fa-message-slash::after, .fa-sharp-duotone.fa-message-slash::after {
  content: "\f4a9\f4a9"; }

.fasds.fa-comment-alt-slash::after, .fa-sharp-duotone.fa-comment-alt-slash::after {
  content: "\f4a9\f4a9"; }

.fasds.fa-bolt::after, .fa-sharp-duotone.fa-bolt::after {
  content: "\f0e7\f0e7"; }

.fasds.fa-zap::after, .fa-sharp-duotone.fa-zap::after {
  content: "\f0e7\f0e7"; }

.fasds.fa-trash-can-check::after, .fa-sharp-duotone.fa-trash-can-check::after {
  content: "\e2a9\e2a9"; }

.fasds.fa-glass-water::after, .fa-sharp-duotone.fa-glass-water::after {
  content: "\e4f4\e4f4"; }

.fasds.fa-oil-well::after, .fa-sharp-duotone.fa-oil-well::after {
  content: "\e532\e532"; }

.fasds.fa-table-cells-column-unlock::after, .fa-sharp-duotone.fa-table-cells-column-unlock::after {
  content: "\e690\e690"; }

.fasds.fa-person-simple::after, .fa-sharp-duotone.fa-person-simple::after {
  content: "\e220\e220"; }

.fasds.fa-arrow-turn-left-up::after, .fa-sharp-duotone.fa-arrow-turn-left-up::after {
  content: "\e634\e634"; }

.fasds.fa-vault::after, .fa-sharp-duotone.fa-vault::after {
  content: "\e2c5\e2c5"; }

.fasds.fa-mars::after, .fa-sharp-duotone.fa-mars::after {
  content: "\f222\f222"; }

.fasds.fa-toilet::after, .fa-sharp-duotone.fa-toilet::after {
  content: "\f7d8\f7d8"; }

.fasds.fa-plane-circle-xmark::after, .fa-sharp-duotone.fa-plane-circle-xmark::after {
  content: "\e557\e557"; }

.fasds.fa-yen-sign::after, .fa-sharp-duotone.fa-yen-sign::after {
  content: "\f157\f157"; }

.fasds.fa-cny::after, .fa-sharp-duotone.fa-cny::after {
  content: "\f157\f157"; }

.fasds.fa-jpy::after, .fa-sharp-duotone.fa-jpy::after {
  content: "\f157\f157"; }

.fasds.fa-rmb::after, .fa-sharp-duotone.fa-rmb::after {
  content: "\f157\f157"; }

.fasds.fa-yen::after, .fa-sharp-duotone.fa-yen::after {
  content: "\f157\f157"; }

.fasds.fa-gear-code::after, .fa-sharp-duotone.fa-gear-code::after {
  content: "\e5e8\e5e8"; }

.fasds.fa-notes::after, .fa-sharp-duotone.fa-notes::after {
  content: "\e202\e202"; }

.fasds.fa-ruble-sign::after, .fa-sharp-duotone.fa-ruble-sign::after {
  content: "\f158\f158"; }

.fasds.fa-rouble::after, .fa-sharp-duotone.fa-rouble::after {
  content: "\f158\f158"; }

.fasds.fa-rub::after, .fa-sharp-duotone.fa-rub::after {
  content: "\f158\f158"; }

.fasds.fa-ruble::after, .fa-sharp-duotone.fa-ruble::after {
  content: "\f158\f158"; }

.fasds.fa-trash-undo::after, .fa-sharp-duotone.fa-trash-undo::after {
  content: "\f895\f895"; }

.fasds.fa-trash-arrow-turn-left::after, .fa-sharp-duotone.fa-trash-arrow-turn-left::after {
  content: "\f895\f895"; }

.fasds.fa-champagne-glass::after, .fa-sharp-duotone.fa-champagne-glass::after {
  content: "\f79e\f79e"; }

.fasds.fa-glass-champagne::after, .fa-sharp-duotone.fa-glass-champagne::after {
  content: "\f79e\f79e"; }

.fasds.fa-objects-align-center-horizontal::after, .fa-sharp-duotone.fa-objects-align-center-horizontal::after {
  content: "\e3bc\e3bc"; }

.fasds.fa-sun::after, .fa-sharp-duotone.fa-sun::after {
  content: "\f185\f185"; }

.fasds.fa-trash-can-slash::after, .fa-sharp-duotone.fa-trash-can-slash::after {
  content: "\e2ad\e2ad"; }

.fasds.fa-trash-alt-slash::after, .fa-sharp-duotone.fa-trash-alt-slash::after {
  content: "\e2ad\e2ad"; }

.fasds.fa-screen-users::after, .fa-sharp-duotone.fa-screen-users::after {
  content: "\f63d\f63d"; }

.fasds.fa-users-class::after, .fa-sharp-duotone.fa-users-class::after {
  content: "\f63d\f63d"; }

.fasds.fa-guitar::after, .fa-sharp-duotone.fa-guitar::after {
  content: "\f7a6\f7a6"; }

.fasds.fa-square-arrow-left::after, .fa-sharp-duotone.fa-square-arrow-left::after {
  content: "\f33a\f33a"; }

.fasds.fa-arrow-square-left::after, .fa-sharp-duotone.fa-arrow-square-left::after {
  content: "\f33a\f33a"; }

.fasds.fa-square-8::after, .fa-sharp-duotone.fa-square-8::after {
  content: "\e25d\e25d"; }

.fasds.fa-face-smile-hearts::after, .fa-sharp-duotone.fa-face-smile-hearts::after {
  content: "\e390\e390"; }

.fasds.fa-brackets-square::after, .fa-sharp-duotone.fa-brackets-square::after {
  content: "\f7e9\f7e9"; }

.fasds.fa-brackets::after, .fa-sharp-duotone.fa-brackets::after {
  content: "\f7e9\f7e9"; }

.fasds.fa-laptop-arrow-down::after, .fa-sharp-duotone.fa-laptop-arrow-down::after {
  content: "\e1c6\e1c6"; }

.fasds.fa-hockey-stick-puck::after, .fa-sharp-duotone.fa-hockey-stick-puck::after {
  content: "\e3ae\e3ae"; }

.fasds.fa-house-tree::after, .fa-sharp-duotone.fa-house-tree::after {
  content: "\e1b3\e1b3"; }

.fasds.fa-signal-fair::after, .fa-sharp-duotone.fa-signal-fair::after {
  content: "\f68d\f68d"; }

.fasds.fa-signal-2::after, .fa-sharp-duotone.fa-signal-2::after {
  content: "\f68d\f68d"; }

.fasds.fa-face-laugh-wink::after, .fa-sharp-duotone.fa-face-laugh-wink::after {
  content: "\f59c\f59c"; }

.fasds.fa-laugh-wink::after, .fa-sharp-duotone.fa-laugh-wink::after {
  content: "\f59c\f59c"; }

.fasds.fa-circle-dollar::after, .fa-sharp-duotone.fa-circle-dollar::after {
  content: "\f2e8\f2e8"; }

.fasds.fa-dollar-circle::after, .fa-sharp-duotone.fa-dollar-circle::after {
  content: "\f2e8\f2e8"; }

.fasds.fa-usd-circle::after, .fa-sharp-duotone.fa-usd-circle::after {
  content: "\f2e8\f2e8"; }

.fasds.fa-horse-head::after, .fa-sharp-duotone.fa-horse-head::after {
  content: "\f7ab\f7ab"; }

.fasds.fa-arrows-repeat::after, .fa-sharp-duotone.fa-arrows-repeat::after {
  content: "\f364\f364"; }

.fasds.fa-repeat-alt::after, .fa-sharp-duotone.fa-repeat-alt::after {
  content: "\f364\f364"; }

.fasds.fa-bore-hole::after, .fa-sharp-duotone.fa-bore-hole::after {
  content: "\e4c3\e4c3"; }

.fasds.fa-industry::after, .fa-sharp-duotone.fa-industry::after {
  content: "\f275\f275"; }

.fasds.fa-image-polaroid::after, .fa-sharp-duotone.fa-image-polaroid::after {
  content: "\f8c4\f8c4"; }

.fasds.fa-wave-triangle::after, .fa-sharp-duotone.fa-wave-triangle::after {
  content: "\f89a\f89a"; }

.fasds.fa-turn-left-down::after, .fa-sharp-duotone.fa-turn-left-down::after {
  content: "\e637\e637"; }

.fasds.fa-person-running-fast::after, .fa-sharp-duotone.fa-person-running-fast::after {
  content: "\e5ff\e5ff"; }

.fasds.fa-circle-down::after, .fa-sharp-duotone.fa-circle-down::after {
  content: "\f358\f358"; }

.fasds.fa-arrow-alt-circle-down::after, .fa-sharp-duotone.fa-arrow-alt-circle-down::after {
  content: "\f358\f358"; }

.fasds.fa-grill::after, .fa-sharp-duotone.fa-grill::after {
  content: "\e5a3\e5a3"; }

.fasds.fa-arrows-turn-to-dots::after, .fa-sharp-duotone.fa-arrows-turn-to-dots::after {
  content: "\e4c1\e4c1"; }

.fasds.fa-chart-mixed::after, .fa-sharp-duotone.fa-chart-mixed::after {
  content: "\f643\f643"; }

.fasds.fa-analytics::after, .fa-sharp-duotone.fa-analytics::after {
  content: "\f643\f643"; }

.fasds.fa-florin-sign::after, .fa-sharp-duotone.fa-florin-sign::after {
  content: "\e184\e184"; }

.fasds.fa-arrow-down-short-wide::after, .fa-sharp-duotone.fa-arrow-down-short-wide::after {
  content: "\f884\f884"; }

.fasds.fa-sort-amount-desc::after, .fa-sharp-duotone.fa-sort-amount-desc::after {
  content: "\f884\f884"; }

.fasds.fa-sort-amount-down-alt::after, .fa-sharp-duotone.fa-sort-amount-down-alt::after {
  content: "\f884\f884"; }

.fasds.fa-less-than::after, .fa-sharp-duotone.fa-less-than::after {
  content: "\3c\3c"; }

.fasds.fa-display-code::after, .fa-sharp-duotone.fa-display-code::after {
  content: "\e165\e165"; }

.fasds.fa-desktop-code::after, .fa-sharp-duotone.fa-desktop-code::after {
  content: "\e165\e165"; }

.fasds.fa-face-drooling::after, .fa-sharp-duotone.fa-face-drooling::after {
  content: "\e372\e372"; }

.fasds.fa-oil-temperature::after, .fa-sharp-duotone.fa-oil-temperature::after {
  content: "\f614\f614"; }

.fasds.fa-oil-temp::after, .fa-sharp-duotone.fa-oil-temp::after {
  content: "\f614\f614"; }

.fasds.fa-square-question::after, .fa-sharp-duotone.fa-square-question::after {
  content: "\f2fd\f2fd"; }

.fasds.fa-question-square::after, .fa-sharp-duotone.fa-question-square::after {
  content: "\f2fd\f2fd"; }

.fasds.fa-air-conditioner::after, .fa-sharp-duotone.fa-air-conditioner::after {
  content: "\f8f4\f8f4"; }

.fasds.fa-angle-down::after, .fa-sharp-duotone.fa-angle-down::after {
  content: "\f107\f107"; }

.fasds.fa-mountains::after, .fa-sharp-duotone.fa-mountains::after {
  content: "\f6fd\f6fd"; }

.fasds.fa-omega::after, .fa-sharp-duotone.fa-omega::after {
  content: "\f67a\f67a"; }

.fasds.fa-car-tunnel::after, .fa-sharp-duotone.fa-car-tunnel::after {
  content: "\e4de\e4de"; }

.fasds.fa-person-dolly-empty::after, .fa-sharp-duotone.fa-person-dolly-empty::after {
  content: "\f4d1\f4d1"; }

.fasds.fa-pan-food::after, .fa-sharp-duotone.fa-pan-food::after {
  content: "\e42b\e42b"; }

.fasds.fa-head-side-cough::after, .fa-sharp-duotone.fa-head-side-cough::after {
  content: "\e061\e061"; }

.fasds.fa-grip-lines::after, .fa-sharp-duotone.fa-grip-lines::after {
  content: "\f7a4\f7a4"; }

.fasds.fa-thumbs-down::after, .fa-sharp-duotone.fa-thumbs-down::after {
  content: "\f165\f165"; }

.fasds.fa-user-lock::after, .fa-sharp-duotone.fa-user-lock::after {
  content: "\f502\f502"; }

.fasds.fa-arrow-right-long::after, .fa-sharp-duotone.fa-arrow-right-long::after {
  content: "\f178\f178"; }

.fasds.fa-long-arrow-right::after, .fa-sharp-duotone.fa-long-arrow-right::after {
  content: "\f178\f178"; }

.fasds.fa-tickets-airline::after, .fa-sharp-duotone.fa-tickets-airline::after {
  content: "\e29b\e29b"; }

.fasds.fa-tickets-perforated-plane::after, .fa-sharp-duotone.fa-tickets-perforated-plane::after {
  content: "\e29b\e29b"; }

.fasds.fa-tickets-plane::after, .fa-sharp-duotone.fa-tickets-plane::after {
  content: "\e29b\e29b"; }

.fasds.fa-tent-double-peak::after, .fa-sharp-duotone.fa-tent-double-peak::after {
  content: "\e627\e627"; }

.fasds.fa-anchor-circle-xmark::after, .fa-sharp-duotone.fa-anchor-circle-xmark::after {
  content: "\e4ac\e4ac"; }

.fasds.fa-ellipsis::after, .fa-sharp-duotone.fa-ellipsis::after {
  content: "\f141\f141"; }

.fasds.fa-ellipsis-h::after, .fa-sharp-duotone.fa-ellipsis-h::after {
  content: "\f141\f141"; }

.fasds.fa-nfc-slash::after, .fa-sharp-duotone.fa-nfc-slash::after {
  content: "\e1fc\e1fc"; }

.fasds.fa-chess-pawn::after, .fa-sharp-duotone.fa-chess-pawn::after {
  content: "\f443\f443"; }

.fasds.fa-kit-medical::after, .fa-sharp-duotone.fa-kit-medical::after {
  content: "\f479\f479"; }

.fasds.fa-first-aid::after, .fa-sharp-duotone.fa-first-aid::after {
  content: "\f479\f479"; }

.fasds.fa-grid-2-plus::after, .fa-sharp-duotone.fa-grid-2-plus::after {
  content: "\e197\e197"; }

.fasds.fa-bells::after, .fa-sharp-duotone.fa-bells::after {
  content: "\f77f\f77f"; }

.fasds.fa-person-through-window::after, .fa-sharp-duotone.fa-person-through-window::after {
  content: "\e5a9\e5a9"; }

.fasds.fa-toolbox::after, .fa-sharp-duotone.fa-toolbox::after {
  content: "\f552\f552"; }

.fasds.fa-globe-wifi::after, .fa-sharp-duotone.fa-globe-wifi::after {
  content: "\e685\e685"; }

.fasds.fa-envelope-dot::after, .fa-sharp-duotone.fa-envelope-dot::after {
  content: "\e16f\e16f"; }

.fasds.fa-envelope-badge::after, .fa-sharp-duotone.fa-envelope-badge::after {
  content: "\e16f\e16f"; }

.fasds.fa-magnifying-glass-waveform::after, .fa-sharp-duotone.fa-magnifying-glass-waveform::after {
  content: "\e661\e661"; }

.fasds.fa-hands-holding-circle::after, .fa-sharp-duotone.fa-hands-holding-circle::after {
  content: "\e4fb\e4fb"; }

.fasds.fa-bug::after, .fa-sharp-duotone.fa-bug::after {
  content: "\f188\f188"; }

.fasds.fa-bowl-chopsticks::after, .fa-sharp-duotone.fa-bowl-chopsticks::after {
  content: "\e2e9\e2e9"; }

.fasds.fa-credit-card::after, .fa-sharp-duotone.fa-credit-card::after {
  content: "\f09d\f09d"; }

.fasds.fa-credit-card-alt::after, .fa-sharp-duotone.fa-credit-card-alt::after {
  content: "\f09d\f09d"; }

.fasds.fa-circle-s::after, .fa-sharp-duotone.fa-circle-s::after {
  content: "\e121\e121"; }

.fasds.fa-box-ballot::after, .fa-sharp-duotone.fa-box-ballot::after {
  content: "\f735\f735"; }

.fasds.fa-car::after, .fa-sharp-duotone.fa-car::after {
  content: "\f1b9\f1b9"; }

.fasds.fa-automobile::after, .fa-sharp-duotone.fa-automobile::after {
  content: "\f1b9\f1b9"; }

.fasds.fa-hand-holding-hand::after, .fa-sharp-duotone.fa-hand-holding-hand::after {
  content: "\e4f7\e4f7"; }

.fasds.fa-user-tie-hair::after, .fa-sharp-duotone.fa-user-tie-hair::after {
  content: "\e45f\e45f"; }

.fasds.fa-podium-star::after, .fa-sharp-duotone.fa-podium-star::after {
  content: "\f758\f758"; }

.fasds.fa-user-hair-mullet::after, .fa-sharp-duotone.fa-user-hair-mullet::after {
  content: "\e45c\e45c"; }

.fasds.fa-business-front::after, .fa-sharp-duotone.fa-business-front::after {
  content: "\e45c\e45c"; }

.fasds.fa-party-back::after, .fa-sharp-duotone.fa-party-back::after {
  content: "\e45c\e45c"; }

.fasds.fa-trian-balbot::after, .fa-sharp-duotone.fa-trian-balbot::after {
  content: "\e45c\e45c"; }

.fasds.fa-microphone-stand::after, .fa-sharp-duotone.fa-microphone-stand::after {
  content: "\f8cb\f8cb"; }

.fasds.fa-book-open-reader::after, .fa-sharp-duotone.fa-book-open-reader::after {
  content: "\f5da\f5da"; }

.fasds.fa-book-reader::after, .fa-sharp-duotone.fa-book-reader::after {
  content: "\f5da\f5da"; }

.fasds.fa-family-dress::after, .fa-sharp-duotone.fa-family-dress::after {
  content: "\e301\e301"; }

.fasds.fa-circle-x::after, .fa-sharp-duotone.fa-circle-x::after {
  content: "\e12e\e12e"; }

.fasds.fa-cabin::after, .fa-sharp-duotone.fa-cabin::after {
  content: "\e46d\e46d"; }

.fasds.fa-mountain-sun::after, .fa-sharp-duotone.fa-mountain-sun::after {
  content: "\e52f\e52f"; }

.fasds.fa-chart-simple-horizontal::after, .fa-sharp-duotone.fa-chart-simple-horizontal::after {
  content: "\e474\e474"; }

.fasds.fa-arrows-left-right-to-line::after, .fa-sharp-duotone.fa-arrows-left-right-to-line::after {
  content: "\e4ba\e4ba"; }

.fasds.fa-hand-back-point-left::after, .fa-sharp-duotone.fa-hand-back-point-left::after {
  content: "\e19f\e19f"; }

.fasds.fa-message-dots::after, .fa-sharp-duotone.fa-message-dots::after {
  content: "\f4a3\f4a3"; }

.fasds.fa-comment-alt-dots::after, .fa-sharp-duotone.fa-comment-alt-dots::after {
  content: "\f4a3\f4a3"; }

.fasds.fa-messaging::after, .fa-sharp-duotone.fa-messaging::after {
  content: "\f4a3\f4a3"; }

.fasds.fa-file-heart::after, .fa-sharp-duotone.fa-file-heart::after {
  content: "\e176\e176"; }

.fasds.fa-beer-mug::after, .fa-sharp-duotone.fa-beer-mug::after {
  content: "\e0b3\e0b3"; }

.fasds.fa-beer-foam::after, .fa-sharp-duotone.fa-beer-foam::after {
  content: "\e0b3\e0b3"; }

.fasds.fa-dice-d20::after, .fa-sharp-duotone.fa-dice-d20::after {
  content: "\f6cf\f6cf"; }

.fasds.fa-drone::after, .fa-sharp-duotone.fa-drone::after {
  content: "\f85f\f85f"; }

.fasds.fa-truck-droplet::after, .fa-sharp-duotone.fa-truck-droplet::after {
  content: "\e58c\e58c"; }

.fasds.fa-file-circle-xmark::after, .fa-sharp-duotone.fa-file-circle-xmark::after {
  content: "\e5a1\e5a1"; }

.fasds.fa-temperature-arrow-up::after, .fa-sharp-duotone.fa-temperature-arrow-up::after {
  content: "\e040\e040"; }

.fasds.fa-temperature-up::after, .fa-sharp-duotone.fa-temperature-up::after {
  content: "\e040\e040"; }

.fasds.fa-medal::after, .fa-sharp-duotone.fa-medal::after {
  content: "\f5a2\f5a2"; }

.fasds.fa-person-fairy::after, .fa-sharp-duotone.fa-person-fairy::after {
  content: "\e608\e608"; }

.fasds.fa-bed::after, .fa-sharp-duotone.fa-bed::after {
  content: "\f236\f236"; }

.fasds.fa-book-copy::after, .fa-sharp-duotone.fa-book-copy::after {
  content: "\e0be\e0be"; }

.fasds.fa-square-h::after, .fa-sharp-duotone.fa-square-h::after {
  content: "\f0fd\f0fd"; }

.fasds.fa-h-square::after, .fa-sharp-duotone.fa-h-square::after {
  content: "\f0fd\f0fd"; }

.fasds.fa-square-c::after, .fa-sharp-duotone.fa-square-c::after {
  content: "\e266\e266"; }

.fasds.fa-clock-two::after, .fa-sharp-duotone.fa-clock-two::after {
  content: "\e35a\e35a"; }

.fasds.fa-square-ellipsis-vertical::after, .fa-sharp-duotone.fa-square-ellipsis-vertical::after {
  content: "\e26f\e26f"; }

.fasds.fa-calendar-users::after, .fa-sharp-duotone.fa-calendar-users::after {
  content: "\e5e2\e5e2"; }

.fasds.fa-podcast::after, .fa-sharp-duotone.fa-podcast::after {
  content: "\f2ce\f2ce"; }

.fasds.fa-bee::after, .fa-sharp-duotone.fa-bee::after {
  content: "\e0b2\e0b2"; }

.fasds.fa-temperature-full::after, .fa-sharp-duotone.fa-temperature-full::after {
  content: "\f2c7\f2c7"; }

.fasds.fa-temperature-4::after, .fa-sharp-duotone.fa-temperature-4::after {
  content: "\f2c7\f2c7"; }

.fasds.fa-thermometer-4::after, .fa-sharp-duotone.fa-thermometer-4::after {
  content: "\f2c7\f2c7"; }

.fasds.fa-thermometer-full::after, .fa-sharp-duotone.fa-thermometer-full::after {
  content: "\f2c7\f2c7"; }

.fasds.fa-bell::after, .fa-sharp-duotone.fa-bell::after {
  content: "\f0f3\f0f3"; }

.fasds.fa-candy-bar::after, .fa-sharp-duotone.fa-candy-bar::after {
  content: "\e3e8\e3e8"; }

.fasds.fa-chocolate-bar::after, .fa-sharp-duotone.fa-chocolate-bar::after {
  content: "\e3e8\e3e8"; }

.fasds.fa-xmark-large::after, .fa-sharp-duotone.fa-xmark-large::after {
  content: "\e59b\e59b"; }

.fasds.fa-pinata::after, .fa-sharp-duotone.fa-pinata::after {
  content: "\e3c3\e3c3"; }

.fasds.fa-file-ppt::after, .fa-sharp-duotone.fa-file-ppt::after {
  content: "\e64a\e64a"; }

.fasds.fa-arrows-from-line::after, .fa-sharp-duotone.fa-arrows-from-line::after {
  content: "\e0a4\e0a4"; }

.fasds.fa-superscript::after, .fa-sharp-duotone.fa-superscript::after {
  content: "\f12b\f12b"; }

.fasds.fa-bowl-spoon::after, .fa-sharp-duotone.fa-bowl-spoon::after {
  content: "\e3e0\e3e0"; }

.fasds.fa-hexagon-check::after, .fa-sharp-duotone.fa-hexagon-check::after {
  content: "\e416\e416"; }

.fasds.fa-plug-circle-xmark::after, .fa-sharp-duotone.fa-plug-circle-xmark::after {
  content: "\e560\e560"; }

.fasds.fa-star-of-life::after, .fa-sharp-duotone.fa-star-of-life::after {
  content: "\f621\f621"; }

.fasds.fa-phone-slash::after, .fa-sharp-duotone.fa-phone-slash::after {
  content: "\f3dd\f3dd"; }

.fasds.fa-traffic-light-stop::after, .fa-sharp-duotone.fa-traffic-light-stop::after {
  content: "\f63a\f63a"; }

.fasds.fa-paint-roller::after, .fa-sharp-duotone.fa-paint-roller::after {
  content: "\f5aa\f5aa"; }

.fasds.fa-accent-grave::after, .fa-sharp-duotone.fa-accent-grave::after {
  content: "\60\60"; }

.fasds.fa-handshake-angle::after, .fa-sharp-duotone.fa-handshake-angle::after {
  content: "\f4c4\f4c4"; }

.fasds.fa-hands-helping::after, .fa-sharp-duotone.fa-hands-helping::after {
  content: "\f4c4\f4c4"; }

.fasds.fa-circle-0::after, .fa-sharp-duotone.fa-circle-0::after {
  content: "\e0ed\e0ed"; }

.fasds.fa-dial-med-low::after, .fa-sharp-duotone.fa-dial-med-low::after {
  content: "\e160\e160"; }

.fasds.fa-location-dot::after, .fa-sharp-duotone.fa-location-dot::after {
  content: "\f3c5\f3c5"; }

.fasds.fa-map-marker-alt::after, .fa-sharp-duotone.fa-map-marker-alt::after {
  content: "\f3c5\f3c5"; }

.fasds.fa-crab::after, .fa-sharp-duotone.fa-crab::after {
  content: "\e3ff\e3ff"; }

.fasds.fa-box-open-full::after, .fa-sharp-duotone.fa-box-open-full::after {
  content: "\f49c\f49c"; }

.fasds.fa-box-full::after, .fa-sharp-duotone.fa-box-full::after {
  content: "\f49c\f49c"; }

.fasds.fa-file::after, .fa-sharp-duotone.fa-file::after {
  content: "\f15b\f15b"; }

.fasds.fa-greater-than::after, .fa-sharp-duotone.fa-greater-than::after {
  content: "\3e\3e"; }

.fasds.fa-quotes::after, .fa-sharp-duotone.fa-quotes::after {
  content: "\e234\e234"; }

.fasds.fa-pretzel::after, .fa-sharp-duotone.fa-pretzel::after {
  content: "\e441\e441"; }

.fasds.fa-t-rex::after, .fa-sharp-duotone.fa-t-rex::after {
  content: "\e629\e629"; }

.fasds.fa-person-swimming::after, .fa-sharp-duotone.fa-person-swimming::after {
  content: "\f5c4\f5c4"; }

.fasds.fa-swimmer::after, .fa-sharp-duotone.fa-swimmer::after {
  content: "\f5c4\f5c4"; }

.fasds.fa-arrow-down::after, .fa-sharp-duotone.fa-arrow-down::after {
  content: "\f063\f063"; }

.fasds.fa-user-robot-xmarks::after, .fa-sharp-duotone.fa-user-robot-xmarks::after {
  content: "\e4a7\e4a7"; }

.fasds.fa-message-quote::after, .fa-sharp-duotone.fa-message-quote::after {
  content: "\e1e4\e1e4"; }

.fasds.fa-comment-alt-quote::after, .fa-sharp-duotone.fa-comment-alt-quote::after {
  content: "\e1e4\e1e4"; }

.fasds.fa-candy-corn::after, .fa-sharp-duotone.fa-candy-corn::after {
  content: "\f6bd\f6bd"; }

.fasds.fa-folder-magnifying-glass::after, .fa-sharp-duotone.fa-folder-magnifying-glass::after {
  content: "\e18b\e18b"; }

.fasds.fa-folder-search::after, .fa-sharp-duotone.fa-folder-search::after {
  content: "\e18b\e18b"; }

.fasds.fa-notebook::after, .fa-sharp-duotone.fa-notebook::after {
  content: "\e201\e201"; }

.fasds.fa-circle-wifi::after, .fa-sharp-duotone.fa-circle-wifi::after {
  content: "\e67d\e67d"; }

.fasds.fa-droplet::after, .fa-sharp-duotone.fa-droplet::after {
  content: "\f043\f043"; }

.fasds.fa-tint::after, .fa-sharp-duotone.fa-tint::after {
  content: "\f043\f043"; }

.fasds.fa-bullseye-pointer::after, .fa-sharp-duotone.fa-bullseye-pointer::after {
  content: "\f649\f649"; }

.fasds.fa-eraser::after, .fa-sharp-duotone.fa-eraser::after {
  content: "\f12d\f12d"; }

.fasds.fa-hexagon-image::after, .fa-sharp-duotone.fa-hexagon-image::after {
  content: "\e504\e504"; }

.fasds.fa-earth-americas::after, .fa-sharp-duotone.fa-earth-americas::after {
  content: "\f57d\f57d"; }

.fasds.fa-earth::after, .fa-sharp-duotone.fa-earth::after {
  content: "\f57d\f57d"; }

.fasds.fa-earth-america::after, .fa-sharp-duotone.fa-earth-america::after {
  content: "\f57d\f57d"; }

.fasds.fa-globe-americas::after, .fa-sharp-duotone.fa-globe-americas::after {
  content: "\f57d\f57d"; }

.fasds.fa-file-svg::after, .fa-sharp-duotone.fa-file-svg::after {
  content: "\e64b\e64b"; }

.fasds.fa-crate-apple::after, .fa-sharp-duotone.fa-crate-apple::after {
  content: "\f6b1\f6b1"; }

.fasds.fa-apple-crate::after, .fa-sharp-duotone.fa-apple-crate::after {
  content: "\f6b1\f6b1"; }

.fasds.fa-person-burst::after, .fa-sharp-duotone.fa-person-burst::after {
  content: "\e53b\e53b"; }

.fasds.fa-game-board::after, .fa-sharp-duotone.fa-game-board::after {
  content: "\f867\f867"; }

.fasds.fa-hat-chef::after, .fa-sharp-duotone.fa-hat-chef::after {
  content: "\f86b\f86b"; }

.fasds.fa-hand-back-point-right::after, .fa-sharp-duotone.fa-hand-back-point-right::after {
  content: "\e1a1\e1a1"; }

.fasds.fa-dove::after, .fa-sharp-duotone.fa-dove::after {
  content: "\f4ba\f4ba"; }

.fasds.fa-snowflake-droplets::after, .fa-sharp-duotone.fa-snowflake-droplets::after {
  content: "\e5c1\e5c1"; }

.fasds.fa-battery-empty::after, .fa-sharp-duotone.fa-battery-empty::after {
  content: "\f244\f244"; }

.fasds.fa-battery-0::after, .fa-sharp-duotone.fa-battery-0::after {
  content: "\f244\f244"; }

.fasds.fa-grid-4::after, .fa-sharp-duotone.fa-grid-4::after {
  content: "\e198\e198"; }

.fasds.fa-socks::after, .fa-sharp-duotone.fa-socks::after {
  content: "\f696\f696"; }

.fasds.fa-face-sunglasses::after, .fa-sharp-duotone.fa-face-sunglasses::after {
  content: "\e398\e398"; }

.fasds.fa-inbox::after, .fa-sharp-duotone.fa-inbox::after {
  content: "\f01c\f01c"; }

.fasds.fa-square-0::after, .fa-sharp-duotone.fa-square-0::after {
  content: "\e255\e255"; }

.fasds.fa-section::after, .fa-sharp-duotone.fa-section::after {
  content: "\e447\e447"; }

.fasds.fa-square-this-way-up::after, .fa-sharp-duotone.fa-square-this-way-up::after {
  content: "\f49f\f49f"; }

.fasds.fa-box-up::after, .fa-sharp-duotone.fa-box-up::after {
  content: "\f49f\f49f"; }

.fasds.fa-gauge-high::after, .fa-sharp-duotone.fa-gauge-high::after {
  content: "\f625\f625"; }

.fasds.fa-tachometer-alt::after, .fa-sharp-duotone.fa-tachometer-alt::after {
  content: "\f625\f625"; }

.fasds.fa-tachometer-alt-fast::after, .fa-sharp-duotone.fa-tachometer-alt-fast::after {
  content: "\f625\f625"; }

.fasds.fa-square-ampersand::after, .fa-sharp-duotone.fa-square-ampersand::after {
  content: "\e260\e260"; }

.fasds.fa-envelope-open-text::after, .fa-sharp-duotone.fa-envelope-open-text::after {
  content: "\f658\f658"; }

.fasds.fa-lamp-desk::after, .fa-sharp-duotone.fa-lamp-desk::after {
  content: "\e014\e014"; }

.fasds.fa-hospital::after, .fa-sharp-duotone.fa-hospital::after {
  content: "\f0f8\f0f8"; }

.fasds.fa-hospital-alt::after, .fa-sharp-duotone.fa-hospital-alt::after {
  content: "\f0f8\f0f8"; }

.fasds.fa-hospital-wide::after, .fa-sharp-duotone.fa-hospital-wide::after {
  content: "\f0f8\f0f8"; }

.fasds.fa-poll-people::after, .fa-sharp-duotone.fa-poll-people::after {
  content: "\f759\f759"; }

.fasds.fa-whiskey-glass-ice::after, .fa-sharp-duotone.fa-whiskey-glass-ice::after {
  content: "\f7a1\f7a1"; }

.fasds.fa-glass-whiskey-rocks::after, .fa-sharp-duotone.fa-glass-whiskey-rocks::after {
  content: "\f7a1\f7a1"; }

.fasds.fa-wine-bottle::after, .fa-sharp-duotone.fa-wine-bottle::after {
  content: "\f72f\f72f"; }

.fasds.fa-chess-rook::after, .fa-sharp-duotone.fa-chess-rook::after {
  content: "\f447\f447"; }

.fasds.fa-user-bounty-hunter::after, .fa-sharp-duotone.fa-user-bounty-hunter::after {
  content: "\e2bf\e2bf"; }

.fasds.fa-bars-staggered::after, .fa-sharp-duotone.fa-bars-staggered::after {
  content: "\f550\f550"; }

.fasds.fa-reorder::after, .fa-sharp-duotone.fa-reorder::after {
  content: "\f550\f550"; }

.fasds.fa-stream::after, .fa-sharp-duotone.fa-stream::after {
  content: "\f550\f550"; }

.fasds.fa-diagram-sankey::after, .fa-sharp-duotone.fa-diagram-sankey::after {
  content: "\e158\e158"; }

.fasds.fa-cloud-hail-mixed::after, .fa-sharp-duotone.fa-cloud-hail-mixed::after {
  content: "\f73a\f73a"; }

.fasds.fa-circle-up-left::after, .fa-sharp-duotone.fa-circle-up-left::after {
  content: "\e128\e128"; }

.fasds.fa-dharmachakra::after, .fa-sharp-duotone.fa-dharmachakra::after {
  content: "\f655\f655"; }

.fasds.fa-objects-align-left::after, .fa-sharp-duotone.fa-objects-align-left::after {
  content: "\e3be\e3be"; }

.fasds.fa-oil-can-drip::after, .fa-sharp-duotone.fa-oil-can-drip::after {
  content: "\e205\e205"; }

.fasds.fa-face-smiling-hands::after, .fa-sharp-duotone.fa-face-smiling-hands::after {
  content: "\e396\e396"; }

.fasds.fa-broccoli::after, .fa-sharp-duotone.fa-broccoli::after {
  content: "\e3e2\e3e2"; }

.fasds.fa-route-interstate::after, .fa-sharp-duotone.fa-route-interstate::after {
  content: "\f61b\f61b"; }

.fasds.fa-ear-muffs::after, .fa-sharp-duotone.fa-ear-muffs::after {
  content: "\f795\f795"; }

.fasds.fa-hotdog::after, .fa-sharp-duotone.fa-hotdog::after {
  content: "\f80f\f80f"; }

.fasds.fa-transporter-empty::after, .fa-sharp-duotone.fa-transporter-empty::after {
  content: "\e046\e046"; }

.fasds.fa-person-walking-with-cane::after, .fa-sharp-duotone.fa-person-walking-with-cane::after {
  content: "\f29d\f29d"; }

.fasds.fa-blind::after, .fa-sharp-duotone.fa-blind::after {
  content: "\f29d\f29d"; }

.fasds.fa-angle-90::after, .fa-sharp-duotone.fa-angle-90::after {
  content: "\e08d\e08d"; }

.fasds.fa-rectangle-terminal::after, .fa-sharp-duotone.fa-rectangle-terminal::after {
  content: "\e236\e236"; }

.fasds.fa-kite::after, .fa-sharp-duotone.fa-kite::after {
  content: "\f6f4\f6f4"; }

.fasds.fa-drum::after, .fa-sharp-duotone.fa-drum::after {
  content: "\f569\f569"; }

.fasds.fa-scrubber::after, .fa-sharp-duotone.fa-scrubber::after {
  content: "\f2f8\f2f8"; }

.fasds.fa-ice-cream::after, .fa-sharp-duotone.fa-ice-cream::after {
  content: "\f810\f810"; }

.fasds.fa-heart-circle-bolt::after, .fa-sharp-duotone.fa-heart-circle-bolt::after {
  content: "\e4fc\e4fc"; }

.fasds.fa-fish-bones::after, .fa-sharp-duotone.fa-fish-bones::after {
  content: "\e304\e304"; }

.fasds.fa-deer-rudolph::after, .fa-sharp-duotone.fa-deer-rudolph::after {
  content: "\f78f\f78f"; }

.fasds.fa-fax::after, .fa-sharp-duotone.fa-fax::after {
  content: "\f1ac\f1ac"; }

.fasds.fa-paragraph::after, .fa-sharp-duotone.fa-paragraph::after {
  content: "\f1dd\f1dd"; }

.fasds.fa-head-side-heart::after, .fa-sharp-duotone.fa-head-side-heart::after {
  content: "\e1aa\e1aa"; }

.fasds.fa-square-e::after, .fa-sharp-duotone.fa-square-e::after {
  content: "\e26d\e26d"; }

.fasds.fa-meter-fire::after, .fa-sharp-duotone.fa-meter-fire::after {
  content: "\e1eb\e1eb"; }

.fasds.fa-cloud-hail::after, .fa-sharp-duotone.fa-cloud-hail::after {
  content: "\f739\f739"; }

.fasds.fa-check-to-slot::after, .fa-sharp-duotone.fa-check-to-slot::after {
  content: "\f772\f772"; }

.fasds.fa-vote-yea::after, .fa-sharp-duotone.fa-vote-yea::after {
  content: "\f772\f772"; }

.fasds.fa-money-from-bracket::after, .fa-sharp-duotone.fa-money-from-bracket::after {
  content: "\e312\e312"; }

.fasds.fa-star-half::after, .fa-sharp-duotone.fa-star-half::after {
  content: "\f089\f089"; }

.fasds.fa-car-bus::after, .fa-sharp-duotone.fa-car-bus::after {
  content: "\f85a\f85a"; }

.fasds.fa-speaker::after, .fa-sharp-duotone.fa-speaker::after {
  content: "\f8df\f8df"; }

.fasds.fa-timer::after, .fa-sharp-duotone.fa-timer::after {
  content: "\e29e\e29e"; }

.fasds.fa-boxes-stacked::after, .fa-sharp-duotone.fa-boxes-stacked::after {
  content: "\f468\f468"; }

.fasds.fa-boxes::after, .fa-sharp-duotone.fa-boxes::after {
  content: "\f468\f468"; }

.fasds.fa-boxes-alt::after, .fa-sharp-duotone.fa-boxes-alt::after {
  content: "\f468\f468"; }

.fasds.fa-landmark-magnifying-glass::after, .fa-sharp-duotone.fa-landmark-magnifying-glass::after {
  content: "\e622\e622"; }

.fasds.fa-grill-hot::after, .fa-sharp-duotone.fa-grill-hot::after {
  content: "\e5a5\e5a5"; }

.fasds.fa-ballot-check::after, .fa-sharp-duotone.fa-ballot-check::after {
  content: "\f733\f733"; }

.fasds.fa-link::after, .fa-sharp-duotone.fa-link::after {
  content: "\f0c1\f0c1"; }

.fasds.fa-chain::after, .fa-sharp-duotone.fa-chain::after {
  content: "\f0c1\f0c1"; }

.fasds.fa-ear-listen::after, .fa-sharp-duotone.fa-ear-listen::after {
  content: "\f2a2\f2a2"; }

.fasds.fa-assistive-listening-systems::after, .fa-sharp-duotone.fa-assistive-listening-systems::after {
  content: "\f2a2\f2a2"; }

.fasds.fa-file-minus::after, .fa-sharp-duotone.fa-file-minus::after {
  content: "\f318\f318"; }

.fasds.fa-tree-city::after, .fa-sharp-duotone.fa-tree-city::after {
  content: "\e587\e587"; }

.fasds.fa-play::after, .fa-sharp-duotone.fa-play::after {
  content: "\f04b\f04b"; }

.fasds.fa-font::after, .fa-sharp-duotone.fa-font::after {
  content: "\f031\f031"; }

.fasds.fa-cup-togo::after, .fa-sharp-duotone.fa-cup-togo::after {
  content: "\f6c5\f6c5"; }

.fasds.fa-coffee-togo::after, .fa-sharp-duotone.fa-coffee-togo::after {
  content: "\f6c5\f6c5"; }

.fasds.fa-square-down-left::after, .fa-sharp-duotone.fa-square-down-left::after {
  content: "\e26b\e26b"; }

.fasds.fa-burger-lettuce::after, .fa-sharp-duotone.fa-burger-lettuce::after {
  content: "\e3e3\e3e3"; }

.fasds.fa-table-cells-row-lock::after, .fa-sharp-duotone.fa-table-cells-row-lock::after {
  content: "\e67a\e67a"; }

.fasds.fa-rupiah-sign::after, .fa-sharp-duotone.fa-rupiah-sign::after {
  content: "\e23d\e23d"; }

.fasds.fa-magnifying-glass::after, .fa-sharp-duotone.fa-magnifying-glass::after {
  content: "\f002\f002"; }

.fasds.fa-search::after, .fa-sharp-duotone.fa-search::after {
  content: "\f002\f002"; }

.fasds.fa-table-tennis-paddle-ball::after, .fa-sharp-duotone.fa-table-tennis-paddle-ball::after {
  content: "\f45d\f45d"; }

.fasds.fa-ping-pong-paddle-ball::after, .fa-sharp-duotone.fa-ping-pong-paddle-ball::after {
  content: "\f45d\f45d"; }

.fasds.fa-table-tennis::after, .fa-sharp-duotone.fa-table-tennis::after {
  content: "\f45d\f45d"; }

.fasds.fa-person-dots-from-line::after, .fa-sharp-duotone.fa-person-dots-from-line::after {
  content: "\f470\f470"; }

.fasds.fa-diagnoses::after, .fa-sharp-duotone.fa-diagnoses::after {
  content: "\f470\f470"; }

.fasds.fa-chevrons-down::after, .fa-sharp-duotone.fa-chevrons-down::after {
  content: "\f322\f322"; }

.fasds.fa-chevron-double-down::after, .fa-sharp-duotone.fa-chevron-double-down::after {
  content: "\f322\f322"; }

.fasds.fa-trash-can-arrow-up::after, .fa-sharp-duotone.fa-trash-can-arrow-up::after {
  content: "\f82a\f82a"; }

.fasds.fa-trash-restore-alt::after, .fa-sharp-duotone.fa-trash-restore-alt::after {
  content: "\f82a\f82a"; }

.fasds.fa-signal-good::after, .fa-sharp-duotone.fa-signal-good::after {
  content: "\f68e\f68e"; }

.fasds.fa-signal-3::after, .fa-sharp-duotone.fa-signal-3::after {
  content: "\f68e\f68e"; }

.fasds.fa-location-question::after, .fa-sharp-duotone.fa-location-question::after {
  content: "\f60b\f60b"; }

.fasds.fa-map-marker-question::after, .fa-sharp-duotone.fa-map-marker-question::after {
  content: "\f60b\f60b"; }

.fasds.fa-floppy-disk-circle-xmark::after, .fa-sharp-duotone.fa-floppy-disk-circle-xmark::after {
  content: "\e181\e181"; }

.fasds.fa-floppy-disk-times::after, .fa-sharp-duotone.fa-floppy-disk-times::after {
  content: "\e181\e181"; }

.fasds.fa-save-circle-xmark::after, .fa-sharp-duotone.fa-save-circle-xmark::after {
  content: "\e181\e181"; }

.fasds.fa-save-times::after, .fa-sharp-duotone.fa-save-times::after {
  content: "\e181\e181"; }

.fasds.fa-naira-sign::after, .fa-sharp-duotone.fa-naira-sign::after {
  content: "\e1f6\e1f6"; }

.fasds.fa-peach::after, .fa-sharp-duotone.fa-peach::after {
  content: "\e20b\e20b"; }

.fasds.fa-taxi-bus::after, .fa-sharp-duotone.fa-taxi-bus::after {
  content: "\e298\e298"; }

.fasds.fa-bracket-curly::after, .fa-sharp-duotone.fa-bracket-curly::after {
  content: "\7b\7b"; }

.fasds.fa-bracket-curly-left::after, .fa-sharp-duotone.fa-bracket-curly-left::after {
  content: "\7b\7b"; }

.fasds.fa-lobster::after, .fa-sharp-duotone.fa-lobster::after {
  content: "\e421\e421"; }

.fasds.fa-cart-flatbed-empty::after, .fa-sharp-duotone.fa-cart-flatbed-empty::after {
  content: "\f476\f476"; }

.fasds.fa-dolly-flatbed-empty::after, .fa-sharp-duotone.fa-dolly-flatbed-empty::after {
  content: "\f476\f476"; }

.fasds.fa-colon::after, .fa-sharp-duotone.fa-colon::after {
  content: "\3a\3a"; }

.fasds.fa-cart-arrow-down::after, .fa-sharp-duotone.fa-cart-arrow-down::after {
  content: "\f218\f218"; }

.fasds.fa-wand::after, .fa-sharp-duotone.fa-wand::after {
  content: "\f72a\f72a"; }

.fasds.fa-walkie-talkie::after, .fa-sharp-duotone.fa-walkie-talkie::after {
  content: "\f8ef\f8ef"; }

.fasds.fa-file-pen::after, .fa-sharp-duotone.fa-file-pen::after {
  content: "\f31c\f31c"; }

.fasds.fa-file-edit::after, .fa-sharp-duotone.fa-file-edit::after {
  content: "\f31c\f31c"; }

.fasds.fa-receipt::after, .fa-sharp-duotone.fa-receipt::after {
  content: "\f543\f543"; }

.fasds.fa-table-picnic::after, .fa-sharp-duotone.fa-table-picnic::after {
  content: "\e32d\e32d"; }

.fasds.fa-square-pen::after, .fa-sharp-duotone.fa-square-pen::after {
  content: "\f14b\f14b"; }

.fasds.fa-pen-square::after, .fa-sharp-duotone.fa-pen-square::after {
  content: "\f14b\f14b"; }

.fasds.fa-pencil-square::after, .fa-sharp-duotone.fa-pencil-square::after {
  content: "\f14b\f14b"; }

.fasds.fa-circle-microphone-lines::after, .fa-sharp-duotone.fa-circle-microphone-lines::after {
  content: "\e117\e117"; }

.fasds.fa-microphone-circle-alt::after, .fa-sharp-duotone.fa-microphone-circle-alt::after {
  content: "\e117\e117"; }

.fasds.fa-display-slash::after, .fa-sharp-duotone.fa-display-slash::after {
  content: "\e2fa\e2fa"; }

.fasds.fa-desktop-slash::after, .fa-sharp-duotone.fa-desktop-slash::after {
  content: "\e2fa\e2fa"; }

.fasds.fa-suitcase-rolling::after, .fa-sharp-duotone.fa-suitcase-rolling::after {
  content: "\f5c1\f5c1"; }

.fasds.fa-person-circle-exclamation::after, .fa-sharp-duotone.fa-person-circle-exclamation::after {
  content: "\e53f\e53f"; }

.fasds.fa-transporter-2::after, .fa-sharp-duotone.fa-transporter-2::after {
  content: "\e044\e044"; }

.fasds.fa-user-hoodie::after, .fa-sharp-duotone.fa-user-hoodie::after {
  content: "\e68a\e68a"; }

.fasds.fa-hands-holding-diamond::after, .fa-sharp-duotone.fa-hands-holding-diamond::after {
  content: "\f47c\f47c"; }

.fasds.fa-hand-receiving::after, .fa-sharp-duotone.fa-hand-receiving::after {
  content: "\f47c\f47c"; }

.fasds.fa-money-bill-simple-wave::after, .fa-sharp-duotone.fa-money-bill-simple-wave::after {
  content: "\e1f2\e1f2"; }

.fasds.fa-chevron-down::after, .fa-sharp-duotone.fa-chevron-down::after {
  content: "\f078\f078"; }

.fasds.fa-battery-full::after, .fa-sharp-duotone.fa-battery-full::after {
  content: "\f240\f240"; }

.fasds.fa-battery::after, .fa-sharp-duotone.fa-battery::after {
  content: "\f240\f240"; }

.fasds.fa-battery-5::after, .fa-sharp-duotone.fa-battery-5::after {
  content: "\f240\f240"; }

.fasds.fa-bell-plus::after, .fa-sharp-duotone.fa-bell-plus::after {
  content: "\f849\f849"; }

.fasds.fa-book-arrow-right::after, .fa-sharp-duotone.fa-book-arrow-right::after {
  content: "\e0b9\e0b9"; }

.fasds.fa-hospitals::after, .fa-sharp-duotone.fa-hospitals::after {
  content: "\f80e\f80e"; }

.fasds.fa-club::after, .fa-sharp-duotone.fa-club::after {
  content: "\f327\f327"; }

.fasds.fa-skull-crossbones::after, .fa-sharp-duotone.fa-skull-crossbones::after {
  content: "\f714\f714"; }

.fasds.fa-droplet-degree::after, .fa-sharp-duotone.fa-droplet-degree::after {
  content: "\f748\f748"; }

.fasds.fa-dewpoint::after, .fa-sharp-duotone.fa-dewpoint::after {
  content: "\f748\f748"; }

.fasds.fa-code-compare::after, .fa-sharp-duotone.fa-code-compare::after {
  content: "\e13a\e13a"; }

.fasds.fa-list-ul::after, .fa-sharp-duotone.fa-list-ul::after {
  content: "\f0ca\f0ca"; }

.fasds.fa-list-dots::after, .fa-sharp-duotone.fa-list-dots::after {
  content: "\f0ca\f0ca"; }

.fasds.fa-hand-holding-magic::after, .fa-sharp-duotone.fa-hand-holding-magic::after {
  content: "\f6e5\f6e5"; }

.fasds.fa-watermelon-slice::after, .fa-sharp-duotone.fa-watermelon-slice::after {
  content: "\e337\e337"; }

.fasds.fa-circle-ellipsis::after, .fa-sharp-duotone.fa-circle-ellipsis::after {
  content: "\e10a\e10a"; }

.fasds.fa-school-lock::after, .fa-sharp-duotone.fa-school-lock::after {
  content: "\e56f\e56f"; }

.fasds.fa-tower-cell::after, .fa-sharp-duotone.fa-tower-cell::after {
  content: "\e585\e585"; }

.fasds.fa-sd-cards::after, .fa-sharp-duotone.fa-sd-cards::after {
  content: "\e240\e240"; }

.fasds.fa-jug-bottle::after, .fa-sharp-duotone.fa-jug-bottle::after {
  content: "\e5fb\e5fb"; }

.fasds.fa-down-long::after, .fa-sharp-duotone.fa-down-long::after {
  content: "\f309\f309"; }

.fasds.fa-long-arrow-alt-down::after, .fa-sharp-duotone.fa-long-arrow-alt-down::after {
  content: "\f309\f309"; }

.fasds.fa-envelopes::after, .fa-sharp-duotone.fa-envelopes::after {
  content: "\e170\e170"; }

.fasds.fa-phone-office::after, .fa-sharp-duotone.fa-phone-office::after {
  content: "\f67d\f67d"; }

.fasds.fa-ranking-star::after, .fa-sharp-duotone.fa-ranking-star::after {
  content: "\e561\e561"; }

.fasds.fa-chess-king::after, .fa-sharp-duotone.fa-chess-king::after {
  content: "\f43f\f43f"; }

.fasds.fa-nfc-pen::after, .fa-sharp-duotone.fa-nfc-pen::after {
  content: "\e1fa\e1fa"; }

.fasds.fa-person-harassing::after, .fa-sharp-duotone.fa-person-harassing::after {
  content: "\e549\e549"; }

.fasds.fa-magnifying-glass-play::after, .fa-sharp-duotone.fa-magnifying-glass-play::after {
  content: "\e660\e660"; }

.fasds.fa-hat-winter::after, .fa-sharp-duotone.fa-hat-winter::after {
  content: "\f7a8\f7a8"; }

.fasds.fa-brazilian-real-sign::after, .fa-sharp-duotone.fa-brazilian-real-sign::after {
  content: "\e46c\e46c"; }

.fasds.fa-landmark-dome::after, .fa-sharp-duotone.fa-landmark-dome::after {
  content: "\f752\f752"; }

.fasds.fa-landmark-alt::after, .fa-sharp-duotone.fa-landmark-alt::after {
  content: "\f752\f752"; }

.fasds.fa-bone-break::after, .fa-sharp-duotone.fa-bone-break::after {
  content: "\f5d8\f5d8"; }

.fasds.fa-arrow-up::after, .fa-sharp-duotone.fa-arrow-up::after {
  content: "\f062\f062"; }

.fasds.fa-down-from-dotted-line::after, .fa-sharp-duotone.fa-down-from-dotted-line::after {
  content: "\e407\e407"; }

.fasds.fa-tv::after, .fa-sharp-duotone.fa-tv::after {
  content: "\f26c\f26c"; }

.fasds.fa-television::after, .fa-sharp-duotone.fa-television::after {
  content: "\f26c\f26c"; }

.fasds.fa-tv-alt::after, .fa-sharp-duotone.fa-tv-alt::after {
  content: "\f26c\f26c"; }

.fasds.fa-border-left::after, .fa-sharp-duotone.fa-border-left::after {
  content: "\f84f\f84f"; }

.fasds.fa-circle-divide::after, .fa-sharp-duotone.fa-circle-divide::after {
  content: "\e106\e106"; }

.fasds.fa-shrimp::after, .fa-sharp-duotone.fa-shrimp::after {
  content: "\e448\e448"; }

.fasds.fa-list-check::after, .fa-sharp-duotone.fa-list-check::after {
  content: "\f0ae\f0ae"; }

.fasds.fa-tasks::after, .fa-sharp-duotone.fa-tasks::after {
  content: "\f0ae\f0ae"; }

.fasds.fa-diagram-subtask::after, .fa-sharp-duotone.fa-diagram-subtask::after {
  content: "\e479\e479"; }

.fasds.fa-jug-detergent::after, .fa-sharp-duotone.fa-jug-detergent::after {
  content: "\e519\e519"; }

.fasds.fa-circle-user::after, .fa-sharp-duotone.fa-circle-user::after {
  content: "\f2bd\f2bd"; }

.fasds.fa-user-circle::after, .fa-sharp-duotone.fa-user-circle::after {
  content: "\f2bd\f2bd"; }

.fasds.fa-square-y::after, .fa-sharp-duotone.fa-square-y::after {
  content: "\e287\e287"; }

.fasds.fa-user-doctor-hair::after, .fa-sharp-duotone.fa-user-doctor-hair::after {
  content: "\e458\e458"; }

.fasds.fa-planet-ringed::after, .fa-sharp-duotone.fa-planet-ringed::after {
  content: "\e020\e020"; }

.fasds.fa-mushroom::after, .fa-sharp-duotone.fa-mushroom::after {
  content: "\e425\e425"; }

.fasds.fa-user-shield::after, .fa-sharp-duotone.fa-user-shield::after {
  content: "\f505\f505"; }

.fasds.fa-megaphone::after, .fa-sharp-duotone.fa-megaphone::after {
  content: "\f675\f675"; }

.fasds.fa-wreath-laurel::after, .fa-sharp-duotone.fa-wreath-laurel::after {
  content: "\e5d2\e5d2"; }

.fasds.fa-circle-exclamation-check::after, .fa-sharp-duotone.fa-circle-exclamation-check::after {
  content: "\e10d\e10d"; }

.fasds.fa-wind::after, .fa-sharp-duotone.fa-wind::after {
  content: "\f72e\f72e"; }

.fasds.fa-box-dollar::after, .fa-sharp-duotone.fa-box-dollar::after {
  content: "\f4a0\f4a0"; }

.fasds.fa-box-usd::after, .fa-sharp-duotone.fa-box-usd::after {
  content: "\f4a0\f4a0"; }

.fasds.fa-car-burst::after, .fa-sharp-duotone.fa-car-burst::after {
  content: "\f5e1\f5e1"; }

.fasds.fa-car-crash::after, .fa-sharp-duotone.fa-car-crash::after {
  content: "\f5e1\f5e1"; }

.fasds.fa-y::after, .fa-sharp-duotone.fa-y::after {
  content: "\59\59"; }

.fasds.fa-user-headset::after, .fa-sharp-duotone.fa-user-headset::after {
  content: "\f82d\f82d"; }

.fasds.fa-arrows-retweet::after, .fa-sharp-duotone.fa-arrows-retweet::after {
  content: "\f361\f361"; }

.fasds.fa-retweet-alt::after, .fa-sharp-duotone.fa-retweet-alt::after {
  content: "\f361\f361"; }

.fasds.fa-person-snowboarding::after, .fa-sharp-duotone.fa-person-snowboarding::after {
  content: "\f7ce\f7ce"; }

.fasds.fa-snowboarding::after, .fa-sharp-duotone.fa-snowboarding::after {
  content: "\f7ce\f7ce"; }

.fasds.fa-square-chevron-right::after, .fa-sharp-duotone.fa-square-chevron-right::after {
  content: "\f32b\f32b"; }

.fasds.fa-chevron-square-right::after, .fa-sharp-duotone.fa-chevron-square-right::after {
  content: "\f32b\f32b"; }

.fasds.fa-lacrosse-stick-ball::after, .fa-sharp-duotone.fa-lacrosse-stick-ball::after {
  content: "\e3b6\e3b6"; }

.fasds.fa-truck-fast::after, .fa-sharp-duotone.fa-truck-fast::after {
  content: "\f48b\f48b"; }

.fasds.fa-shipping-fast::after, .fa-sharp-duotone.fa-shipping-fast::after {
  content: "\f48b\f48b"; }

.fasds.fa-user-magnifying-glass::after, .fa-sharp-duotone.fa-user-magnifying-glass::after {
  content: "\e5c5\e5c5"; }

.fasds.fa-star-sharp::after, .fa-sharp-duotone.fa-star-sharp::after {
  content: "\e28b\e28b"; }

.fasds.fa-comment-heart::after, .fa-sharp-duotone.fa-comment-heart::after {
  content: "\e5c8\e5c8"; }

.fasds.fa-circle-1::after, .fa-sharp-duotone.fa-circle-1::after {
  content: "\e0ee\e0ee"; }

.fasds.fa-circle-star::after, .fa-sharp-duotone.fa-circle-star::after {
  content: "\e123\e123"; }

.fasds.fa-star-circle::after, .fa-sharp-duotone.fa-star-circle::after {
  content: "\e123\e123"; }

.fasds.fa-fish::after, .fa-sharp-duotone.fa-fish::after {
  content: "\f578\f578"; }

.fasds.fa-cloud-fog::after, .fa-sharp-duotone.fa-cloud-fog::after {
  content: "\f74e\f74e"; }

.fasds.fa-fog::after, .fa-sharp-duotone.fa-fog::after {
  content: "\f74e\f74e"; }

.fasds.fa-waffle::after, .fa-sharp-duotone.fa-waffle::after {
  content: "\e466\e466"; }

.fasds.fa-music-note::after, .fa-sharp-duotone.fa-music-note::after {
  content: "\f8cf\f8cf"; }

.fasds.fa-music-alt::after, .fa-sharp-duotone.fa-music-alt::after {
  content: "\f8cf\f8cf"; }

.fasds.fa-hexagon-exclamation::after, .fa-sharp-duotone.fa-hexagon-exclamation::after {
  content: "\e417\e417"; }

.fasds.fa-cart-shopping-fast::after, .fa-sharp-duotone.fa-cart-shopping-fast::after {
  content: "\e0dc\e0dc"; }

.fasds.fa-object-union::after, .fa-sharp-duotone.fa-object-union::after {
  content: "\e49f\e49f"; }

.fasds.fa-user-graduate::after, .fa-sharp-duotone.fa-user-graduate::after {
  content: "\f501\f501"; }

.fasds.fa-starfighter::after, .fa-sharp-duotone.fa-starfighter::after {
  content: "\e037\e037"; }

.fasds.fa-circle-half-stroke::after, .fa-sharp-duotone.fa-circle-half-stroke::after {
  content: "\f042\f042"; }

.fasds.fa-adjust::after, .fa-sharp-duotone.fa-adjust::after {
  content: "\f042\f042"; }

.fasds.fa-arrow-right-long-to-line::after, .fa-sharp-duotone.fa-arrow-right-long-to-line::after {
  content: "\e3d5\e3d5"; }

.fasds.fa-square-arrow-down::after, .fa-sharp-duotone.fa-square-arrow-down::after {
  content: "\f339\f339"; }

.fasds.fa-arrow-square-down::after, .fa-sharp-duotone.fa-arrow-square-down::after {
  content: "\f339\f339"; }

.fasds.fa-diamond-half-stroke::after, .fa-sharp-duotone.fa-diamond-half-stroke::after {
  content: "\e5b8\e5b8"; }

.fasds.fa-clapperboard::after, .fa-sharp-duotone.fa-clapperboard::after {
  content: "\e131\e131"; }

.fasds.fa-square-chevron-left::after, .fa-sharp-duotone.fa-square-chevron-left::after {
  content: "\f32a\f32a"; }

.fasds.fa-chevron-square-left::after, .fa-sharp-duotone.fa-chevron-square-left::after {
  content: "\f32a\f32a"; }

.fasds.fa-phone-intercom::after, .fa-sharp-duotone.fa-phone-intercom::after {
  content: "\e434\e434"; }

.fasds.fa-link-horizontal::after, .fa-sharp-duotone.fa-link-horizontal::after {
  content: "\e1cb\e1cb"; }

.fasds.fa-chain-horizontal::after, .fa-sharp-duotone.fa-chain-horizontal::after {
  content: "\e1cb\e1cb"; }

.fasds.fa-mango::after, .fa-sharp-duotone.fa-mango::after {
  content: "\e30f\e30f"; }

.fasds.fa-music-note-slash::after, .fa-sharp-duotone.fa-music-note-slash::after {
  content: "\f8d0\f8d0"; }

.fasds.fa-music-alt-slash::after, .fa-sharp-duotone.fa-music-alt-slash::after {
  content: "\f8d0\f8d0"; }

.fasds.fa-circle-radiation::after, .fa-sharp-duotone.fa-circle-radiation::after {
  content: "\f7ba\f7ba"; }

.fasds.fa-radiation-alt::after, .fa-sharp-duotone.fa-radiation-alt::after {
  content: "\f7ba\f7ba"; }

.fasds.fa-face-tongue-sweat::after, .fa-sharp-duotone.fa-face-tongue-sweat::after {
  content: "\e39e\e39e"; }

.fasds.fa-globe-stand::after, .fa-sharp-duotone.fa-globe-stand::after {
  content: "\f5f6\f5f6"; }

.fasds.fa-baseball::after, .fa-sharp-duotone.fa-baseball::after {
  content: "\f433\f433"; }

.fasds.fa-baseball-ball::after, .fa-sharp-duotone.fa-baseball-ball::after {
  content: "\f433\f433"; }

.fasds.fa-circle-p::after, .fa-sharp-duotone.fa-circle-p::after {
  content: "\e11a\e11a"; }

.fasds.fa-award-simple::after, .fa-sharp-duotone.fa-award-simple::after {
  content: "\e0ab\e0ab"; }

.fasds.fa-jet-fighter-up::after, .fa-sharp-duotone.fa-jet-fighter-up::after {
  content: "\e518\e518"; }

.fasds.fa-diagram-project::after, .fa-sharp-duotone.fa-diagram-project::after {
  content: "\f542\f542"; }

.fasds.fa-project-diagram::after, .fa-sharp-duotone.fa-project-diagram::after {
  content: "\f542\f542"; }

.fasds.fa-pedestal::after, .fa-sharp-duotone.fa-pedestal::after {
  content: "\e20d\e20d"; }

.fasds.fa-chart-pyramid::after, .fa-sharp-duotone.fa-chart-pyramid::after {
  content: "\e0e6\e0e6"; }

.fasds.fa-sidebar::after, .fa-sharp-duotone.fa-sidebar::after {
  content: "\e24e\e24e"; }

.fasds.fa-snowman-head::after, .fa-sharp-duotone.fa-snowman-head::after {
  content: "\f79b\f79b"; }

.fasds.fa-frosty-head::after, .fa-sharp-duotone.fa-frosty-head::after {
  content: "\f79b\f79b"; }

.fasds.fa-copy::after, .fa-sharp-duotone.fa-copy::after {
  content: "\f0c5\f0c5"; }

.fasds.fa-burger-glass::after, .fa-sharp-duotone.fa-burger-glass::after {
  content: "\e0ce\e0ce"; }

.fasds.fa-volume-xmark::after, .fa-sharp-duotone.fa-volume-xmark::after {
  content: "\f6a9\f6a9"; }

.fasds.fa-volume-mute::after, .fa-sharp-duotone.fa-volume-mute::after {
  content: "\f6a9\f6a9"; }

.fasds.fa-volume-times::after, .fa-sharp-duotone.fa-volume-times::after {
  content: "\f6a9\f6a9"; }

.fasds.fa-hand-sparkles::after, .fa-sharp-duotone.fa-hand-sparkles::after {
  content: "\e05d\e05d"; }

.fasds.fa-bars-filter::after, .fa-sharp-duotone.fa-bars-filter::after {
  content: "\e0ad\e0ad"; }

.fasds.fa-paintbrush-pencil::after, .fa-sharp-duotone.fa-paintbrush-pencil::after {
  content: "\e206\e206"; }

.fasds.fa-party-bell::after, .fa-sharp-duotone.fa-party-bell::after {
  content: "\e31a\e31a"; }

.fasds.fa-user-vneck-hair::after, .fa-sharp-duotone.fa-user-vneck-hair::after {
  content: "\e462\e462"; }

.fasds.fa-jack-o-lantern::after, .fa-sharp-duotone.fa-jack-o-lantern::after {
  content: "\f30e\f30e"; }

.fasds.fa-grip::after, .fa-sharp-duotone.fa-grip::after {
  content: "\f58d\f58d"; }

.fasds.fa-grip-horizontal::after, .fa-sharp-duotone.fa-grip-horizontal::after {
  content: "\f58d\f58d"; }

.fasds.fa-share-from-square::after, .fa-sharp-duotone.fa-share-from-square::after {
  content: "\f14d\f14d"; }

.fasds.fa-share-square::after, .fa-sharp-duotone.fa-share-square::after {
  content: "\f14d\f14d"; }

.fasds.fa-keynote::after, .fa-sharp-duotone.fa-keynote::after {
  content: "\f66c\f66c"; }

.fasds.fa-child-combatant::after, .fa-sharp-duotone.fa-child-combatant::after {
  content: "\e4e0\e4e0"; }

.fasds.fa-child-rifle::after, .fa-sharp-duotone.fa-child-rifle::after {
  content: "\e4e0\e4e0"; }

.fasds.fa-gun::after, .fa-sharp-duotone.fa-gun::after {
  content: "\e19b\e19b"; }

.fasds.fa-square-phone::after, .fa-sharp-duotone.fa-square-phone::after {
  content: "\f098\f098"; }

.fasds.fa-phone-square::after, .fa-sharp-duotone.fa-phone-square::after {
  content: "\f098\f098"; }

.fasds.fa-hat-beach::after, .fa-sharp-duotone.fa-hat-beach::after {
  content: "\e606\e606"; }

.fasds.fa-plus::after, .fa-sharp-duotone.fa-plus::after {
  content: "\2b\2b"; }

.fasds.fa-add::after, .fa-sharp-duotone.fa-add::after {
  content: "\2b\2b"; }

.fasds.fa-expand::after, .fa-sharp-duotone.fa-expand::after {
  content: "\f065\f065"; }

.fasds.fa-computer::after, .fa-sharp-duotone.fa-computer::after {
  content: "\e4e5\e4e5"; }

.fasds.fa-fort::after, .fa-sharp-duotone.fa-fort::after {
  content: "\e486\e486"; }

.fasds.fa-cloud-check::after, .fa-sharp-duotone.fa-cloud-check::after {
  content: "\e35c\e35c"; }

.fasds.fa-xmark::after, .fa-sharp-duotone.fa-xmark::after {
  content: "\f00d\f00d"; }

.fasds.fa-close::after, .fa-sharp-duotone.fa-close::after {
  content: "\f00d\f00d"; }

.fasds.fa-multiply::after, .fa-sharp-duotone.fa-multiply::after {
  content: "\f00d\f00d"; }

.fasds.fa-remove::after, .fa-sharp-duotone.fa-remove::after {
  content: "\f00d\f00d"; }

.fasds.fa-times::after, .fa-sharp-duotone.fa-times::after {
  content: "\f00d\f00d"; }

.fasds.fa-face-smirking::after, .fa-sharp-duotone.fa-face-smirking::after {
  content: "\e397\e397"; }

.fasds.fa-arrows-up-down-left-right::after, .fa-sharp-duotone.fa-arrows-up-down-left-right::after {
  content: "\f047\f047"; }

.fasds.fa-arrows::after, .fa-sharp-duotone.fa-arrows::after {
  content: "\f047\f047"; }

.fasds.fa-chalkboard-user::after, .fa-sharp-duotone.fa-chalkboard-user::after {
  content: "\f51c\f51c"; }

.fasds.fa-chalkboard-teacher::after, .fa-sharp-duotone.fa-chalkboard-teacher::after {
  content: "\f51c\f51c"; }

.fasds.fa-rhombus::after, .fa-sharp-duotone.fa-rhombus::after {
  content: "\e23b\e23b"; }

.fasds.fa-claw-marks::after, .fa-sharp-duotone.fa-claw-marks::after {
  content: "\f6c2\f6c2"; }

.fasds.fa-peso-sign::after, .fa-sharp-duotone.fa-peso-sign::after {
  content: "\e222\e222"; }

.fasds.fa-face-smile-tongue::after, .fa-sharp-duotone.fa-face-smile-tongue::after {
  content: "\e394\e394"; }

.fasds.fa-cart-circle-xmark::after, .fa-sharp-duotone.fa-cart-circle-xmark::after {
  content: "\e3f4\e3f4"; }

.fasds.fa-building-shield::after, .fa-sharp-duotone.fa-building-shield::after {
  content: "\e4d8\e4d8"; }

.fasds.fa-circle-phone-flip::after, .fa-sharp-duotone.fa-circle-phone-flip::after {
  content: "\e11c\e11c"; }

.fasds.fa-phone-circle-alt::after, .fa-sharp-duotone.fa-phone-circle-alt::after {
  content: "\e11c\e11c"; }

.fasds.fa-baby::after, .fa-sharp-duotone.fa-baby::after {
  content: "\f77c\f77c"; }

.fasds.fa-users-line::after, .fa-sharp-duotone.fa-users-line::after {
  content: "\e592\e592"; }

.fasds.fa-quote-left::after, .fa-sharp-duotone.fa-quote-left::after {
  content: "\f10d\f10d"; }

.fasds.fa-quote-left-alt::after, .fa-sharp-duotone.fa-quote-left-alt::after {
  content: "\f10d\f10d"; }

.fasds.fa-tractor::after, .fa-sharp-duotone.fa-tractor::after {
  content: "\f722\f722"; }

.fasds.fa-down-from-bracket::after, .fa-sharp-duotone.fa-down-from-bracket::after {
  content: "\e66b\e66b"; }

.fasds.fa-key-skeleton::after, .fa-sharp-duotone.fa-key-skeleton::after {
  content: "\f6f3\f6f3"; }

.fasds.fa-trash-arrow-up::after, .fa-sharp-duotone.fa-trash-arrow-up::after {
  content: "\f829\f829"; }

.fasds.fa-trash-restore::after, .fa-sharp-duotone.fa-trash-restore::after {
  content: "\f829\f829"; }

.fasds.fa-arrow-down-up-lock::after, .fa-sharp-duotone.fa-arrow-down-up-lock::after {
  content: "\e4b0\e4b0"; }

.fasds.fa-arrow-down-to-bracket::after, .fa-sharp-duotone.fa-arrow-down-to-bracket::after {
  content: "\e094\e094"; }

.fasds.fa-lines-leaning::after, .fa-sharp-duotone.fa-lines-leaning::after {
  content: "\e51e\e51e"; }

.fasds.fa-square-q::after, .fa-sharp-duotone.fa-square-q::after {
  content: "\e27b\e27b"; }

.fasds.fa-ruler-combined::after, .fa-sharp-duotone.fa-ruler-combined::after {
  content: "\f546\f546"; }

.fasds.fa-symbols::after, .fa-sharp-duotone.fa-symbols::after {
  content: "\f86e\f86e"; }

.fasds.fa-icons-alt::after, .fa-sharp-duotone.fa-icons-alt::after {
  content: "\f86e\f86e"; }

.fasds.fa-copyright::after, .fa-sharp-duotone.fa-copyright::after {
  content: "\f1f9\f1f9"; }

.fasds.fa-flask-gear::after, .fa-sharp-duotone.fa-flask-gear::after {
  content: "\e5f1\e5f1"; }

.fasds.fa-highlighter-line::after, .fa-sharp-duotone.fa-highlighter-line::after {
  content: "\e1af\e1af"; }

.fasds.fa-bracket-square::after, .fa-sharp-duotone.fa-bracket-square::after {
  content: "\5b\5b"; }

.fasds.fa-bracket::after, .fa-sharp-duotone.fa-bracket::after {
  content: "\5b\5b"; }

.fasds.fa-bracket-left::after, .fa-sharp-duotone.fa-bracket-left::after {
  content: "\5b\5b"; }

.fasds.fa-island-tropical::after, .fa-sharp-duotone.fa-island-tropical::after {
  content: "\f811\f811"; }

.fasds.fa-island-tree-palm::after, .fa-sharp-duotone.fa-island-tree-palm::after {
  content: "\f811\f811"; }

.fasds.fa-arrow-right-from-line::after, .fa-sharp-duotone.fa-arrow-right-from-line::after {
  content: "\f343\f343"; }

.fasds.fa-arrow-from-left::after, .fa-sharp-duotone.fa-arrow-from-left::after {
  content: "\f343\f343"; }

.fasds.fa-h2::after, .fa-sharp-duotone.fa-h2::after {
  content: "\f314\f314"; }

.fasds.fa-equals::after, .fa-sharp-duotone.fa-equals::after {
  content: "\3d\3d"; }

.fasds.fa-cake-slice::after, .fa-sharp-duotone.fa-cake-slice::after {
  content: "\e3e5\e3e5"; }

.fasds.fa-shortcake::after, .fa-sharp-duotone.fa-shortcake::after {
  content: "\e3e5\e3e5"; }

.fasds.fa-building-magnifying-glass::after, .fa-sharp-duotone.fa-building-magnifying-glass::after {
  content: "\e61c\e61c"; }

.fasds.fa-peanut::after, .fa-sharp-duotone.fa-peanut::after {
  content: "\e430\e430"; }

.fasds.fa-wrench-simple::after, .fa-sharp-duotone.fa-wrench-simple::after {
  content: "\e2d1\e2d1"; }

.fasds.fa-blender::after, .fa-sharp-duotone.fa-blender::after {
  content: "\f517\f517"; }

.fasds.fa-teeth::after, .fa-sharp-duotone.fa-teeth::after {
  content: "\f62e\f62e"; }

.fasds.fa-tally-2::after, .fa-sharp-duotone.fa-tally-2::after {
  content: "\e295\e295"; }

.fasds.fa-shekel-sign::after, .fa-sharp-duotone.fa-shekel-sign::after {
  content: "\f20b\f20b"; }

.fasds.fa-ils::after, .fa-sharp-duotone.fa-ils::after {
  content: "\f20b\f20b"; }

.fasds.fa-shekel::after, .fa-sharp-duotone.fa-shekel::after {
  content: "\f20b\f20b"; }

.fasds.fa-sheqel::after, .fa-sharp-duotone.fa-sheqel::after {
  content: "\f20b\f20b"; }

.fasds.fa-sheqel-sign::after, .fa-sharp-duotone.fa-sheqel-sign::after {
  content: "\f20b\f20b"; }

.fasds.fa-cars::after, .fa-sharp-duotone.fa-cars::after {
  content: "\f85b\f85b"; }

.fasds.fa-axe-battle::after, .fa-sharp-duotone.fa-axe-battle::after {
  content: "\f6b3\f6b3"; }

.fasds.fa-user-hair-long::after, .fa-sharp-duotone.fa-user-hair-long::after {
  content: "\e45b\e45b"; }

.fasds.fa-map::after, .fa-sharp-duotone.fa-map::after {
  content: "\f279\f279"; }

.fasds.fa-arrow-left-from-arc::after, .fa-sharp-duotone.fa-arrow-left-from-arc::after {
  content: "\e615\e615"; }

.fasds.fa-file-circle-info::after, .fa-sharp-duotone.fa-file-circle-info::after {
  content: "\e493\e493"; }

.fasds.fa-face-disappointed::after, .fa-sharp-duotone.fa-face-disappointed::after {
  content: "\e36f\e36f"; }

.fasds.fa-lasso-sparkles::after, .fa-sharp-duotone.fa-lasso-sparkles::after {
  content: "\e1c9\e1c9"; }

.fasds.fa-clock-eleven::after, .fa-sharp-duotone.fa-clock-eleven::after {
  content: "\e347\e347"; }

.fasds.fa-rocket::after, .fa-sharp-duotone.fa-rocket::after {
  content: "\f135\f135"; }

.fasds.fa-siren-on::after, .fa-sharp-duotone.fa-siren-on::after {
  content: "\e02e\e02e"; }

.fasds.fa-clock-ten::after, .fa-sharp-duotone.fa-clock-ten::after {
  content: "\e354\e354"; }

.fasds.fa-candle-holder::after, .fa-sharp-duotone.fa-candle-holder::after {
  content: "\f6bc\f6bc"; }

.fasds.fa-video-arrow-down-left::after, .fa-sharp-duotone.fa-video-arrow-down-left::after {
  content: "\e2c8\e2c8"; }

.fasds.fa-photo-film::after, .fa-sharp-duotone.fa-photo-film::after {
  content: "\f87c\f87c"; }

.fasds.fa-photo-video::after, .fa-sharp-duotone.fa-photo-video::after {
  content: "\f87c\f87c"; }

.fasds.fa-floppy-disk-circle-arrow-right::after, .fa-sharp-duotone.fa-floppy-disk-circle-arrow-right::after {
  content: "\e180\e180"; }

.fasds.fa-save-circle-arrow-right::after, .fa-sharp-duotone.fa-save-circle-arrow-right::after {
  content: "\e180\e180"; }

.fasds.fa-folder-minus::after, .fa-sharp-duotone.fa-folder-minus::after {
  content: "\f65d\f65d"; }

.fasds.fa-planet-moon::after, .fa-sharp-duotone.fa-planet-moon::after {
  content: "\e01f\e01f"; }

.fasds.fa-face-eyes-xmarks::after, .fa-sharp-duotone.fa-face-eyes-xmarks::after {
  content: "\e374\e374"; }

.fasds.fa-chart-scatter::after, .fa-sharp-duotone.fa-chart-scatter::after {
  content: "\f7ee\f7ee"; }

.fasds.fa-circle-gf::after, .fa-sharp-duotone.fa-circle-gf::after {
  content: "\e67f\e67f"; }

.fasds.fa-display-arrow-down::after, .fa-sharp-duotone.fa-display-arrow-down::after {
  content: "\e164\e164"; }

.fasds.fa-store::after, .fa-sharp-duotone.fa-store::after {
  content: "\f54e\f54e"; }

.fasds.fa-arrow-trend-up::after, .fa-sharp-duotone.fa-arrow-trend-up::after {
  content: "\e098\e098"; }

.fasds.fa-plug-circle-minus::after, .fa-sharp-duotone.fa-plug-circle-minus::after {
  content: "\e55e\e55e"; }

.fasds.fa-olive-branch::after, .fa-sharp-duotone.fa-olive-branch::after {
  content: "\e317\e317"; }

.fasds.fa-angle::after, .fa-sharp-duotone.fa-angle::after {
  content: "\e08c\e08c"; }

.fasds.fa-vacuum-robot::after, .fa-sharp-duotone.fa-vacuum-robot::after {
  content: "\e04e\e04e"; }

.fasds.fa-sign-hanging::after, .fa-sharp-duotone.fa-sign-hanging::after {
  content: "\f4d9\f4d9"; }

.fasds.fa-sign::after, .fa-sharp-duotone.fa-sign::after {
  content: "\f4d9\f4d9"; }

.fasds.fa-square-divide::after, .fa-sharp-duotone.fa-square-divide::after {
  content: "\e26a\e26a"; }

.fasds.fa-folder-check::after, .fa-sharp-duotone.fa-folder-check::after {
  content: "\e64e\e64e"; }

.fasds.fa-signal-stream-slash::after, .fa-sharp-duotone.fa-signal-stream-slash::after {
  content: "\e250\e250"; }

.fasds.fa-bezier-curve::after, .fa-sharp-duotone.fa-bezier-curve::after {
  content: "\f55b\f55b"; }

.fasds.fa-eye-dropper-half::after, .fa-sharp-duotone.fa-eye-dropper-half::after {
  content: "\e173\e173"; }

.fasds.fa-store-lock::after, .fa-sharp-duotone.fa-store-lock::after {
  content: "\e4a6\e4a6"; }

.fasds.fa-bell-slash::after, .fa-sharp-duotone.fa-bell-slash::after {
  content: "\f1f6\f1f6"; }

.fasds.fa-cloud-bolt-sun::after, .fa-sharp-duotone.fa-cloud-bolt-sun::after {
  content: "\f76e\f76e"; }

.fasds.fa-thunderstorm-sun::after, .fa-sharp-duotone.fa-thunderstorm-sun::after {
  content: "\f76e\f76e"; }

.fasds.fa-camera-slash::after, .fa-sharp-duotone.fa-camera-slash::after {
  content: "\e0d9\e0d9"; }

.fasds.fa-comment-quote::after, .fa-sharp-duotone.fa-comment-quote::after {
  content: "\e14c\e14c"; }

.fasds.fa-tablet::after, .fa-sharp-duotone.fa-tablet::after {
  content: "\f3fb\f3fb"; }

.fasds.fa-tablet-android::after, .fa-sharp-duotone.fa-tablet-android::after {
  content: "\f3fb\f3fb"; }

.fasds.fa-school-flag::after, .fa-sharp-duotone.fa-school-flag::after {
  content: "\e56e\e56e"; }

.fasds.fa-message-code::after, .fa-sharp-duotone.fa-message-code::after {
  content: "\e1df\e1df"; }

.fasds.fa-glass-half::after, .fa-sharp-duotone.fa-glass-half::after {
  content: "\e192\e192"; }

.fasds.fa-glass-half-empty::after, .fa-sharp-duotone.fa-glass-half-empty::after {
  content: "\e192\e192"; }

.fasds.fa-glass-half-full::after, .fa-sharp-duotone.fa-glass-half-full::after {
  content: "\e192\e192"; }

.fasds.fa-fill::after, .fa-sharp-duotone.fa-fill::after {
  content: "\f575\f575"; }

.fasds.fa-message-minus::after, .fa-sharp-duotone.fa-message-minus::after {
  content: "\f4a7\f4a7"; }

.fasds.fa-comment-alt-minus::after, .fa-sharp-duotone.fa-comment-alt-minus::after {
  content: "\f4a7\f4a7"; }

.fasds.fa-angle-up::after, .fa-sharp-duotone.fa-angle-up::after {
  content: "\f106\f106"; }

.fasds.fa-dinosaur::after, .fa-sharp-duotone.fa-dinosaur::after {
  content: "\e5fe\e5fe"; }

.fasds.fa-drumstick-bite::after, .fa-sharp-duotone.fa-drumstick-bite::after {
  content: "\f6d7\f6d7"; }

.fasds.fa-link-horizontal-slash::after, .fa-sharp-duotone.fa-link-horizontal-slash::after {
  content: "\e1cc\e1cc"; }

.fasds.fa-chain-horizontal-slash::after, .fa-sharp-duotone.fa-chain-horizontal-slash::after {
  content: "\e1cc\e1cc"; }

.fasds.fa-holly-berry::after, .fa-sharp-duotone.fa-holly-berry::after {
  content: "\f7aa\f7aa"; }

.fasds.fa-nose::after, .fa-sharp-duotone.fa-nose::after {
  content: "\e5bd\e5bd"; }

.fasds.fa-arrow-left-to-arc::after, .fa-sharp-duotone.fa-arrow-left-to-arc::after {
  content: "\e616\e616"; }

.fasds.fa-chevron-left::after, .fa-sharp-duotone.fa-chevron-left::after {
  content: "\f053\f053"; }

.fasds.fa-bacteria::after, .fa-sharp-duotone.fa-bacteria::after {
  content: "\e059\e059"; }

.fasds.fa-clouds::after, .fa-sharp-duotone.fa-clouds::after {
  content: "\f744\f744"; }

.fasds.fa-money-bill-simple::after, .fa-sharp-duotone.fa-money-bill-simple::after {
  content: "\e1f1\e1f1"; }

.fasds.fa-hand-lizard::after, .fa-sharp-duotone.fa-hand-lizard::after {
  content: "\f258\f258"; }

.fasds.fa-table-pivot::after, .fa-sharp-duotone.fa-table-pivot::after {
  content: "\e291\e291"; }

.fasds.fa-filter-slash::after, .fa-sharp-duotone.fa-filter-slash::after {
  content: "\e17d\e17d"; }

.fasds.fa-trash-can-undo::after, .fa-sharp-duotone.fa-trash-can-undo::after {
  content: "\f896\f896"; }

.fasds.fa-trash-can-arrow-turn-left::after, .fa-sharp-duotone.fa-trash-can-arrow-turn-left::after {
  content: "\f896\f896"; }

.fasds.fa-trash-undo-alt::after, .fa-sharp-duotone.fa-trash-undo-alt::after {
  content: "\f896\f896"; }

.fasds.fa-notdef::after, .fa-sharp-duotone.fa-notdef::after {
  content: "\e1fe\e1fe"; }

.fasds.fa-disease::after, .fa-sharp-duotone.fa-disease::after {
  content: "\f7fa\f7fa"; }

.fasds.fa-person-to-door::after, .fa-sharp-duotone.fa-person-to-door::after {
  content: "\e433\e433"; }

.fasds.fa-turntable::after, .fa-sharp-duotone.fa-turntable::after {
  content: "\f8e4\f8e4"; }

.fasds.fa-briefcase-medical::after, .fa-sharp-duotone.fa-briefcase-medical::after {
  content: "\f469\f469"; }

.fasds.fa-genderless::after, .fa-sharp-duotone.fa-genderless::after {
  content: "\f22d\f22d"; }

.fasds.fa-chevron-right::after, .fa-sharp-duotone.fa-chevron-right::after {
  content: "\f054\f054"; }

.fasds.fa-signal-weak::after, .fa-sharp-duotone.fa-signal-weak::after {
  content: "\f68c\f68c"; }

.fasds.fa-signal-1::after, .fa-sharp-duotone.fa-signal-1::after {
  content: "\f68c\f68c"; }

.fasds.fa-clock-five::after, .fa-sharp-duotone.fa-clock-five::after {
  content: "\e349\e349"; }

.fasds.fa-retweet::after, .fa-sharp-duotone.fa-retweet::after {
  content: "\f079\f079"; }

.fasds.fa-car-rear::after, .fa-sharp-duotone.fa-car-rear::after {
  content: "\f5de\f5de"; }

.fasds.fa-car-alt::after, .fa-sharp-duotone.fa-car-alt::after {
  content: "\f5de\f5de"; }

.fasds.fa-pump-soap::after, .fa-sharp-duotone.fa-pump-soap::after {
  content: "\e06b\e06b"; }

.fasds.fa-computer-classic::after, .fa-sharp-duotone.fa-computer-classic::after {
  content: "\f8b1\f8b1"; }

.fasds.fa-frame::after, .fa-sharp-duotone.fa-frame::after {
  content: "\e495\e495"; }

.fasds.fa-video-slash::after, .fa-sharp-duotone.fa-video-slash::after {
  content: "\f4e2\f4e2"; }

.fasds.fa-battery-quarter::after, .fa-sharp-duotone.fa-battery-quarter::after {
  content: "\f243\f243"; }

.fasds.fa-battery-2::after, .fa-sharp-duotone.fa-battery-2::after {
  content: "\f243\f243"; }

.fasds.fa-ellipsis-stroke::after, .fa-sharp-duotone.fa-ellipsis-stroke::after {
  content: "\f39b\f39b"; }

.fasds.fa-ellipsis-h-alt::after, .fa-sharp-duotone.fa-ellipsis-h-alt::after {
  content: "\f39b\f39b"; }

.fasds.fa-radio::after, .fa-sharp-duotone.fa-radio::after {
  content: "\f8d7\f8d7"; }

.fasds.fa-baby-carriage::after, .fa-sharp-duotone.fa-baby-carriage::after {
  content: "\f77d\f77d"; }

.fasds.fa-carriage-baby::after, .fa-sharp-duotone.fa-carriage-baby::after {
  content: "\f77d\f77d"; }

.fasds.fa-face-expressionless::after, .fa-sharp-duotone.fa-face-expressionless::after {
  content: "\e373\e373"; }

.fasds.fa-down-to-dotted-line::after, .fa-sharp-duotone.fa-down-to-dotted-line::after {
  content: "\e408\e408"; }

.fasds.fa-cloud-music::after, .fa-sharp-duotone.fa-cloud-music::after {
  content: "\f8ae\f8ae"; }

.fasds.fa-traffic-light::after, .fa-sharp-duotone.fa-traffic-light::after {
  content: "\f637\f637"; }

.fasds.fa-cloud-minus::after, .fa-sharp-duotone.fa-cloud-minus::after {
  content: "\e35d\e35d"; }

.fasds.fa-thermometer::after, .fa-sharp-duotone.fa-thermometer::after {
  content: "\f491\f491"; }

.fasds.fa-shield-minus::after, .fa-sharp-duotone.fa-shield-minus::after {
  content: "\e249\e249"; }

.fasds.fa-vr-cardboard::after, .fa-sharp-duotone.fa-vr-cardboard::after {
  content: "\f729\f729"; }

.fasds.fa-car-tilt::after, .fa-sharp-duotone.fa-car-tilt::after {
  content: "\f5e5\f5e5"; }

.fasds.fa-gauge-circle-minus::after, .fa-sharp-duotone.fa-gauge-circle-minus::after {
  content: "\e497\e497"; }

.fasds.fa-brightness-low::after, .fa-sharp-duotone.fa-brightness-low::after {
  content: "\e0ca\e0ca"; }

.fasds.fa-hand-middle-finger::after, .fa-sharp-duotone.fa-hand-middle-finger::after {
  content: "\f806\f806"; }

.fasds.fa-percent::after, .fa-sharp-duotone.fa-percent::after {
  content: "\25\25"; }

.fasds.fa-percentage::after, .fa-sharp-duotone.fa-percentage::after {
  content: "\25\25"; }

.fasds.fa-truck-moving::after, .fa-sharp-duotone.fa-truck-moving::after {
  content: "\f4df\f4df"; }

.fasds.fa-glass-water-droplet::after, .fa-sharp-duotone.fa-glass-water-droplet::after {
  content: "\e4f5\e4f5"; }

.fasds.fa-conveyor-belt::after, .fa-sharp-duotone.fa-conveyor-belt::after {
  content: "\f46e\f46e"; }

.fasds.fa-location-check::after, .fa-sharp-duotone.fa-location-check::after {
  content: "\f606\f606"; }

.fasds.fa-map-marker-check::after, .fa-sharp-duotone.fa-map-marker-check::after {
  content: "\f606\f606"; }

.fasds.fa-coin-vertical::after, .fa-sharp-duotone.fa-coin-vertical::after {
  content: "\e3fd\e3fd"; }

.fasds.fa-display::after, .fa-sharp-duotone.fa-display::after {
  content: "\e163\e163"; }

.fasds.fa-person-sign::after, .fa-sharp-duotone.fa-person-sign::after {
  content: "\f757\f757"; }

.fasds.fa-face-smile::after, .fa-sharp-duotone.fa-face-smile::after {
  content: "\f118\f118"; }

.fasds.fa-smile::after, .fa-sharp-duotone.fa-smile::after {
  content: "\f118\f118"; }

.fasds.fa-phone-hangup::after, .fa-sharp-duotone.fa-phone-hangup::after {
  content: "\e225\e225"; }

.fasds.fa-signature-slash::after, .fa-sharp-duotone.fa-signature-slash::after {
  content: "\e3cb\e3cb"; }

.fasds.fa-thumbtack::after, .fa-sharp-duotone.fa-thumbtack::after {
  content: "\f08d\f08d"; }

.fasds.fa-thumb-tack::after, .fa-sharp-duotone.fa-thumb-tack::after {
  content: "\f08d\f08d"; }

.fasds.fa-wheat-slash::after, .fa-sharp-duotone.fa-wheat-slash::after {
  content: "\e339\e339"; }

.fasds.fa-trophy::after, .fa-sharp-duotone.fa-trophy::after {
  content: "\f091\f091"; }

.fasds.fa-clouds-sun::after, .fa-sharp-duotone.fa-clouds-sun::after {
  content: "\f746\f746"; }

.fasds.fa-person-praying::after, .fa-sharp-duotone.fa-person-praying::after {
  content: "\f683\f683"; }

.fasds.fa-pray::after, .fa-sharp-duotone.fa-pray::after {
  content: "\f683\f683"; }

.fasds.fa-hammer::after, .fa-sharp-duotone.fa-hammer::after {
  content: "\f6e3\f6e3"; }

.fasds.fa-face-vomit::after, .fa-sharp-duotone.fa-face-vomit::after {
  content: "\e3a0\e3a0"; }

.fasds.fa-speakers::after, .fa-sharp-duotone.fa-speakers::after {
  content: "\f8e0\f8e0"; }

.fasds.fa-tty-answer::after, .fa-sharp-duotone.fa-tty-answer::after {
  content: "\e2b9\e2b9"; }

.fasds.fa-teletype-answer::after, .fa-sharp-duotone.fa-teletype-answer::after {
  content: "\e2b9\e2b9"; }

.fasds.fa-mug-tea-saucer::after, .fa-sharp-duotone.fa-mug-tea-saucer::after {
  content: "\e1f5\e1f5"; }

.fasds.fa-diagram-lean-canvas::after, .fa-sharp-duotone.fa-diagram-lean-canvas::after {
  content: "\e156\e156"; }

.fasds.fa-alt::after, .fa-sharp-duotone.fa-alt::after {
  content: "\e08a\e08a"; }

.fasds.fa-dial::after, .fa-sharp-duotone.fa-dial::after {
  content: "\e15b\e15b"; }

.fasds.fa-dial-med-high::after, .fa-sharp-duotone.fa-dial-med-high::after {
  content: "\e15b\e15b"; }

.fasds.fa-hand-peace::after, .fa-sharp-duotone.fa-hand-peace::after {
  content: "\f25b\f25b"; }

.fasds.fa-circle-trash::after, .fa-sharp-duotone.fa-circle-trash::after {
  content: "\e126\e126"; }

.fasds.fa-trash-circle::after, .fa-sharp-duotone.fa-trash-circle::after {
  content: "\e126\e126"; }

.fasds.fa-rotate::after, .fa-sharp-duotone.fa-rotate::after {
  content: "\f2f1\f2f1"; }

.fasds.fa-sync-alt::after, .fa-sharp-duotone.fa-sync-alt::after {
  content: "\f2f1\f2f1"; }

.fasds.fa-circle-quarters::after, .fa-sharp-duotone.fa-circle-quarters::after {
  content: "\e3f8\e3f8"; }

.fasds.fa-spinner::after, .fa-sharp-duotone.fa-spinner::after {
  content: "\f110\f110"; }

.fasds.fa-tower-control::after, .fa-sharp-duotone.fa-tower-control::after {
  content: "\e2a2\e2a2"; }

.fasds.fa-arrow-up-triangle-square::after, .fa-sharp-duotone.fa-arrow-up-triangle-square::after {
  content: "\f88a\f88a"; }

.fasds.fa-sort-shapes-up::after, .fa-sharp-duotone.fa-sort-shapes-up::after {
  content: "\f88a\f88a"; }

.fasds.fa-whale::after, .fa-sharp-duotone.fa-whale::after {
  content: "\f72c\f72c"; }

.fasds.fa-robot::after, .fa-sharp-duotone.fa-robot::after {
  content: "\f544\f544"; }

.fasds.fa-peace::after, .fa-sharp-duotone.fa-peace::after {
  content: "\f67c\f67c"; }

.fasds.fa-party-horn::after, .fa-sharp-duotone.fa-party-horn::after {
  content: "\e31b\e31b"; }

.fasds.fa-gears::after, .fa-sharp-duotone.fa-gears::after {
  content: "\f085\f085"; }

.fasds.fa-cogs::after, .fa-sharp-duotone.fa-cogs::after {
  content: "\f085\f085"; }

.fasds.fa-sun-bright::after, .fa-sharp-duotone.fa-sun-bright::after {
  content: "\e28f\e28f"; }

.fasds.fa-sun-alt::after, .fa-sharp-duotone.fa-sun-alt::after {
  content: "\e28f\e28f"; }

.fasds.fa-warehouse::after, .fa-sharp-duotone.fa-warehouse::after {
  content: "\f494\f494"; }

.fasds.fa-conveyor-belt-arm::after, .fa-sharp-duotone.fa-conveyor-belt-arm::after {
  content: "\e5f8\e5f8"; }

.fasds.fa-lock-keyhole-open::after, .fa-sharp-duotone.fa-lock-keyhole-open::after {
  content: "\f3c2\f3c2"; }

.fasds.fa-lock-open-alt::after, .fa-sharp-duotone.fa-lock-open-alt::after {
  content: "\f3c2\f3c2"; }

.fasds.fa-square-fragile::after, .fa-sharp-duotone.fa-square-fragile::after {
  content: "\f49b\f49b"; }

.fasds.fa-box-fragile::after, .fa-sharp-duotone.fa-box-fragile::after {
  content: "\f49b\f49b"; }

.fasds.fa-square-wine-glass-crack::after, .fa-sharp-duotone.fa-square-wine-glass-crack::after {
  content: "\f49b\f49b"; }

.fasds.fa-arrow-up-right-dots::after, .fa-sharp-duotone.fa-arrow-up-right-dots::after {
  content: "\e4b7\e4b7"; }

.fasds.fa-square-n::after, .fa-sharp-duotone.fa-square-n::after {
  content: "\e277\e277"; }

.fasds.fa-splotch::after, .fa-sharp-duotone.fa-splotch::after {
  content: "\f5bc\f5bc"; }

.fasds.fa-face-grin-hearts::after, .fa-sharp-duotone.fa-face-grin-hearts::after {
  content: "\f584\f584"; }

.fasds.fa-grin-hearts::after, .fa-sharp-duotone.fa-grin-hearts::after {
  content: "\f584\f584"; }

.fasds.fa-meter::after, .fa-sharp-duotone.fa-meter::after {
  content: "\e1e8\e1e8"; }

.fasds.fa-mandolin::after, .fa-sharp-duotone.fa-mandolin::after {
  content: "\f6f9\f6f9"; }

.fasds.fa-dice-four::after, .fa-sharp-duotone.fa-dice-four::after {
  content: "\f524\f524"; }

.fasds.fa-sim-card::after, .fa-sharp-duotone.fa-sim-card::after {
  content: "\f7c4\f7c4"; }

.fasds.fa-transgender::after, .fa-sharp-duotone.fa-transgender::after {
  content: "\f225\f225"; }

.fasds.fa-transgender-alt::after, .fa-sharp-duotone.fa-transgender-alt::after {
  content: "\f225\f225"; }

.fasds.fa-mercury::after, .fa-sharp-duotone.fa-mercury::after {
  content: "\f223\f223"; }

.fasds.fa-up-from-bracket::after, .fa-sharp-duotone.fa-up-from-bracket::after {
  content: "\e590\e590"; }

.fasds.fa-knife-kitchen::after, .fa-sharp-duotone.fa-knife-kitchen::after {
  content: "\f6f5\f6f5"; }

.fasds.fa-border-right::after, .fa-sharp-duotone.fa-border-right::after {
  content: "\f852\f852"; }

.fasds.fa-arrow-turn-down::after, .fa-sharp-duotone.fa-arrow-turn-down::after {
  content: "\f149\f149"; }

.fasds.fa-level-down::after, .fa-sharp-duotone.fa-level-down::after {
  content: "\f149\f149"; }

.fasds.fa-spade::after, .fa-sharp-duotone.fa-spade::after {
  content: "\f2f4\f2f4"; }

.fasds.fa-card-spade::after, .fa-sharp-duotone.fa-card-spade::after {
  content: "\e3ec\e3ec"; }

.fasds.fa-line-columns::after, .fa-sharp-duotone.fa-line-columns::after {
  content: "\f870\f870"; }

.fasds.fa-ant::after, .fa-sharp-duotone.fa-ant::after {
  content: "\e680\e680"; }

.fasds.fa-arrow-right-to-line::after, .fa-sharp-duotone.fa-arrow-right-to-line::after {
  content: "\f340\f340"; }

.fasds.fa-arrow-to-right::after, .fa-sharp-duotone.fa-arrow-to-right::after {
  content: "\f340\f340"; }

.fasds.fa-person-falling-burst::after, .fa-sharp-duotone.fa-person-falling-burst::after {
  content: "\e547\e547"; }

.fasds.fa-flag-pennant::after, .fa-sharp-duotone.fa-flag-pennant::after {
  content: "\f456\f456"; }

.fasds.fa-pennant::after, .fa-sharp-duotone.fa-pennant::after {
  content: "\f456\f456"; }

.fasds.fa-conveyor-belt-empty::after, .fa-sharp-duotone.fa-conveyor-belt-empty::after {
  content: "\e150\e150"; }

.fasds.fa-user-group-simple::after, .fa-sharp-duotone.fa-user-group-simple::after {
  content: "\e603\e603"; }

.fasds.fa-award::after, .fa-sharp-duotone.fa-award::after {
  content: "\f559\f559"; }

.fasds.fa-ticket-simple::after, .fa-sharp-duotone.fa-ticket-simple::after {
  content: "\f3ff\f3ff"; }

.fasds.fa-ticket-alt::after, .fa-sharp-duotone.fa-ticket-alt::after {
  content: "\f3ff\f3ff"; }

.fasds.fa-building::after, .fa-sharp-duotone.fa-building::after {
  content: "\f1ad\f1ad"; }

.fasds.fa-angles-left::after, .fa-sharp-duotone.fa-angles-left::after {
  content: "\f100\f100"; }

.fasds.fa-angle-double-left::after, .fa-sharp-duotone.fa-angle-double-left::after {
  content: "\f100\f100"; }

.fasds.fa-camcorder::after, .fa-sharp-duotone.fa-camcorder::after {
  content: "\f8a8\f8a8"; }

.fasds.fa-video-handheld::after, .fa-sharp-duotone.fa-video-handheld::after {
  content: "\f8a8\f8a8"; }

.fasds.fa-pancakes::after, .fa-sharp-duotone.fa-pancakes::after {
  content: "\e42d\e42d"; }

.fasds.fa-album-circle-user::after, .fa-sharp-duotone.fa-album-circle-user::after {
  content: "\e48d\e48d"; }

.fasds.fa-subtitles-slash::after, .fa-sharp-duotone.fa-subtitles-slash::after {
  content: "\e610\e610"; }

.fasds.fa-qrcode::after, .fa-sharp-duotone.fa-qrcode::after {
  content: "\f029\f029"; }

.fasds.fa-dice-d10::after, .fa-sharp-duotone.fa-dice-d10::after {
  content: "\f6cd\f6cd"; }

.fasds.fa-fireplace::after, .fa-sharp-duotone.fa-fireplace::after {
  content: "\f79a\f79a"; }

.fasds.fa-browser::after, .fa-sharp-duotone.fa-browser::after {
  content: "\f37e\f37e"; }

.fasds.fa-pen-paintbrush::after, .fa-sharp-duotone.fa-pen-paintbrush::after {
  content: "\f618\f618"; }

.fasds.fa-pencil-paintbrush::after, .fa-sharp-duotone.fa-pencil-paintbrush::after {
  content: "\f618\f618"; }

.fasds.fa-fish-cooked::after, .fa-sharp-duotone.fa-fish-cooked::after {
  content: "\f7fe\f7fe"; }

.fasds.fa-chair-office::after, .fa-sharp-duotone.fa-chair-office::after {
  content: "\f6c1\f6c1"; }

.fasds.fa-magnifying-glass-music::after, .fa-sharp-duotone.fa-magnifying-glass-music::after {
  content: "\e65f\e65f"; }

.fasds.fa-nesting-dolls::after, .fa-sharp-duotone.fa-nesting-dolls::after {
  content: "\e3ba\e3ba"; }

.fasds.fa-clock-rotate-left::after, .fa-sharp-duotone.fa-clock-rotate-left::after {
  content: "\f1da\f1da"; }

.fasds.fa-history::after, .fa-sharp-duotone.fa-history::after {
  content: "\f1da\f1da"; }

.fasds.fa-trumpet::after, .fa-sharp-duotone.fa-trumpet::after {
  content: "\f8e3\f8e3"; }

.fasds.fa-face-grin-beam-sweat::after, .fa-sharp-duotone.fa-face-grin-beam-sweat::after {
  content: "\f583\f583"; }

.fasds.fa-grin-beam-sweat::after, .fa-sharp-duotone.fa-grin-beam-sweat::after {
  content: "\f583\f583"; }

.fasds.fa-fire-smoke::after, .fa-sharp-duotone.fa-fire-smoke::after {
  content: "\f74b\f74b"; }

.fasds.fa-phone-missed::after, .fa-sharp-duotone.fa-phone-missed::after {
  content: "\e226\e226"; }

.fasds.fa-file-export::after, .fa-sharp-duotone.fa-file-export::after {
  content: "\f56e\f56e"; }

.fasds.fa-arrow-right-from-file::after, .fa-sharp-duotone.fa-arrow-right-from-file::after {
  content: "\f56e\f56e"; }

.fasds.fa-shield::after, .fa-sharp-duotone.fa-shield::after {
  content: "\f132\f132"; }

.fasds.fa-shield-blank::after, .fa-sharp-duotone.fa-shield-blank::after {
  content: "\f132\f132"; }

.fasds.fa-arrow-up-short-wide::after, .fa-sharp-duotone.fa-arrow-up-short-wide::after {
  content: "\f885\f885"; }

.fasds.fa-sort-amount-up-alt::after, .fa-sharp-duotone.fa-sort-amount-up-alt::after {
  content: "\f885\f885"; }

.fasds.fa-arrows-repeat-1::after, .fa-sharp-duotone.fa-arrows-repeat-1::after {
  content: "\f366\f366"; }

.fasds.fa-repeat-1-alt::after, .fa-sharp-duotone.fa-repeat-1-alt::after {
  content: "\f366\f366"; }

.fasds.fa-gun-slash::after, .fa-sharp-duotone.fa-gun-slash::after {
  content: "\e19c\e19c"; }

.fasds.fa-avocado::after, .fa-sharp-duotone.fa-avocado::after {
  content: "\e0aa\e0aa"; }

.fasds.fa-binary::after, .fa-sharp-duotone.fa-binary::after {
  content: "\e33b\e33b"; }

.fasds.fa-glasses-round::after, .fa-sharp-duotone.fa-glasses-round::after {
  content: "\f5f5\f5f5"; }

.fasds.fa-glasses-alt::after, .fa-sharp-duotone.fa-glasses-alt::after {
  content: "\f5f5\f5f5"; }

.fasds.fa-phone-plus::after, .fa-sharp-duotone.fa-phone-plus::after {
  content: "\f4d2\f4d2"; }

.fasds.fa-ditto::after, .fa-sharp-duotone.fa-ditto::after {
  content: "\22\22"; }

.fasds.fa-person-seat::after, .fa-sharp-duotone.fa-person-seat::after {
  content: "\e21e\e21e"; }

.fasds.fa-house-medical::after, .fa-sharp-duotone.fa-house-medical::after {
  content: "\e3b2\e3b2"; }

.fasds.fa-golf-ball-tee::after, .fa-sharp-duotone.fa-golf-ball-tee::after {
  content: "\f450\f450"; }

.fasds.fa-golf-ball::after, .fa-sharp-duotone.fa-golf-ball::after {
  content: "\f450\f450"; }

.fasds.fa-circle-chevron-left::after, .fa-sharp-duotone.fa-circle-chevron-left::after {
  content: "\f137\f137"; }

.fasds.fa-chevron-circle-left::after, .fa-sharp-duotone.fa-chevron-circle-left::after {
  content: "\f137\f137"; }

.fasds.fa-house-chimney-window::after, .fa-sharp-duotone.fa-house-chimney-window::after {
  content: "\e00d\e00d"; }

.fasds.fa-scythe::after, .fa-sharp-duotone.fa-scythe::after {
  content: "\f710\f710"; }

.fasds.fa-pen-nib::after, .fa-sharp-duotone.fa-pen-nib::after {
  content: "\f5ad\f5ad"; }

.fasds.fa-ban-parking::after, .fa-sharp-duotone.fa-ban-parking::after {
  content: "\f616\f616"; }

.fasds.fa-parking-circle-slash::after, .fa-sharp-duotone.fa-parking-circle-slash::after {
  content: "\f616\f616"; }

.fasds.fa-tent-arrow-turn-left::after, .fa-sharp-duotone.fa-tent-arrow-turn-left::after {
  content: "\e580\e580"; }

.fasds.fa-face-diagonal-mouth::after, .fa-sharp-duotone.fa-face-diagonal-mouth::after {
  content: "\e47e\e47e"; }

.fasds.fa-diagram-cells::after, .fa-sharp-duotone.fa-diagram-cells::after {
  content: "\e475\e475"; }

.fasds.fa-cricket-bat-ball::after, .fa-sharp-duotone.fa-cricket-bat-ball::after {
  content: "\f449\f449"; }

.fasds.fa-cricket::after, .fa-sharp-duotone.fa-cricket::after {
  content: "\f449\f449"; }

.fasds.fa-tents::after, .fa-sharp-duotone.fa-tents::after {
  content: "\e582\e582"; }

.fasds.fa-wand-magic::after, .fa-sharp-duotone.fa-wand-magic::after {
  content: "\f0d0\f0d0"; }

.fasds.fa-magic::after, .fa-sharp-duotone.fa-magic::after {
  content: "\f0d0\f0d0"; }

.fasds.fa-dog::after, .fa-sharp-duotone.fa-dog::after {
  content: "\f6d3\f6d3"; }

.fasds.fa-pen-line::after, .fa-sharp-duotone.fa-pen-line::after {
  content: "\e212\e212"; }

.fasds.fa-atom-simple::after, .fa-sharp-duotone.fa-atom-simple::after {
  content: "\f5d3\f5d3"; }

.fasds.fa-atom-alt::after, .fa-sharp-duotone.fa-atom-alt::after {
  content: "\f5d3\f5d3"; }

.fasds.fa-ampersand::after, .fa-sharp-duotone.fa-ampersand::after {
  content: "\26\26"; }

.fasds.fa-carrot::after, .fa-sharp-duotone.fa-carrot::after {
  content: "\f787\f787"; }

.fasds.fa-arrow-up-from-line::after, .fa-sharp-duotone.fa-arrow-up-from-line::after {
  content: "\f342\f342"; }

.fasds.fa-arrow-from-bottom::after, .fa-sharp-duotone.fa-arrow-from-bottom::after {
  content: "\f342\f342"; }

.fasds.fa-moon::after, .fa-sharp-duotone.fa-moon::after {
  content: "\f186\f186"; }

.fasds.fa-pen-slash::after, .fa-sharp-duotone.fa-pen-slash::after {
  content: "\e213\e213"; }

.fasds.fa-wine-glass-empty::after, .fa-sharp-duotone.fa-wine-glass-empty::after {
  content: "\f5ce\f5ce"; }

.fasds.fa-wine-glass-alt::after, .fa-sharp-duotone.fa-wine-glass-alt::after {
  content: "\f5ce\f5ce"; }

.fasds.fa-square-star::after, .fa-sharp-duotone.fa-square-star::after {
  content: "\e27f\e27f"; }

.fasds.fa-cheese::after, .fa-sharp-duotone.fa-cheese::after {
  content: "\f7ef\f7ef"; }

.fasds.fa-send-backward::after, .fa-sharp-duotone.fa-send-backward::after {
  content: "\f87f\f87f"; }

.fasds.fa-yin-yang::after, .fa-sharp-duotone.fa-yin-yang::after {
  content: "\f6ad\f6ad"; }

.fasds.fa-music::after, .fa-sharp-duotone.fa-music::after {
  content: "\f001\f001"; }

.fasds.fa-compass-slash::after, .fa-sharp-duotone.fa-compass-slash::after {
  content: "\f5e9\f5e9"; }

.fasds.fa-clock-one::after, .fa-sharp-duotone.fa-clock-one::after {
  content: "\e34e\e34e"; }

.fasds.fa-file-music::after, .fa-sharp-duotone.fa-file-music::after {
  content: "\f8b6\f8b6"; }

.fasds.fa-code-commit::after, .fa-sharp-duotone.fa-code-commit::after {
  content: "\f386\f386"; }

.fasds.fa-temperature-low::after, .fa-sharp-duotone.fa-temperature-low::after {
  content: "\f76b\f76b"; }

.fasds.fa-person-biking::after, .fa-sharp-duotone.fa-person-biking::after {
  content: "\f84a\f84a"; }

.fasds.fa-biking::after, .fa-sharp-duotone.fa-biking::after {
  content: "\f84a\f84a"; }

.fasds.fa-display-chart-up-circle-currency::after, .fa-sharp-duotone.fa-display-chart-up-circle-currency::after {
  content: "\e5e5\e5e5"; }

.fasds.fa-skeleton::after, .fa-sharp-duotone.fa-skeleton::after {
  content: "\f620\f620"; }

.fasds.fa-circle-g::after, .fa-sharp-duotone.fa-circle-g::after {
  content: "\e10f\e10f"; }

.fasds.fa-circle-arrow-up-left::after, .fa-sharp-duotone.fa-circle-arrow-up-left::after {
  content: "\e0fb\e0fb"; }

.fasds.fa-coin-blank::after, .fa-sharp-duotone.fa-coin-blank::after {
  content: "\e3fb\e3fb"; }

.fasds.fa-broom::after, .fa-sharp-duotone.fa-broom::after {
  content: "\f51a\f51a"; }

.fasds.fa-vacuum::after, .fa-sharp-duotone.fa-vacuum::after {
  content: "\e04d\e04d"; }

.fasds.fa-shield-heart::after, .fa-sharp-duotone.fa-shield-heart::after {
  content: "\e574\e574"; }

.fasds.fa-card-heart::after, .fa-sharp-duotone.fa-card-heart::after {
  content: "\e3eb\e3eb"; }

.fasds.fa-lightbulb-cfl-on::after, .fa-sharp-duotone.fa-lightbulb-cfl-on::after {
  content: "\e5a7\e5a7"; }

.fasds.fa-melon::after, .fa-sharp-duotone.fa-melon::after {
  content: "\e310\e310"; }

.fasds.fa-gopuram::after, .fa-sharp-duotone.fa-gopuram::after {
  content: "\f664\f664"; }

.fasds.fa-earth-oceania::after, .fa-sharp-duotone.fa-earth-oceania::after {
  content: "\e47b\e47b"; }

.fasds.fa-globe-oceania::after, .fa-sharp-duotone.fa-globe-oceania::after {
  content: "\e47b\e47b"; }

.fasds.fa-container-storage::after, .fa-sharp-duotone.fa-container-storage::after {
  content: "\f4b7\f4b7"; }

.fasds.fa-face-pouting::after, .fa-sharp-duotone.fa-face-pouting::after {
  content: "\e387\e387"; }

.fasds.fa-square-xmark::after, .fa-sharp-duotone.fa-square-xmark::after {
  content: "\f2d3\f2d3"; }

.fasds.fa-times-square::after, .fa-sharp-duotone.fa-times-square::after {
  content: "\f2d3\f2d3"; }

.fasds.fa-xmark-square::after, .fa-sharp-duotone.fa-xmark-square::after {
  content: "\f2d3\f2d3"; }

.fasds.fa-face-explode::after, .fa-sharp-duotone.fa-face-explode::after {
  content: "\e2fe\e2fe"; }

.fasds.fa-exploding-head::after, .fa-sharp-duotone.fa-exploding-head::after {
  content: "\e2fe\e2fe"; }

.fasds.fa-hashtag::after, .fa-sharp-duotone.fa-hashtag::after {
  content: "\23\23"; }

.fasds.fa-up-right-and-down-left-from-center::after, .fa-sharp-duotone.fa-up-right-and-down-left-from-center::after {
  content: "\f424\f424"; }

.fasds.fa-expand-alt::after, .fa-sharp-duotone.fa-expand-alt::after {
  content: "\f424\f424"; }

.fasds.fa-oil-can::after, .fa-sharp-duotone.fa-oil-can::after {
  content: "\f613\f613"; }

.fasds.fa-t::after, .fa-sharp-duotone.fa-t::after {
  content: "\54\54"; }

.fasds.fa-transformer-bolt::after, .fa-sharp-duotone.fa-transformer-bolt::after {
  content: "\e2a4\e2a4"; }

.fasds.fa-hippo::after, .fa-sharp-duotone.fa-hippo::after {
  content: "\f6ed\f6ed"; }

.fasds.fa-chart-column::after, .fa-sharp-duotone.fa-chart-column::after {
  content: "\e0e3\e0e3"; }

.fasds.fa-cassette-vhs::after, .fa-sharp-duotone.fa-cassette-vhs::after {
  content: "\f8ec\f8ec"; }

.fasds.fa-vhs::after, .fa-sharp-duotone.fa-vhs::after {
  content: "\f8ec\f8ec"; }

.fasds.fa-infinity::after, .fa-sharp-duotone.fa-infinity::after {
  content: "\f534\f534"; }

.fasds.fa-vial-circle-check::after, .fa-sharp-duotone.fa-vial-circle-check::after {
  content: "\e596\e596"; }

.fasds.fa-chimney::after, .fa-sharp-duotone.fa-chimney::after {
  content: "\f78b\f78b"; }

.fasds.fa-object-intersect::after, .fa-sharp-duotone.fa-object-intersect::after {
  content: "\e49d\e49d"; }

.fasds.fa-person-arrow-down-to-line::after, .fa-sharp-duotone.fa-person-arrow-down-to-line::after {
  content: "\e538\e538"; }

.fasds.fa-voicemail::after, .fa-sharp-duotone.fa-voicemail::after {
  content: "\f897\f897"; }

.fasds.fa-block-brick::after, .fa-sharp-duotone.fa-block-brick::after {
  content: "\e3db\e3db"; }

.fasds.fa-wall-brick::after, .fa-sharp-duotone.fa-wall-brick::after {
  content: "\e3db\e3db"; }

.fasds.fa-fan::after, .fa-sharp-duotone.fa-fan::after {
  content: "\f863\f863"; }

.fasds.fa-bags-shopping::after, .fa-sharp-duotone.fa-bags-shopping::after {
  content: "\f847\f847"; }

.fasds.fa-paragraph-left::after, .fa-sharp-duotone.fa-paragraph-left::after {
  content: "\f878\f878"; }

.fasds.fa-paragraph-rtl::after, .fa-sharp-duotone.fa-paragraph-rtl::after {
  content: "\f878\f878"; }

.fasds.fa-person-walking-luggage::after, .fa-sharp-duotone.fa-person-walking-luggage::after {
  content: "\e554\e554"; }

.fasds.fa-caravan-simple::after, .fa-sharp-duotone.fa-caravan-simple::after {
  content: "\e000\e000"; }

.fasds.fa-caravan-alt::after, .fa-sharp-duotone.fa-caravan-alt::after {
  content: "\e000\e000"; }

.fasds.fa-turtle::after, .fa-sharp-duotone.fa-turtle::after {
  content: "\f726\f726"; }

.fasds.fa-pencil-mechanical::after, .fa-sharp-duotone.fa-pencil-mechanical::after {
  content: "\e5ca\e5ca"; }

.fasds.fa-up-down::after, .fa-sharp-duotone.fa-up-down::after {
  content: "\f338\f338"; }

.fasds.fa-arrows-alt-v::after, .fa-sharp-duotone.fa-arrows-alt-v::after {
  content: "\f338\f338"; }

.fasds.fa-cloud-moon-rain::after, .fa-sharp-duotone.fa-cloud-moon-rain::after {
  content: "\f73c\f73c"; }

.fasds.fa-booth-curtain::after, .fa-sharp-duotone.fa-booth-curtain::after {
  content: "\f734\f734"; }

.fasds.fa-calendar::after, .fa-sharp-duotone.fa-calendar::after {
  content: "\f133\f133"; }

.fasds.fa-box-heart::after, .fa-sharp-duotone.fa-box-heart::after {
  content: "\f49d\f49d"; }

.fasds.fa-trailer::after, .fa-sharp-duotone.fa-trailer::after {
  content: "\e041\e041"; }

.fasds.fa-user-doctor-message::after, .fa-sharp-duotone.fa-user-doctor-message::after {
  content: "\f82e\f82e"; }

.fasds.fa-user-md-chat::after, .fa-sharp-duotone.fa-user-md-chat::after {
  content: "\f82e\f82e"; }

.fasds.fa-bahai::after, .fa-sharp-duotone.fa-bahai::after {
  content: "\f666\f666"; }

.fasds.fa-haykal::after, .fa-sharp-duotone.fa-haykal::after {
  content: "\f666\f666"; }

.fasds.fa-lighthouse::after, .fa-sharp-duotone.fa-lighthouse::after {
  content: "\e612\e612"; }

.fasds.fa-amp-guitar::after, .fa-sharp-duotone.fa-amp-guitar::after {
  content: "\f8a1\f8a1"; }

.fasds.fa-sd-card::after, .fa-sharp-duotone.fa-sd-card::after {
  content: "\f7c2\f7c2"; }

.fasds.fa-volume-slash::after, .fa-sharp-duotone.fa-volume-slash::after {
  content: "\f2e2\f2e2"; }

.fasds.fa-border-bottom::after, .fa-sharp-duotone.fa-border-bottom::after {
  content: "\f84d\f84d"; }

.fasds.fa-wifi-weak::after, .fa-sharp-duotone.fa-wifi-weak::after {
  content: "\f6aa\f6aa"; }

.fasds.fa-wifi-1::after, .fa-sharp-duotone.fa-wifi-1::after {
  content: "\f6aa\f6aa"; }

.fasds.fa-dragon::after, .fa-sharp-duotone.fa-dragon::after {
  content: "\f6d5\f6d5"; }

.fasds.fa-shoe-prints::after, .fa-sharp-duotone.fa-shoe-prints::after {
  content: "\f54b\f54b"; }

.fasds.fa-circle-plus::after, .fa-sharp-duotone.fa-circle-plus::after {
  content: "\f055\f055"; }

.fasds.fa-plus-circle::after, .fa-sharp-duotone.fa-plus-circle::after {
  content: "\f055\f055"; }

.fasds.fa-face-grin-tongue-wink::after, .fa-sharp-duotone.fa-face-grin-tongue-wink::after {
  content: "\f58b\f58b"; }

.fasds.fa-grin-tongue-wink::after, .fa-sharp-duotone.fa-grin-tongue-wink::after {
  content: "\f58b\f58b"; }

.fasds.fa-hand-holding::after, .fa-sharp-duotone.fa-hand-holding::after {
  content: "\f4bd\f4bd"; }

.fasds.fa-plug-circle-exclamation::after, .fa-sharp-duotone.fa-plug-circle-exclamation::after {
  content: "\e55d\e55d"; }

.fasds.fa-link-slash::after, .fa-sharp-duotone.fa-link-slash::after {
  content: "\f127\f127"; }

.fasds.fa-chain-broken::after, .fa-sharp-duotone.fa-chain-broken::after {
  content: "\f127\f127"; }

.fasds.fa-chain-slash::after, .fa-sharp-duotone.fa-chain-slash::after {
  content: "\f127\f127"; }

.fasds.fa-unlink::after, .fa-sharp-duotone.fa-unlink::after {
  content: "\f127\f127"; }

.fasds.fa-clone::after, .fa-sharp-duotone.fa-clone::after {
  content: "\f24d\f24d"; }

.fasds.fa-person-walking-arrow-loop-left::after, .fa-sharp-duotone.fa-person-walking-arrow-loop-left::after {
  content: "\e551\e551"; }

.fasds.fa-arrow-up-z-a::after, .fa-sharp-duotone.fa-arrow-up-z-a::after {
  content: "\f882\f882"; }

.fasds.fa-sort-alpha-up-alt::after, .fa-sharp-duotone.fa-sort-alpha-up-alt::after {
  content: "\f882\f882"; }

.fasds.fa-fire-flame-curved::after, .fa-sharp-duotone.fa-fire-flame-curved::after {
  content: "\f7e4\f7e4"; }

.fasds.fa-fire-alt::after, .fa-sharp-duotone.fa-fire-alt::after {
  content: "\f7e4\f7e4"; }

.fasds.fa-tornado::after, .fa-sharp-duotone.fa-tornado::after {
  content: "\f76f\f76f"; }

.fasds.fa-file-circle-plus::after, .fa-sharp-duotone.fa-file-circle-plus::after {
  content: "\e494\e494"; }

.fasds.fa-delete-right::after, .fa-sharp-duotone.fa-delete-right::after {
  content: "\e154\e154"; }

.fasds.fa-book-quran::after, .fa-sharp-duotone.fa-book-quran::after {
  content: "\f687\f687"; }

.fasds.fa-quran::after, .fa-sharp-duotone.fa-quran::after {
  content: "\f687\f687"; }

.fasds.fa-circle-quarter::after, .fa-sharp-duotone.fa-circle-quarter::after {
  content: "\e11f\e11f"; }

.fasds.fa-anchor::after, .fa-sharp-duotone.fa-anchor::after {
  content: "\f13d\f13d"; }

.fasds.fa-border-all::after, .fa-sharp-duotone.fa-border-all::after {
  content: "\f84c\f84c"; }

.fasds.fa-function::after, .fa-sharp-duotone.fa-function::after {
  content: "\f661\f661"; }

.fasds.fa-face-angry::after, .fa-sharp-duotone.fa-face-angry::after {
  content: "\f556\f556"; }

.fasds.fa-angry::after, .fa-sharp-duotone.fa-angry::after {
  content: "\f556\f556"; }

.fasds.fa-people-simple::after, .fa-sharp-duotone.fa-people-simple::after {
  content: "\e21b\e21b"; }

.fasds.fa-cookie-bite::after, .fa-sharp-duotone.fa-cookie-bite::after {
  content: "\f564\f564"; }

.fasds.fa-arrow-trend-down::after, .fa-sharp-duotone.fa-arrow-trend-down::after {
  content: "\e097\e097"; }

.fasds.fa-rss::after, .fa-sharp-duotone.fa-rss::after {
  content: "\f09e\f09e"; }

.fasds.fa-feed::after, .fa-sharp-duotone.fa-feed::after {
  content: "\f09e\f09e"; }

.fasds.fa-face-monocle::after, .fa-sharp-duotone.fa-face-monocle::after {
  content: "\e380\e380"; }

.fasds.fa-draw-polygon::after, .fa-sharp-duotone.fa-draw-polygon::after {
  content: "\f5ee\f5ee"; }

.fasds.fa-scale-balanced::after, .fa-sharp-duotone.fa-scale-balanced::after {
  content: "\f24e\f24e"; }

.fasds.fa-balance-scale::after, .fa-sharp-duotone.fa-balance-scale::after {
  content: "\f24e\f24e"; }

.fasds.fa-calendar-lines::after, .fa-sharp-duotone.fa-calendar-lines::after {
  content: "\e0d5\e0d5"; }

.fasds.fa-calendar-note::after, .fa-sharp-duotone.fa-calendar-note::after {
  content: "\e0d5\e0d5"; }

.fasds.fa-arrow-down-big-small::after, .fa-sharp-duotone.fa-arrow-down-big-small::after {
  content: "\f88c\f88c"; }

.fasds.fa-sort-size-down::after, .fa-sharp-duotone.fa-sort-size-down::after {
  content: "\f88c\f88c"; }

.fasds.fa-gauge-simple-high::after, .fa-sharp-duotone.fa-gauge-simple-high::after {
  content: "\f62a\f62a"; }

.fasds.fa-tachometer::after, .fa-sharp-duotone.fa-tachometer::after {
  content: "\f62a\f62a"; }

.fasds.fa-tachometer-fast::after, .fa-sharp-duotone.fa-tachometer-fast::after {
  content: "\f62a\f62a"; }

.fasds.fa-do-not-enter::after, .fa-sharp-duotone.fa-do-not-enter::after {
  content: "\f5ec\f5ec"; }

.fasds.fa-shower::after, .fa-sharp-duotone.fa-shower::after {
  content: "\f2cc\f2cc"; }

.fasds.fa-dice-d8::after, .fa-sharp-duotone.fa-dice-d8::after {
  content: "\f6d2\f6d2"; }

.fasds.fa-desktop::after, .fa-sharp-duotone.fa-desktop::after {
  content: "\f390\f390"; }

.fasds.fa-desktop-alt::after, .fa-sharp-duotone.fa-desktop-alt::after {
  content: "\f390\f390"; }

.fasds.fa-m::after, .fa-sharp-duotone.fa-m::after {
  content: "\4d\4d"; }

.fasds.fa-spinner-scale::after, .fa-sharp-duotone.fa-spinner-scale::after {
  content: "\e62a\e62a"; }

.fasds.fa-grip-dots-vertical::after, .fa-sharp-duotone.fa-grip-dots-vertical::after {
  content: "\e411\e411"; }

.fasds.fa-face-viewfinder::after, .fa-sharp-duotone.fa-face-viewfinder::after {
  content: "\e2ff\e2ff"; }

.fasds.fa-soft-serve::after, .fa-sharp-duotone.fa-soft-serve::after {
  content: "\e400\e400"; }

.fasds.fa-creemee::after, .fa-sharp-duotone.fa-creemee::after {
  content: "\e400\e400"; }

.fasds.fa-h5::after, .fa-sharp-duotone.fa-h5::after {
  content: "\e412\e412"; }

.fasds.fa-hand-back-point-down::after, .fa-sharp-duotone.fa-hand-back-point-down::after {
  content: "\e19e\e19e"; }

.fasds.fa-table-list::after, .fa-sharp-duotone.fa-table-list::after {
  content: "\f00b\f00b"; }

.fasds.fa-th-list::after, .fa-sharp-duotone.fa-th-list::after {
  content: "\f00b\f00b"; }

.fasds.fa-basket-shopping-minus::after, .fa-sharp-duotone.fa-basket-shopping-minus::after {
  content: "\e652\e652"; }

.fasds.fa-comment-sms::after, .fa-sharp-duotone.fa-comment-sms::after {
  content: "\f7cd\f7cd"; }

.fasds.fa-sms::after, .fa-sharp-duotone.fa-sms::after {
  content: "\f7cd\f7cd"; }

.fasds.fa-rectangle::after, .fa-sharp-duotone.fa-rectangle::after {
  content: "\f2fa\f2fa"; }

.fasds.fa-rectangle-landscape::after, .fa-sharp-duotone.fa-rectangle-landscape::after {
  content: "\f2fa\f2fa"; }

.fasds.fa-clipboard-list-check::after, .fa-sharp-duotone.fa-clipboard-list-check::after {
  content: "\f737\f737"; }

.fasds.fa-turkey::after, .fa-sharp-duotone.fa-turkey::after {
  content: "\f725\f725"; }

.fasds.fa-book::after, .fa-sharp-duotone.fa-book::after {
  content: "\f02d\f02d"; }

.fasds.fa-user-plus::after, .fa-sharp-duotone.fa-user-plus::after {
  content: "\f234\f234"; }

.fasds.fa-ice-skate::after, .fa-sharp-duotone.fa-ice-skate::after {
  content: "\f7ac\f7ac"; }

.fasds.fa-check::after, .fa-sharp-duotone.fa-check::after {
  content: "\f00c\f00c"; }

.fasds.fa-battery-three-quarters::after, .fa-sharp-duotone.fa-battery-three-quarters::after {
  content: "\f241\f241"; }

.fasds.fa-battery-4::after, .fa-sharp-duotone.fa-battery-4::after {
  content: "\f241\f241"; }

.fasds.fa-tomato::after, .fa-sharp-duotone.fa-tomato::after {
  content: "\e330\e330"; }

.fasds.fa-sword-laser::after, .fa-sharp-duotone.fa-sword-laser::after {
  content: "\e03b\e03b"; }

.fasds.fa-house-circle-check::after, .fa-sharp-duotone.fa-house-circle-check::after {
  content: "\e509\e509"; }

.fasds.fa-buildings::after, .fa-sharp-duotone.fa-buildings::after {
  content: "\e0cc\e0cc"; }

.fasds.fa-angle-left::after, .fa-sharp-duotone.fa-angle-left::after {
  content: "\f104\f104"; }

.fasds.fa-cart-flatbed-boxes::after, .fa-sharp-duotone.fa-cart-flatbed-boxes::after {
  content: "\f475\f475"; }

.fasds.fa-dolly-flatbed-alt::after, .fa-sharp-duotone.fa-dolly-flatbed-alt::after {
  content: "\f475\f475"; }

.fasds.fa-diagram-successor::after, .fa-sharp-duotone.fa-diagram-successor::after {
  content: "\e47a\e47a"; }

.fasds.fa-truck-arrow-right::after, .fa-sharp-duotone.fa-truck-arrow-right::after {
  content: "\e58b\e58b"; }

.fasds.fa-square-w::after, .fa-sharp-duotone.fa-square-w::after {
  content: "\e285\e285"; }

.fasds.fa-arrows-split-up-and-left::after, .fa-sharp-duotone.fa-arrows-split-up-and-left::after {
  content: "\e4bc\e4bc"; }

.fasds.fa-lamp::after, .fa-sharp-duotone.fa-lamp::after {
  content: "\f4ca\f4ca"; }

.fasds.fa-airplay::after, .fa-sharp-duotone.fa-airplay::after {
  content: "\e089\e089"; }

.fasds.fa-hand-fist::after, .fa-sharp-duotone.fa-hand-fist::after {
  content: "\f6de\f6de"; }

.fasds.fa-fist-raised::after, .fa-sharp-duotone.fa-fist-raised::after {
  content: "\f6de\f6de"; }

.fasds.fa-shield-quartered::after, .fa-sharp-duotone.fa-shield-quartered::after {
  content: "\e575\e575"; }

.fasds.fa-slash-forward::after, .fa-sharp-duotone.fa-slash-forward::after {
  content: "\2f\2f"; }

.fasds.fa-location-pen::after, .fa-sharp-duotone.fa-location-pen::after {
  content: "\f607\f607"; }

.fasds.fa-map-marker-edit::after, .fa-sharp-duotone.fa-map-marker-edit::after {
  content: "\f607\f607"; }

.fasds.fa-cloud-moon::after, .fa-sharp-duotone.fa-cloud-moon::after {
  content: "\f6c3\f6c3"; }

.fasds.fa-pot-food::after, .fa-sharp-duotone.fa-pot-food::after {
  content: "\e43f\e43f"; }

.fasds.fa-briefcase::after, .fa-sharp-duotone.fa-briefcase::after {
  content: "\f0b1\f0b1"; }

.fasds.fa-person-falling::after, .fa-sharp-duotone.fa-person-falling::after {
  content: "\e546\e546"; }

.fasds.fa-image-portrait::after, .fa-sharp-duotone.fa-image-portrait::after {
  content: "\f3e0\f3e0"; }

.fasds.fa-portrait::after, .fa-sharp-duotone.fa-portrait::after {
  content: "\f3e0\f3e0"; }

.fasds.fa-user-tag::after, .fa-sharp-duotone.fa-user-tag::after {
  content: "\f507\f507"; }

.fasds.fa-rug::after, .fa-sharp-duotone.fa-rug::after {
  content: "\e569\e569"; }

.fasds.fa-print-slash::after, .fa-sharp-duotone.fa-print-slash::after {
  content: "\f686\f686"; }

.fasds.fa-earth-europe::after, .fa-sharp-duotone.fa-earth-europe::after {
  content: "\f7a2\f7a2"; }

.fasds.fa-globe-europe::after, .fa-sharp-duotone.fa-globe-europe::after {
  content: "\f7a2\f7a2"; }

.fasds.fa-cart-flatbed-suitcase::after, .fa-sharp-duotone.fa-cart-flatbed-suitcase::after {
  content: "\f59d\f59d"; }

.fasds.fa-luggage-cart::after, .fa-sharp-duotone.fa-luggage-cart::after {
  content: "\f59d\f59d"; }

.fasds.fa-hand-back-point-ribbon::after, .fa-sharp-duotone.fa-hand-back-point-ribbon::after {
  content: "\e1a0\e1a0"; }

.fasds.fa-rectangle-xmark::after, .fa-sharp-duotone.fa-rectangle-xmark::after {
  content: "\f410\f410"; }

.fasds.fa-rectangle-times::after, .fa-sharp-duotone.fa-rectangle-times::after {
  content: "\f410\f410"; }

.fasds.fa-times-rectangle::after, .fa-sharp-duotone.fa-times-rectangle::after {
  content: "\f410\f410"; }

.fasds.fa-window-close::after, .fa-sharp-duotone.fa-window-close::after {
  content: "\f410\f410"; }

.fasds.fa-tire-rugged::after, .fa-sharp-duotone.fa-tire-rugged::after {
  content: "\f634\f634"; }

.fasds.fa-lightbulb-dollar::after, .fa-sharp-duotone.fa-lightbulb-dollar::after {
  content: "\f670\f670"; }

.fasds.fa-cowbell::after, .fa-sharp-duotone.fa-cowbell::after {
  content: "\f8b3\f8b3"; }

.fasds.fa-baht-sign::after, .fa-sharp-duotone.fa-baht-sign::after {
  content: "\e0ac\e0ac"; }

.fasds.fa-corner::after, .fa-sharp-duotone.fa-corner::after {
  content: "\e3fe\e3fe"; }

.fasds.fa-chevrons-right::after, .fa-sharp-duotone.fa-chevrons-right::after {
  content: "\f324\f324"; }

.fasds.fa-chevron-double-right::after, .fa-sharp-duotone.fa-chevron-double-right::after {
  content: "\f324\f324"; }

.fasds.fa-book-open::after, .fa-sharp-duotone.fa-book-open::after {
  content: "\f518\f518"; }

.fasds.fa-book-journal-whills::after, .fa-sharp-duotone.fa-book-journal-whills::after {
  content: "\f66a\f66a"; }

.fasds.fa-journal-whills::after, .fa-sharp-duotone.fa-journal-whills::after {
  content: "\f66a\f66a"; }

.fasds.fa-inhaler::after, .fa-sharp-duotone.fa-inhaler::after {
  content: "\f5f9\f5f9"; }

.fasds.fa-handcuffs::after, .fa-sharp-duotone.fa-handcuffs::after {
  content: "\e4f8\e4f8"; }

.fasds.fa-snake::after, .fa-sharp-duotone.fa-snake::after {
  content: "\f716\f716"; }

.fasds.fa-triangle-exclamation::after, .fa-sharp-duotone.fa-triangle-exclamation::after {
  content: "\f071\f071"; }

.fasds.fa-exclamation-triangle::after, .fa-sharp-duotone.fa-exclamation-triangle::after {
  content: "\f071\f071"; }

.fasds.fa-warning::after, .fa-sharp-duotone.fa-warning::after {
  content: "\f071\f071"; }

.fasds.fa-note-medical::after, .fa-sharp-duotone.fa-note-medical::after {
  content: "\e200\e200"; }

.fasds.fa-database::after, .fa-sharp-duotone.fa-database::after {
  content: "\f1c0\f1c0"; }

.fasds.fa-down-left::after, .fa-sharp-duotone.fa-down-left::after {
  content: "\e16a\e16a"; }

.fasds.fa-share::after, .fa-sharp-duotone.fa-share::after {
  content: "\f064\f064"; }

.fasds.fa-mail-forward::after, .fa-sharp-duotone.fa-mail-forward::after {
  content: "\f064\f064"; }

.fasds.fa-face-thinking::after, .fa-sharp-duotone.fa-face-thinking::after {
  content: "\e39b\e39b"; }

.fasds.fa-turn-down-right::after, .fa-sharp-duotone.fa-turn-down-right::after {
  content: "\e455\e455"; }

.fasds.fa-bottle-droplet::after, .fa-sharp-duotone.fa-bottle-droplet::after {
  content: "\e4c4\e4c4"; }

.fasds.fa-mask-face::after, .fa-sharp-duotone.fa-mask-face::after {
  content: "\e1d7\e1d7"; }

.fasds.fa-hill-rockslide::after, .fa-sharp-duotone.fa-hill-rockslide::after {
  content: "\e508\e508"; }

.fasds.fa-scanner-keyboard::after, .fa-sharp-duotone.fa-scanner-keyboard::after {
  content: "\f489\f489"; }

.fasds.fa-circle-o::after, .fa-sharp-duotone.fa-circle-o::after {
  content: "\e119\e119"; }

.fasds.fa-grid-horizontal::after, .fa-sharp-duotone.fa-grid-horizontal::after {
  content: "\e307\e307"; }

.fasds.fa-message-dollar::after, .fa-sharp-duotone.fa-message-dollar::after {
  content: "\f650\f650"; }

.fasds.fa-comment-alt-dollar::after, .fa-sharp-duotone.fa-comment-alt-dollar::after {
  content: "\f650\f650"; }

.fasds.fa-right-left::after, .fa-sharp-duotone.fa-right-left::after {
  content: "\f362\f362"; }

.fasds.fa-exchange-alt::after, .fa-sharp-duotone.fa-exchange-alt::after {
  content: "\f362\f362"; }

.fasds.fa-columns-3::after, .fa-sharp-duotone.fa-columns-3::after {
  content: "\e361\e361"; }

.fasds.fa-paper-plane::after, .fa-sharp-duotone.fa-paper-plane::after {
  content: "\f1d8\f1d8"; }

.fasds.fa-road-circle-exclamation::after, .fa-sharp-duotone.fa-road-circle-exclamation::after {
  content: "\e565\e565"; }

.fasds.fa-dungeon::after, .fa-sharp-duotone.fa-dungeon::after {
  content: "\f6d9\f6d9"; }

.fasds.fa-hand-holding-box::after, .fa-sharp-duotone.fa-hand-holding-box::after {
  content: "\f47b\f47b"; }

.fasds.fa-input-text::after, .fa-sharp-duotone.fa-input-text::after {
  content: "\e1bf\e1bf"; }

.fasds.fa-window-flip::after, .fa-sharp-duotone.fa-window-flip::after {
  content: "\f40f\f40f"; }

.fasds.fa-window-alt::after, .fa-sharp-duotone.fa-window-alt::after {
  content: "\f40f\f40f"; }

.fasds.fa-align-right::after, .fa-sharp-duotone.fa-align-right::after {
  content: "\f038\f038"; }

.fasds.fa-scanner-gun::after, .fa-sharp-duotone.fa-scanner-gun::after {
  content: "\f488\f488"; }

.fasds.fa-scanner::after, .fa-sharp-duotone.fa-scanner::after {
  content: "\f488\f488"; }

.fasds.fa-tire::after, .fa-sharp-duotone.fa-tire::after {
  content: "\f631\f631"; }

.fasds.fa-engine::after, .fa-sharp-duotone.fa-engine::after {
  content: "\e16e\e16e"; }

.fasds.fa-money-bill-1-wave::after, .fa-sharp-duotone.fa-money-bill-1-wave::after {
  content: "\f53b\f53b"; }

.fasds.fa-money-bill-wave-alt::after, .fa-sharp-duotone.fa-money-bill-wave-alt::after {
  content: "\f53b\f53b"; }

.fasds.fa-life-ring::after, .fa-sharp-duotone.fa-life-ring::after {
  content: "\f1cd\f1cd"; }

.fasds.fa-hands::after, .fa-sharp-duotone.fa-hands::after {
  content: "\f2a7\f2a7"; }

.fasds.fa-sign-language::after, .fa-sharp-duotone.fa-sign-language::after {
  content: "\f2a7\f2a7"; }

.fasds.fa-signing::after, .fa-sharp-duotone.fa-signing::after {
  content: "\f2a7\f2a7"; }

.fasds.fa-circle-caret-right::after, .fa-sharp-duotone.fa-circle-caret-right::after {
  content: "\f330\f330"; }

.fasds.fa-caret-circle-right::after, .fa-sharp-duotone.fa-caret-circle-right::after {
  content: "\f330\f330"; }

.fasds.fa-turn-left::after, .fa-sharp-duotone.fa-turn-left::after {
  content: "\e636\e636"; }

.fasds.fa-wheat::after, .fa-sharp-duotone.fa-wheat::after {
  content: "\f72d\f72d"; }

.fasds.fa-file-spreadsheet::after, .fa-sharp-duotone.fa-file-spreadsheet::after {
  content: "\f65b\f65b"; }

.fasds.fa-audio-description-slash::after, .fa-sharp-duotone.fa-audio-description-slash::after {
  content: "\e0a8\e0a8"; }

.fasds.fa-bell-ring::after, .fa-sharp-duotone.fa-bell-ring::after {
  content: "\e62c\e62c"; }

.fasds.fa-calendar-day::after, .fa-sharp-duotone.fa-calendar-day::after {
  content: "\f783\f783"; }

.fasds.fa-water-ladder::after, .fa-sharp-duotone.fa-water-ladder::after {
  content: "\f5c5\f5c5"; }

.fasds.fa-ladder-water::after, .fa-sharp-duotone.fa-ladder-water::after {
  content: "\f5c5\f5c5"; }

.fasds.fa-swimming-pool::after, .fa-sharp-duotone.fa-swimming-pool::after {
  content: "\f5c5\f5c5"; }

.fasds.fa-arrows-up-down::after, .fa-sharp-duotone.fa-arrows-up-down::after {
  content: "\f07d\f07d"; }

.fasds.fa-arrows-v::after, .fa-sharp-duotone.fa-arrows-v::after {
  content: "\f07d\f07d"; }

.fasds.fa-chess-pawn-piece::after, .fa-sharp-duotone.fa-chess-pawn-piece::after {
  content: "\f444\f444"; }

.fasds.fa-chess-pawn-alt::after, .fa-sharp-duotone.fa-chess-pawn-alt::after {
  content: "\f444\f444"; }

.fasds.fa-face-grimace::after, .fa-sharp-duotone.fa-face-grimace::after {
  content: "\f57f\f57f"; }

.fasds.fa-grimace::after, .fa-sharp-duotone.fa-grimace::after {
  content: "\f57f\f57f"; }

.fasds.fa-wheelchair-move::after, .fa-sharp-duotone.fa-wheelchair-move::after {
  content: "\e2ce\e2ce"; }

.fasds.fa-wheelchair-alt::after, .fa-sharp-duotone.fa-wheelchair-alt::after {
  content: "\e2ce\e2ce"; }

.fasds.fa-turn-down::after, .fa-sharp-duotone.fa-turn-down::after {
  content: "\f3be\f3be"; }

.fasds.fa-level-down-alt::after, .fa-sharp-duotone.fa-level-down-alt::after {
  content: "\f3be\f3be"; }

.fasds.fa-square-s::after, .fa-sharp-duotone.fa-square-s::after {
  content: "\e27d\e27d"; }

.fasds.fa-rectangle-barcode::after, .fa-sharp-duotone.fa-rectangle-barcode::after {
  content: "\f463\f463"; }

.fasds.fa-barcode-alt::after, .fa-sharp-duotone.fa-barcode-alt::after {
  content: "\f463\f463"; }

.fasds.fa-person-walking-arrow-right::after, .fa-sharp-duotone.fa-person-walking-arrow-right::after {
  content: "\e552\e552"; }

.fasds.fa-square-envelope::after, .fa-sharp-duotone.fa-square-envelope::after {
  content: "\f199\f199"; }

.fasds.fa-envelope-square::after, .fa-sharp-duotone.fa-envelope-square::after {
  content: "\f199\f199"; }

.fasds.fa-dice::after, .fa-sharp-duotone.fa-dice::after {
  content: "\f522\f522"; }

.fasds.fa-unicorn::after, .fa-sharp-duotone.fa-unicorn::after {
  content: "\f727\f727"; }

.fasds.fa-bowling-ball::after, .fa-sharp-duotone.fa-bowling-ball::after {
  content: "\f436\f436"; }

.fasds.fa-pompebled::after, .fa-sharp-duotone.fa-pompebled::after {
  content: "\e43d\e43d"; }

.fasds.fa-brain::after, .fa-sharp-duotone.fa-brain::after {
  content: "\f5dc\f5dc"; }

.fasds.fa-watch-smart::after, .fa-sharp-duotone.fa-watch-smart::after {
  content: "\e2cc\e2cc"; }

.fasds.fa-book-user::after, .fa-sharp-duotone.fa-book-user::after {
  content: "\f7e7\f7e7"; }

.fasds.fa-sensor-cloud::after, .fa-sharp-duotone.fa-sensor-cloud::after {
  content: "\e02c\e02c"; }

.fasds.fa-sensor-smoke::after, .fa-sharp-duotone.fa-sensor-smoke::after {
  content: "\e02c\e02c"; }

.fasds.fa-clapperboard-play::after, .fa-sharp-duotone.fa-clapperboard-play::after {
  content: "\e132\e132"; }

.fasds.fa-bandage::after, .fa-sharp-duotone.fa-bandage::after {
  content: "\f462\f462"; }

.fasds.fa-band-aid::after, .fa-sharp-duotone.fa-band-aid::after {
  content: "\f462\f462"; }

.fasds.fa-calendar-minus::after, .fa-sharp-duotone.fa-calendar-minus::after {
  content: "\f272\f272"; }

.fasds.fa-circle-xmark::after, .fa-sharp-duotone.fa-circle-xmark::after {
  content: "\f057\f057"; }

.fasds.fa-times-circle::after, .fa-sharp-duotone.fa-times-circle::after {
  content: "\f057\f057"; }

.fasds.fa-xmark-circle::after, .fa-sharp-duotone.fa-xmark-circle::after {
  content: "\f057\f057"; }

.fasds.fa-circle-4::after, .fa-sharp-duotone.fa-circle-4::after {
  content: "\e0f1\e0f1"; }

.fasds.fa-gifts::after, .fa-sharp-duotone.fa-gifts::after {
  content: "\f79c\f79c"; }

.fasds.fa-album-collection::after, .fa-sharp-duotone.fa-album-collection::after {
  content: "\f8a0\f8a0"; }

.fasds.fa-hotel::after, .fa-sharp-duotone.fa-hotel::after {
  content: "\f594\f594"; }

.fasds.fa-earth-asia::after, .fa-sharp-duotone.fa-earth-asia::after {
  content: "\f57e\f57e"; }

.fasds.fa-globe-asia::after, .fa-sharp-duotone.fa-globe-asia::after {
  content: "\f57e\f57e"; }

.fasds.fa-id-card-clip::after, .fa-sharp-duotone.fa-id-card-clip::after {
  content: "\f47f\f47f"; }

.fasds.fa-id-card-alt::after, .fa-sharp-duotone.fa-id-card-alt::after {
  content: "\f47f\f47f"; }

.fasds.fa-magnifying-glass-plus::after, .fa-sharp-duotone.fa-magnifying-glass-plus::after {
  content: "\f00e\f00e"; }

.fasds.fa-search-plus::after, .fa-sharp-duotone.fa-search-plus::after {
  content: "\f00e\f00e"; }

.fasds.fa-thumbs-up::after, .fa-sharp-duotone.fa-thumbs-up::after {
  content: "\f164\f164"; }

.fasds.fa-cloud-showers::after, .fa-sharp-duotone.fa-cloud-showers::after {
  content: "\f73f\f73f"; }

.fasds.fa-user-clock::after, .fa-sharp-duotone.fa-user-clock::after {
  content: "\f4fd\f4fd"; }

.fasds.fa-onion::after, .fa-sharp-duotone.fa-onion::after {
  content: "\e427\e427"; }

.fasds.fa-clock-twelve-thirty::after, .fa-sharp-duotone.fa-clock-twelve-thirty::after {
  content: "\e359\e359"; }

.fasds.fa-arrow-down-to-dotted-line::after, .fa-sharp-duotone.fa-arrow-down-to-dotted-line::after {
  content: "\e095\e095"; }

.fasds.fa-hand-dots::after, .fa-sharp-duotone.fa-hand-dots::after {
  content: "\f461\f461"; }

.fasds.fa-allergies::after, .fa-sharp-duotone.fa-allergies::after {
  content: "\f461\f461"; }

.fasds.fa-file-invoice::after, .fa-sharp-duotone.fa-file-invoice::after {
  content: "\f570\f570"; }

.fasds.fa-window-minimize::after, .fa-sharp-duotone.fa-window-minimize::after {
  content: "\f2d1\f2d1"; }

.fasds.fa-rectangle-wide::after, .fa-sharp-duotone.fa-rectangle-wide::after {
  content: "\f2fc\f2fc"; }

.fasds.fa-comment-arrow-up::after, .fa-sharp-duotone.fa-comment-arrow-up::after {
  content: "\e144\e144"; }

.fasds.fa-garlic::after, .fa-sharp-duotone.fa-garlic::after {
  content: "\e40e\e40e"; }

.fasds.fa-mug-saucer::after, .fa-sharp-duotone.fa-mug-saucer::after {
  content: "\f0f4\f0f4"; }

.fasds.fa-coffee::after, .fa-sharp-duotone.fa-coffee::after {
  content: "\f0f4\f0f4"; }

.fasds.fa-brush::after, .fa-sharp-duotone.fa-brush::after {
  content: "\f55d\f55d"; }

.fasds.fa-tree-decorated::after, .fa-sharp-duotone.fa-tree-decorated::after {
  content: "\f7dc\f7dc"; }

.fasds.fa-mask::after, .fa-sharp-duotone.fa-mask::after {
  content: "\f6fa\f6fa"; }

.fasds.fa-calendar-heart::after, .fa-sharp-duotone.fa-calendar-heart::after {
  content: "\e0d3\e0d3"; }

.fasds.fa-magnifying-glass-minus::after, .fa-sharp-duotone.fa-magnifying-glass-minus::after {
  content: "\f010\f010"; }

.fasds.fa-search-minus::after, .fa-sharp-duotone.fa-search-minus::after {
  content: "\f010\f010"; }

.fasds.fa-flower::after, .fa-sharp-duotone.fa-flower::after {
  content: "\f7ff\f7ff"; }

.fasds.fa-arrow-down-from-arc::after, .fa-sharp-duotone.fa-arrow-down-from-arc::after {
  content: "\e614\e614"; }

.fasds.fa-right-left-large::after, .fa-sharp-duotone.fa-right-left-large::after {
  content: "\e5e1\e5e1"; }

.fasds.fa-ruler-vertical::after, .fa-sharp-duotone.fa-ruler-vertical::after {
  content: "\f548\f548"; }

.fasds.fa-circles-overlap::after, .fa-sharp-duotone.fa-circles-overlap::after {
  content: "\e600\e600"; }

.fasds.fa-user-large::after, .fa-sharp-duotone.fa-user-large::after {
  content: "\f406\f406"; }

.fasds.fa-user-alt::after, .fa-sharp-duotone.fa-user-alt::after {
  content: "\f406\f406"; }

.fasds.fa-starship-freighter::after, .fa-sharp-duotone.fa-starship-freighter::after {
  content: "\e03a\e03a"; }

.fasds.fa-train-tram::after, .fa-sharp-duotone.fa-train-tram::after {
  content: "\e5b4\e5b4"; }

.fasds.fa-bridge-suspension::after, .fa-sharp-duotone.fa-bridge-suspension::after {
  content: "\e4cd\e4cd"; }

.fasds.fa-trash-check::after, .fa-sharp-duotone.fa-trash-check::after {
  content: "\e2af\e2af"; }

.fasds.fa-user-nurse::after, .fa-sharp-duotone.fa-user-nurse::after {
  content: "\f82f\f82f"; }

.fasds.fa-boombox::after, .fa-sharp-duotone.fa-boombox::after {
  content: "\f8a5\f8a5"; }

.fasds.fa-syringe::after, .fa-sharp-duotone.fa-syringe::after {
  content: "\f48e\f48e"; }

.fasds.fa-cloud-sun::after, .fa-sharp-duotone.fa-cloud-sun::after {
  content: "\f6c4\f6c4"; }

.fasds.fa-shield-exclamation::after, .fa-sharp-duotone.fa-shield-exclamation::after {
  content: "\e247\e247"; }

.fasds.fa-stopwatch-20::after, .fa-sharp-duotone.fa-stopwatch-20::after {
  content: "\e06f\e06f"; }

.fasds.fa-square-full::after, .fa-sharp-duotone.fa-square-full::after {
  content: "\f45c\f45c"; }

.fasds.fa-grip-dots::after, .fa-sharp-duotone.fa-grip-dots::after {
  content: "\e410\e410"; }

.fasds.fa-comment-exclamation::after, .fa-sharp-duotone.fa-comment-exclamation::after {
  content: "\f4af\f4af"; }

.fasds.fa-pen-swirl::after, .fa-sharp-duotone.fa-pen-swirl::after {
  content: "\e214\e214"; }

.fasds.fa-falafel::after, .fa-sharp-duotone.fa-falafel::after {
  content: "\e40a\e40a"; }

.fasds.fa-circle-2::after, .fa-sharp-duotone.fa-circle-2::after {
  content: "\e0ef\e0ef"; }

.fasds.fa-magnet::after, .fa-sharp-duotone.fa-magnet::after {
  content: "\f076\f076"; }

.fasds.fa-jar::after, .fa-sharp-duotone.fa-jar::after {
  content: "\e516\e516"; }

.fasds.fa-gramophone::after, .fa-sharp-duotone.fa-gramophone::after {
  content: "\f8bd\f8bd"; }

.fasds.fa-dice-d12::after, .fa-sharp-duotone.fa-dice-d12::after {
  content: "\f6ce\f6ce"; }

.fasds.fa-note-sticky::after, .fa-sharp-duotone.fa-note-sticky::after {
  content: "\f249\f249"; }

.fasds.fa-sticky-note::after, .fa-sharp-duotone.fa-sticky-note::after {
  content: "\f249\f249"; }

.fasds.fa-down::after, .fa-sharp-duotone.fa-down::after {
  content: "\f354\f354"; }

.fasds.fa-arrow-alt-down::after, .fa-sharp-duotone.fa-arrow-alt-down::after {
  content: "\f354\f354"; }

.fasds.fa-hundred-points::after, .fa-sharp-duotone.fa-hundred-points::after {
  content: "\e41c\e41c"; }

.fasds.fa-100::after, .fa-sharp-duotone.fa-100::after {
  content: "\e41c\e41c"; }

.fasds.fa-paperclip-vertical::after, .fa-sharp-duotone.fa-paperclip-vertical::after {
  content: "\e3c2\e3c2"; }

.fasds.fa-wind-warning::after, .fa-sharp-duotone.fa-wind-warning::after {
  content: "\f776\f776"; }

.fasds.fa-wind-circle-exclamation::after, .fa-sharp-duotone.fa-wind-circle-exclamation::after {
  content: "\f776\f776"; }

.fasds.fa-location-pin-slash::after, .fa-sharp-duotone.fa-location-pin-slash::after {
  content: "\f60c\f60c"; }

.fasds.fa-map-marker-slash::after, .fa-sharp-duotone.fa-map-marker-slash::after {
  content: "\f60c\f60c"; }

.fasds.fa-face-sad-sweat::after, .fa-sharp-duotone.fa-face-sad-sweat::after {
  content: "\e38a\e38a"; }

.fasds.fa-bug-slash::after, .fa-sharp-duotone.fa-bug-slash::after {
  content: "\e490\e490"; }

.fasds.fa-cupcake::after, .fa-sharp-duotone.fa-cupcake::after {
  content: "\e402\e402"; }

.fasds.fa-light-switch-off::after, .fa-sharp-duotone.fa-light-switch-off::after {
  content: "\e018\e018"; }

.fasds.fa-toggle-large-off::after, .fa-sharp-duotone.fa-toggle-large-off::after {
  content: "\e5b0\e5b0"; }

.fasds.fa-pen-fancy-slash::after, .fa-sharp-duotone.fa-pen-fancy-slash::after {
  content: "\e210\e210"; }

.fasds.fa-truck-container::after, .fa-sharp-duotone.fa-truck-container::after {
  content: "\f4dc\f4dc"; }

.fasds.fa-boot::after, .fa-sharp-duotone.fa-boot::after {
  content: "\f782\f782"; }

.fasds.fa-arrow-up-from-water-pump::after, .fa-sharp-duotone.fa-arrow-up-from-water-pump::after {
  content: "\e4b6\e4b6"; }

.fasds.fa-file-check::after, .fa-sharp-duotone.fa-file-check::after {
  content: "\f316\f316"; }

.fasds.fa-bone::after, .fa-sharp-duotone.fa-bone::after {
  content: "\f5d7\f5d7"; }

.fasds.fa-cards-blank::after, .fa-sharp-duotone.fa-cards-blank::after {
  content: "\e4df\e4df"; }

.fasds.fa-circle-3::after, .fa-sharp-duotone.fa-circle-3::after {
  content: "\e0f0\e0f0"; }

.fasds.fa-bench-tree::after, .fa-sharp-duotone.fa-bench-tree::after {
  content: "\e2e7\e2e7"; }

.fasds.fa-keyboard-brightness-low::after, .fa-sharp-duotone.fa-keyboard-brightness-low::after {
  content: "\e1c1\e1c1"; }

.fasds.fa-ski-boot-ski::after, .fa-sharp-duotone.fa-ski-boot-ski::after {
  content: "\e3cd\e3cd"; }

.fasds.fa-brain-circuit::after, .fa-sharp-duotone.fa-brain-circuit::after {
  content: "\e0c6\e0c6"; }

.fasds.fa-table-cells-row-unlock::after, .fa-sharp-duotone.fa-table-cells-row-unlock::after {
  content: "\e691\e691"; }

.fasds.fa-user-injured::after, .fa-sharp-duotone.fa-user-injured::after {
  content: "\f728\f728"; }

.fasds.fa-block-brick-fire::after, .fa-sharp-duotone.fa-block-brick-fire::after {
  content: "\e3dc\e3dc"; }

.fasds.fa-firewall::after, .fa-sharp-duotone.fa-firewall::after {
  content: "\e3dc\e3dc"; }

.fasds.fa-face-sad-tear::after, .fa-sharp-duotone.fa-face-sad-tear::after {
  content: "\f5b4\f5b4"; }

.fasds.fa-sad-tear::after, .fa-sharp-duotone.fa-sad-tear::after {
  content: "\f5b4\f5b4"; }

.fasds.fa-plane::after, .fa-sharp-duotone.fa-plane::after {
  content: "\f072\f072"; }

.fasds.fa-tent-arrows-down::after, .fa-sharp-duotone.fa-tent-arrows-down::after {
  content: "\e581\e581"; }

.fasds.fa-exclamation::after, .fa-sharp-duotone.fa-exclamation::after {
  content: "\21\21"; }

.fasds.fa-arrows-spin::after, .fa-sharp-duotone.fa-arrows-spin::after {
  content: "\e4bb\e4bb"; }

.fasds.fa-face-smile-relaxed::after, .fa-sharp-duotone.fa-face-smile-relaxed::after {
  content: "\e392\e392"; }

.fasds.fa-comment-xmark::after, .fa-sharp-duotone.fa-comment-xmark::after {
  content: "\f4b5\f4b5"; }

.fasds.fa-comment-times::after, .fa-sharp-duotone.fa-comment-times::after {
  content: "\f4b5\f4b5"; }

.fasds.fa-print::after, .fa-sharp-duotone.fa-print::after {
  content: "\f02f\f02f"; }

.fasds.fa-turkish-lira-sign::after, .fa-sharp-duotone.fa-turkish-lira-sign::after {
  content: "\e2bb\e2bb"; }

.fasds.fa-try::after, .fa-sharp-duotone.fa-try::after {
  content: "\e2bb\e2bb"; }

.fasds.fa-turkish-lira::after, .fa-sharp-duotone.fa-turkish-lira::after {
  content: "\e2bb\e2bb"; }

.fasds.fa-face-nose-steam::after, .fa-sharp-duotone.fa-face-nose-steam::after {
  content: "\e382\e382"; }

.fasds.fa-circle-waveform-lines::after, .fa-sharp-duotone.fa-circle-waveform-lines::after {
  content: "\e12d\e12d"; }

.fasds.fa-waveform-circle::after, .fa-sharp-duotone.fa-waveform-circle::after {
  content: "\e12d\e12d"; }

.fasds.fa-dollar-sign::after, .fa-sharp-duotone.fa-dollar-sign::after {
  content: "\24\24"; }

.fasds.fa-dollar::after, .fa-sharp-duotone.fa-dollar::after {
  content: "\24\24"; }

.fasds.fa-usd::after, .fa-sharp-duotone.fa-usd::after {
  content: "\24\24"; }

.fasds.fa-ferris-wheel::after, .fa-sharp-duotone.fa-ferris-wheel::after {
  content: "\e174\e174"; }

.fasds.fa-computer-speaker::after, .fa-sharp-duotone.fa-computer-speaker::after {
  content: "\f8b2\f8b2"; }

.fasds.fa-skull-cow::after, .fa-sharp-duotone.fa-skull-cow::after {
  content: "\f8de\f8de"; }

.fasds.fa-x::after, .fa-sharp-duotone.fa-x::after {
  content: "\58\58"; }

.fasds.fa-magnifying-glass-dollar::after, .fa-sharp-duotone.fa-magnifying-glass-dollar::after {
  content: "\f688\f688"; }

.fasds.fa-search-dollar::after, .fa-sharp-duotone.fa-search-dollar::after {
  content: "\f688\f688"; }

.fasds.fa-users-gear::after, .fa-sharp-duotone.fa-users-gear::after {
  content: "\f509\f509"; }

.fasds.fa-users-cog::after, .fa-sharp-duotone.fa-users-cog::after {
  content: "\f509\f509"; }

.fasds.fa-person-military-pointing::after, .fa-sharp-duotone.fa-person-military-pointing::after {
  content: "\e54a\e54a"; }

.fasds.fa-building-columns::after, .fa-sharp-duotone.fa-building-columns::after {
  content: "\f19c\f19c"; }

.fasds.fa-bank::after, .fa-sharp-duotone.fa-bank::after {
  content: "\f19c\f19c"; }

.fasds.fa-institution::after, .fa-sharp-duotone.fa-institution::after {
  content: "\f19c\f19c"; }

.fasds.fa-museum::after, .fa-sharp-duotone.fa-museum::after {
  content: "\f19c\f19c"; }

.fasds.fa-university::after, .fa-sharp-duotone.fa-university::after {
  content: "\f19c\f19c"; }

.fasds.fa-circle-t::after, .fa-sharp-duotone.fa-circle-t::after {
  content: "\e124\e124"; }

.fasds.fa-sack::after, .fa-sharp-duotone.fa-sack::after {
  content: "\f81c\f81c"; }

.fasds.fa-grid-2::after, .fa-sharp-duotone.fa-grid-2::after {
  content: "\e196\e196"; }

.fasds.fa-camera-cctv::after, .fa-sharp-duotone.fa-camera-cctv::after {
  content: "\f8ac\f8ac"; }

.fasds.fa-cctv::after, .fa-sharp-duotone.fa-cctv::after {
  content: "\f8ac\f8ac"; }

.fasds.fa-umbrella::after, .fa-sharp-duotone.fa-umbrella::after {
  content: "\f0e9\f0e9"; }

.fasds.fa-trowel::after, .fa-sharp-duotone.fa-trowel::after {
  content: "\e589\e589"; }

.fasds.fa-horizontal-rule::after, .fa-sharp-duotone.fa-horizontal-rule::after {
  content: "\f86c\f86c"; }

.fasds.fa-bed-front::after, .fa-sharp-duotone.fa-bed-front::after {
  content: "\f8f7\f8f7"; }

.fasds.fa-bed-alt::after, .fa-sharp-duotone.fa-bed-alt::after {
  content: "\f8f7\f8f7"; }

.fasds.fa-d::after, .fa-sharp-duotone.fa-d::after {
  content: "\44\44"; }

.fasds.fa-stapler::after, .fa-sharp-duotone.fa-stapler::after {
  content: "\e5af\e5af"; }

.fasds.fa-masks-theater::after, .fa-sharp-duotone.fa-masks-theater::after {
  content: "\f630\f630"; }

.fasds.fa-theater-masks::after, .fa-sharp-duotone.fa-theater-masks::after {
  content: "\f630\f630"; }

.fasds.fa-file-gif::after, .fa-sharp-duotone.fa-file-gif::after {
  content: "\e645\e645"; }

.fasds.fa-kip-sign::after, .fa-sharp-duotone.fa-kip-sign::after {
  content: "\e1c4\e1c4"; }

.fasds.fa-face-woozy::after, .fa-sharp-duotone.fa-face-woozy::after {
  content: "\e3a2\e3a2"; }

.fasds.fa-cloud-question::after, .fa-sharp-duotone.fa-cloud-question::after {
  content: "\e492\e492"; }

.fasds.fa-pineapple::after, .fa-sharp-duotone.fa-pineapple::after {
  content: "\e31f\e31f"; }

.fasds.fa-hand-point-left::after, .fa-sharp-duotone.fa-hand-point-left::after {
  content: "\f0a5\f0a5"; }

.fasds.fa-gallery-thumbnails::after, .fa-sharp-duotone.fa-gallery-thumbnails::after {
  content: "\e3aa\e3aa"; }

.fasds.fa-circle-j::after, .fa-sharp-duotone.fa-circle-j::after {
  content: "\e112\e112"; }

.fasds.fa-eyes::after, .fa-sharp-duotone.fa-eyes::after {
  content: "\e367\e367"; }

.fasds.fa-handshake-simple::after, .fa-sharp-duotone.fa-handshake-simple::after {
  content: "\f4c6\f4c6"; }

.fasds.fa-handshake-alt::after, .fa-sharp-duotone.fa-handshake-alt::after {
  content: "\f4c6\f4c6"; }

.fasds.fa-page-caret-up::after, .fa-sharp-duotone.fa-page-caret-up::after {
  content: "\e42a\e42a"; }

.fasds.fa-file-caret-up::after, .fa-sharp-duotone.fa-file-caret-up::after {
  content: "\e42a\e42a"; }

.fasds.fa-jet-fighter::after, .fa-sharp-duotone.fa-jet-fighter::after {
  content: "\f0fb\f0fb"; }

.fasds.fa-fighter-jet::after, .fa-sharp-duotone.fa-fighter-jet::after {
  content: "\f0fb\f0fb"; }

.fasds.fa-comet::after, .fa-sharp-duotone.fa-comet::after {
  content: "\e003\e003"; }

.fasds.fa-square-share-nodes::after, .fa-sharp-duotone.fa-square-share-nodes::after {
  content: "\f1e1\f1e1"; }

.fasds.fa-share-alt-square::after, .fa-sharp-duotone.fa-share-alt-square::after {
  content: "\f1e1\f1e1"; }

.fasds.fa-reflect-vertical::after, .fa-sharp-duotone.fa-reflect-vertical::after {
  content: "\e665\e665"; }

.fasds.fa-shield-keyhole::after, .fa-sharp-duotone.fa-shield-keyhole::after {
  content: "\e248\e248"; }

.fasds.fa-file-mp4::after, .fa-sharp-duotone.fa-file-mp4::after {
  content: "\e649\e649"; }

.fasds.fa-barcode::after, .fa-sharp-duotone.fa-barcode::after {
  content: "\f02a\f02a"; }

.fasds.fa-bulldozer::after, .fa-sharp-duotone.fa-bulldozer::after {
  content: "\e655\e655"; }

.fasds.fa-plus-minus::after, .fa-sharp-duotone.fa-plus-minus::after {
  content: "\e43c\e43c"; }

.fasds.fa-square-sliders-vertical::after, .fa-sharp-duotone.fa-square-sliders-vertical::after {
  content: "\f3f2\f3f2"; }

.fasds.fa-sliders-v-square::after, .fa-sharp-duotone.fa-sliders-v-square::after {
  content: "\f3f2\f3f2"; }

.fasds.fa-video::after, .fa-sharp-duotone.fa-video::after {
  content: "\f03d\f03d"; }

.fasds.fa-video-camera::after, .fa-sharp-duotone.fa-video-camera::after {
  content: "\f03d\f03d"; }

.fasds.fa-message-middle::after, .fa-sharp-duotone.fa-message-middle::after {
  content: "\e1e1\e1e1"; }

.fasds.fa-comment-middle-alt::after, .fa-sharp-duotone.fa-comment-middle-alt::after {
  content: "\e1e1\e1e1"; }

.fasds.fa-graduation-cap::after, .fa-sharp-duotone.fa-graduation-cap::after {
  content: "\f19d\f19d"; }

.fasds.fa-mortar-board::after, .fa-sharp-duotone.fa-mortar-board::after {
  content: "\f19d\f19d"; }

.fasds.fa-hand-holding-medical::after, .fa-sharp-duotone.fa-hand-holding-medical::after {
  content: "\e05c\e05c"; }

.fasds.fa-person-circle-check::after, .fa-sharp-duotone.fa-person-circle-check::after {
  content: "\e53e\e53e"; }

.fasds.fa-square-z::after, .fa-sharp-duotone.fa-square-z::after {
  content: "\e288\e288"; }

.fasds.fa-message-text::after, .fa-sharp-duotone.fa-message-text::after {
  content: "\e1e6\e1e6"; }

.fasds.fa-comment-alt-text::after, .fa-sharp-duotone.fa-comment-alt-text::after {
  content: "\e1e6\e1e6"; }

.fasds.fa-turn-up::after, .fa-sharp-duotone.fa-turn-up::after {
  content: "\f3bf\f3bf"; }

.fasds.fa-level-up-alt::after, .fa-sharp-duotone.fa-level-up-alt::after {
  content: "\f3bf\f3bf"; }

