import { createFileRoute } from '@tanstack/react-router'
import {AGGridBase} from "../../component/AGGridBase.jsx";

export const Route = createFileRoute('/Product/menu')({
  component: RouteComponent,
})

function RouteComponent() {
  return <AGGridBase  sortArray={[{colId: 'CreatedOn', sort: 'asc'}, {colId: 'SettleAmount'}, {colId: 'Tax'}]}
                     group={[{label: 'Month'}, {label: 'SettleAmount'}, {label: 'Tax'}, {label: 'Quarter'}]}
                     filterList={filterArray}
                     title={"O"}
                     summary={summary}
                     column={colDef}
                     loadURL={'/AGOrder/GetDaySummary'}/>
}
