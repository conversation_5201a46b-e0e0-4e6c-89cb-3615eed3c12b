import { createFileRoute } from "@tanstack/react-router";
import { Button, Dropdown, Form, Input, Tabs, Tag, Tooltip } from "antd";
import {
  AbbribateNumber,
  ArrowDown,
  Dialog,
  Money,
} from "../../utils/util.jsx";
import Half<PERSON>ieChart from "./DashboardChart/-MonthlySaleHalfChart.jsx";
import { Pie } from "@ant-design/charts";
import React, { useRef, use, useEffect, useState } from "react";
import api from "../../utils/api.js";
import moment from "moment";
import StatasticWidget from "./DashboardChart/-MonthlySaleHalfChart.jsx";
import PeriodicSale from "./DashboardChart/-PeriodicSale.jsx";
import PaymentModeSale from "./DashboardChart/-PaymentModeSale.jsx";
import RevenueSummary from "./DashboardChart/-RevenueSummary.jsx";
import RevenueBreakdown from "./DashboardChart/-RevenueBreakdown.jsx";
import { TopSellingItem } from "./DashboardChart/-TopSellingItem.jsx";
import OrderBreakdown from "./DashboardChart/-OrderBreakdown.jsx";

export const Route = createFileRoute("/reports/Dashboard")({
  component: RouteComponent,
});

function RouteComponent() {
  // const chartRef = useRef();
  const [downloadApp, setDownloadApp] = useState(null);
  const [dateRange, setDateRange] = useState([
    moment().startOf("year").toISOString(),
    moment().endOf("year").toISOString(),
  ]);
  const [data, setData] = useState({});
  useEffect(() => {
    api
      .get(
        `/dashboard/GetTotalStatastics?from=${dateRange[0]}&to=${dateRange[1]}`
      )
      .then((x) => setData(x.data));
  }, []);

  return (
    <div>
      <Dialog
        width={800}
        footer={<></>}
        open={downloadApp}
        setOpen={setDownloadApp}
      >
        <div className={"relative flex-c h-full"}>
          <div className={"bg-gray-50  h-[525px] center"}>
            <div>
              <img
                className={"w-full"}
                src={"/images/face-unlock-security.png"}
              />
            </div>
          </div>

          <div className={"p-14 space-y-7 min-w-[60%] "}>
            <img className="w-45" src="/images/logo.svg" />
            <h4>Download Business App</h4>
            <div className={"py-2"}>
              <p>Download the business app to review sales</p>

              <div className={"my-10 mb-0"}>
                <h6 className={"font-bold flex-c gap-1"}>
                  <Tag
                    className={"small !mx-0 !font-bold text-uppercase"}
                    color={"#5E4DB2FF"}
                  >
                    Step 1
                  </Tag>{" "}
                  Scan QR code
                </h6>
                <p className={"mt-2"}>
                  Scan the QR code below or send link to mobile{" "}
                </p>
                <div
                  className={" flex-c p-3 gap-5 bg-gray-200/50 mt-6 rounded-lg"}
                >
                  <img
                    className="shadow rounded-lg  bg-white p-1.5 w-[140px]"
                    src="/images/Untitled 1 (63).png"
                  />
                  <div className={"space-y-1.5"}>
                    <h6 className={"font-bold"}>Can't scan QR code?</h6>
                    <p>Enter your mobile number to send link</p>
                    <div>
                      <Input
                        size={"small"}
                        placeholder={"Enter your mobile number"}
                      />
                    </div>
                    <div>
                      <Button>Send</Button>
                    </div>
                  </div>
                </div>
                <h6 className={"font-semibold text-slate-500 text-xs mt-13"}>
                  <i className={"fa fa-circle-question"}></i> App secured by
                  AES256
                </h6>
              </div>
            </div>
          </div>
        </div>
      </Dialog>
      <div className={"flex-c mb-10"}>
        <div>
          <h5>Dashboard</h5>
          <p className={"text-base font-semibold mt-1 "}>
            Central Hub for Sales Analytics
          </p>
        </div>
        <Button
          className={"!ms-auto !flex-c"}
          color="default"
          variant="filled"
          type={"primary"}
        >
          This year
          <ArrowDown />
        </Button>
      </div>
      <div className={"grid gap-8.5"}>
        <div className={"grid lg:grid-cols-3 gap-y-5 lg:gap-8.5 items-stretch"}>
          <div className={"lg:col-span-1"}>
            <div
              className={
                "grid grid-cols-2 gap-5 lg:gap-8.5 h-full items-stretch"
              }
            >
              <div
                style={{
                  backgroundImage: 'url("/images/bg-3.png")',
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "right calc(-1.7rem)",
                  backgroundSize: "cover",
                }}
                className="p-4.5 flex flex-col card  min-h-[148px]"
              >
                <img className="w-[32px] mt-1" src="/images/linkedin-2.svg" />
                <div className="mt-auto">
                  <Tooltip
                    placement={"bottomLeft"}
                    title={Money(data.TotalEarn)}
                    overlayInnerStyle={{ marginTop: -10 }}
                  >
                    <h3>{AbbribateNumber(data.TotalEarn, true)}</h3>
                  </Tooltip>
                  <h6 className="text-sm mt-0.5 font-semibold">Earnings</h6>
                </div>
              </div>

              <div
                style={{
                  backgroundImage: 'url("/images/bg-3.png")',
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "right calc(-1.7rem)",
                  backgroundSize: "cover",
                }}
                className="p-4.5 flex flex-col card  min-h-[148px]"
              >
                <img className="w-[32px] mt-1" src="/images/youtube-2.svg" />
                <div className="mt-auto">
                  <Tooltip
                    placement={"bottomLeft"}
                    title={Money(data.TotalOrders)}
                    overlayInnerStyle={{ marginTop: -10 }}
                  >
                    <h3>{AbbribateNumber(data.TotalOrders)}</h3>
                  </Tooltip>
                  <h6 className="text-sm mt-0.5 font-semibold">Total orders</h6>
                </div>
              </div>

              <div
                style={{
                  backgroundImage: 'url("/images/bg-3.png")',
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "right calc(-1.7rem)",
                  backgroundSize: "cover",
                }}
                className="p-4.5 flex flex-col card  min-h-[148px]"
              >
                <img className="w-[32px] mt-1" src="/images/instagram-03.svg" />
                <div className="mt-auto">
                  <Tooltip
                    placement={"bottomLeft"}
                    title={Money(data.Parcels)}
                    overlayInnerStyle={{ marginTop: -10 }}
                  >
                    <h3>{AbbribateNumber(data.Parcels)}</h3>
                  </Tooltip>
                  <h6 className="text-sm mt-0.5 font-semibold">
                    Total parcels
                  </h6>
                </div>
              </div>

              <div
                style={{
                  backgroundImage: 'url("/images/bg-3.png")',
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "right calc(-1.7rem)",
                  backgroundSize: "cover",
                }}
                className="p-4.5 flex flex-col card  min-h-[148px]"
              >
                <img className="w-[32px] mt-1" src="/images/tiktok.svg" />
                <div className="mt-auto">
                  <Tooltip
                    placement={"bottomLeft"}
                    title={Money(data.DineIn)}
                    overlayInnerStyle={{ marginTop: -10 }}
                  >
                    <h3>{AbbribateNumber(data.DineIn)}</h3>
                  </Tooltip>
                  <h6 className="text-sm mt-0.5 font-semibold">Total dinein</h6>
                </div>
              </div>
            </div>
          </div>

          <div className={"lg:col-span-2"}>
            <div
              className={
                "flex space-y-4  flex-col relative items-stretch text-card-foreground rounded-xl card border border-border shadow-xs black/5 h-full"
              }
            >
              <div className="grid grid-cols-2 gap-4 relative overflow-hidden h-full">
                <div className="space-y-5 p-12 pr-0 overflow-hidden">
                  <div className="flex -space-x-4">
                    <img
                      className="w-10 h-10 rounded-full border-2 border-white"
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSuZXxruR0bky7Fpit21SyWNKpfGSsmOLKMZQ&amp;s"
                    />
                    <img
                      className="w-10 h-10 rounded-full border-2 border-white"
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRFtU538zqvEXrThpXXrg18ECbslC2IQNK2lg&amp;s"
                    />
                    <img
                      className="w-10 h-10 rounded-full border-2 border-white"
                      src="https://animationvisarts.com/wp-content/uploads/2023/12/Frame-32-6.png"
                    />
                    <img
                      className="w-10 h-10 rounded-full border-2 border-white"
                      src="https://images.storyboard18.com/storyboard18/2024/10/Untitled-design-2024-10-15T145713.852-2024-10-628be65d4276e77d8273335b38530aa2-1019x573.jpg"
                    />
                  </div>
                  <div>
                    <h4 className="w-full">
                      Track Sales &amp; Data
                      <br /> from
                      <span className="link !font-semibold">
                        {" "}
                        Anywhere or Anytime
                      </span>
                    </h4>
                  </div>
                  <h6 className="w-full text-slate-400">
                    Join _Captain India's_ B2B market to expand your reach, grow
                    your business, and connect with a vast network of buyers and
                    sellers.
                  </h6>
                  <Button onClick={() => setDownloadApp(true)}>
                    Download Now
                  </Button>
                </div>

                <div
                  style={{
                    backgroundSize: "447",
                    backgroundPosition: "-28px -173px",
                    backgroundRepeat: "no-repeat",
                    backgroundImage: 'url("/images/Group 1775.jpg?q=10")',
                  }}
                  className={"flex-c justify-center relative overflow-hidden"}
                >
                  <img
                    src={
                      "https://framerusercontent.com/images/EnEFPDtxrn4mCpoayK0zv0fAORk.png?scale-down-to=1024"
                    }
                    className={"w-[250px] -bottom-18 z-50 absolute "}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={"grid lg:grid-cols-3 gap-y-5 lg:gap-8.5 items-stretch"}>
          <PeriodicSale />

          <div className={"lg:col-span-2"}>
            <StatasticWidget />
          </div>
        </div>

        <div className={"grid lg:grid-cols-3 gap-y-5 lg:gap-8.5 items-stretch"}>
          <PaymentModeSale />

          <div className={"lg:col-span-2"}>
            <RevenueSummary />
          </div>
        </div>

        <div className={"grid lg:grid-cols-3 gap-y-5 lg:gap-8.5 items-stretch"}>
          <RevenueBreakdown />

          <div className={"lg:col-span-2"}>
            <TopSellingItem />
          </div>
        </div>

        <div className={"grid lg:grid-cols-3 gap-y-5 lg:gap-8.5 items-stretch"}>
          <RevenueBreakdown />

          <div className={"lg:col-span-2"}>
            <OrderBreakdown />
          </div>
        </div>
      </div>
    </div>
  );
}
