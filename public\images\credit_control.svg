﻿<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="240" height="240">
  <linearGradient id="vkElyIZjpa9ytD0Lj1rQba" x1="20.375" x2="28.748" y1="242.939" y2="213.054" gradientTransform="matrix(1 0 0 -1 0 254)" gradientUnits="userSpaceOnUse">
    <stop offset="0" stop-color="#f44f5a" />
    <stop offset=".443" stop-color="#ee3d4a" />
    <stop offset="1" stop-color="#e52030" />
  </linearGradient>
  <path fill="url(#vkElyIZjpa9ytD0Lj1rQba)" d="M43.125,9H4.875C3.287,9,2,10.287,2,11.875v24.25C2,37.713,3.287,39,4.875,39h38.25C44.713,39,46,37.713,46,36.125v-24.25C46,10.287,44.713,9,43.125,9z" />
  <linearGradient id="vkElyIZjpa9ytD0Lj1rQbb" x1="2" x2="46" y1="238" y2="238" gradientTransform="matrix(1 0 0 -1 0 254)" gradientUnits="userSpaceOnUse">
    <stop offset="0" stop-color="#45494d" />
    <stop offset="1" stop-color="#6d7479" />
  </linearGradient>
  <rect width="44" height="4" x="2" y="14" fill="url(#vkElyIZjpa9ytD0Lj1rQbb)" />
  <path fill="#ffa8a8" d="M42,24H6c-0.552,0-1-0.448-1-1v-2c0-0.552,0.448-1,1-1h36c0.552,0,1,0.448,1,1v2C43,23.552,42.552,24,42,24z" />
  <radialGradient id="vkElyIZjpa9ytD0Lj1rQbc" cx="26.257" cy="33.315" r="10.612" gradientTransform="matrix(1.05 0 0 1.0625 9 -1.563)" gradientUnits="userSpaceOnUse">
    <stop offset=".55" stop-color="#e4e4e6" />
    <stop offset=".73" stop-color="#e1e2e4" />
    <stop offset=".854" stop-color="#d8dadc" />
    <stop offset=".961" stop-color="#c9cdcf" />
    <stop offset="1" stop-color="#c1c6c9" />
  </radialGradient>
  <ellipse cx="36.5" cy="36.5" fill="url(#vkElyIZjpa9ytD0Lj1rQbc)" rx="10.5" ry="8.5" />
  <linearGradient id="vkElyIZjpa9ytD0Lj1rQbd" x1="32.044" x2="41.082" y1="30.544" y2="39.582" gradientUnits="userSpaceOnUse">
    <stop offset="0" stop-color="#32bdef" />
    <stop offset="1" stop-color="#1ea2e4" />
  </linearGradient>
  <circle cx="36.5" cy="35" r="7" fill="url(#vkElyIZjpa9ytD0Lj1rQbd)" />
  <radialGradient id="vkElyIZjpa9ytD0Lj1rQbe" cx="34.438" cy="29.879" r="9.084" gradientUnits="userSpaceOnUse">
    <stop offset="0" stop-color="#4b4b4b" />
    <stop offset=".531" stop-color="#393939" />
    <stop offset="1" stop-color="#252525" />
  </radialGradient>
  <circle cx="36.5" cy="35" r="3.5" fill="url(#vkElyIZjpa9ytD0Lj1rQbe)" />
  <circle cx="34" cy="32.5" r="1" fill="#fff" />
</svg>