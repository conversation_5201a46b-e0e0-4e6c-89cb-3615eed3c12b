import React from 'react';
import { Star } from 'lucide-react';

const TestimonialSection = () => {
  return (
    <div className="relative bg-gradient-to-br lg:m-20 rounded-4xl from-gray-900 via-blue-900 to-purple-900 py-20 px-8 overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-20 left-20 text-white/10 text-8xl font-thin">★</div>
      <div className="absolute top-10 right-20 text-white/10 text-9xl font-thin rotate-12">Z</div>
      <div className="absolute bottom-20 right-10 text-white/5 text-6xl">↗</div>
      
      {/* Main content container */}
      <div className="relative z-10 max-w-7xl mx-auto text-center">
        {/* Header text */}
        <div className="mb-12">
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-8 leading-tight">
            Customers have <span className="italic font-light">Consistently</span>
            <br />
            <span className="italic font-light">Rated</span> Softec <span className="text-white">4.9/5</span>
          </h2>
          
          {/* CTA Button */}
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-medium transition-colors duration-200 shadow-lg">
            Try it on Browser
          </button>
        </div>

        {/* Review cards */}
        <div className="flex flex-wrap justify-center gap-8 mb-16 max-w-4xl mx-auto">
          {/* Capterra */}
          <div className="text-center">
            <div className="flex justify-center mb-3">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 fill-orange-400 text-orange-400" />
              ))}
            </div>
            <p className="text-white/80 text-sm mb-3 max-w-48">
              "the interface is excellent"
            </p>
            <div className="flex items-center justify-center">
              <span className="text-orange-500 text-2xl mr-2">🔸</span>
              <span className="text-white font-medium">Capterra</span>
            </div>
          </div>

          {/* Trustpilot */}
          <div className="text-center">
            <div className="flex justify-center mb-3">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 fill-green-400 text-green-400" />
              ))}
            </div>
            <p className="text-white/80 text-sm mb-3 max-w-48">
              "improvements in every release"
            </p>
            <div className="flex items-center justify-center">
              <span className="text-green-500 text-2xl mr-2">★</span>
              <span className="text-white font-medium">Trustpilot</span>
            </div>
          </div>

          {/* G2 Crowd */}
          <div className="text-center">
            <div className="flex justify-center mb-3">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 fill-purple-400 text-purple-400" />
              ))}
            </div>
            <p className="text-white/80 text-sm mb-3 max-w-48">
              "improvements in every release"
            </p>
            <div className="flex items-center justify-center">
              <span className="text-purple-500 text-2xl mr-2">G</span>
              <span className="text-white font-medium">CROWD</span>
            </div>
          </div>
        </div>

        {/* Dashboard mockup */}
        <div className="relative max-w-5xl mx-auto">
          <div className=" rotate-y-6 shadow-2xl rounded-2xl overflow-hidden bg-white perspective-1000">
            {/* Browser bar */}
            <div className="bg-gray-100 px-4 py-3 flex items-center space-x-2 border-b">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="flex-1 mx-4">
                <div className="bg-white rounded px-3 py-1 text-sm text-gray-600 border">
                  🔍 Search here to get started
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="bg-blue-500 text-white px-3 py-1 rounded text-sm">+ Create</div>
                <div className="w-8 h-8 rounded-full bg-orange-400"></div>
                <div className="w-8 h-8 rounded-full bg-blue-400"></div>
              </div>
            </div>

            {/* Dashboard content */}
            <div className="bg-white p-6 flex">
              {/* Sidebar */}
              <div className="w-64 pr-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 text-gray-800 font-medium">
                    <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                    <span>Home</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                    <span>Products</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                    <span>Dashboard</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                    <span>Guide</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                    <span>Scorecard</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                    <span>Scorecard</span>
                  </div>
                </div>
              </div>

              {/* Main content */}
              <div className="flex-1">
                {/* Chart area */}
                <div className="mb-6">
                  <div className="flex items-end space-x-4 h-48">
                    {/* Chart bars */}
                    <div className="w-12 bg-green-300 h-32 rounded-t"></div>
                    <div className="w-12 bg-orange-300 h-24 rounded-t"></div>
                    <div className="w-12 bg-green-300 h-40 rounded-t"></div>
                    <div className="w-12 bg-green-300 h-28 rounded-t"></div>
                    <div className="w-12 bg-green-300 h-44 rounded-t"></div>
                    <div className="w-12 bg-green-300 h-36 rounded-t"></div>
                    <div className="w-12 bg-orange-300 h-32 rounded-t"></div>
                    <div className="w-12 bg-green-300 h-40 rounded-t"></div>
                  </div>
                  <div className="flex space-x-4 mt-2 text-xs text-gray-500">
                    <span className="w-12 text-center">Jan</span>
                    <span className="w-12 text-center">Feb</span>
                    <span className="w-12 text-center">Mar</span>
                    <span className="w-12 text-center">Apr</span>
                    <span className="w-12 text-center">May</span>
                    <span className="w-12 text-center">Jun</span>
                    <span className="w-12 text-center">Jul</span>
                    <span className="w-12 text-center">Aug</span>
                  </div>
                </div>
              </div>

              {/* Right sidebar - Comments */}
              <div className="w-80 pl-6 border-l">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-800 mb-4">Comments</h3>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-blue-400"></div>
                      <div>
                        <div className="font-medium text-sm">Gloria</div>
                        <div className="text-xs text-gray-500">Yesterday • 12 users</div>
                        <div className="text-xs text-gray-600 mt-1">In my work so</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-purple-400"></div>
                      <div>
                        <div className="font-medium text-sm">Jacques</div>
                        <div className="text-xs text-gray-500">2 days • Travel shopping</div>
                        <div className="text-xs text-gray-600 mt-1">I need more context about</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Background blur effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-purple-500/5"></div>
    </div>
  );
};

export default TestimonialSection;