import { motion } from "framer-motion";
import { AuroraText } from "./AuroraText";
import { AnimatedShinyText } from "./AnimatedShinyText";
import { PointerHighlight } from "./pointer-highlight";

const HeroSection = () => {
  const customerAvatars = [
    "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=48&h=48&fit=crop&crop=face&auto=format", // Restaurant chef/owner
    "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=48&h=48&fit=crop&crop=face&auto=format", // Professional woman
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face&auto=format", // Business professional
    "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=48&h=48&fit=crop&crop=face&auto=format", // Restaurant manager
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <div className="relative  bg-white overflow-hidden">
      {/* Grid background pattern */}
      <div className="absolute inset-0 [background-size:60px_60px] [background-image:linear-gradient(to_right,#e4e4e7_1px,transparent_1px),linear-gradient(to_bottom,#e4e4e7_1px,transparent_1px)]" />

      {/* Radial gradient overlay for faded look */}
      <div className="pointer-events-none absolute inset-0 bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_1%,transparent_1%,black)]" />

      {/* Subtle animated background elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.1, 0.2, 0.1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 1, 1.1],
            opacity: [0.1, 0.15, 0.1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-pink-100 to-yellow-100 rounded-full blur-3xl"
        />
      </div>

      <div className="max-w-7xl mx-auto ">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="relative z-10 flex flex-col items-center   px-8 text-center pt-20"
        >
          {/* <Customers avatars={customerAvatars} /> */}
          {/* Customer Reviews */}
          <motion.div
            variants={itemVariants}
            className="flex items-center justify-center space-x-6 mb-8"
          >
            {/* Left Side - Larger Avatars */}
            <div className="flex -space-x-3">
              {customerAvatars.map((avatar, index) => (
                <motion.img
                  key={index}
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                  src={avatar}
                  alt={`Customer ${index + 1}`}
                  className="w-12 h-12 rounded-full border-3 border-white shadow-lg"
                />
              ))}
            </div>

            {/* Right Side - Stars and Text Stacked */}
            <div className="flex flex-col items-start space-y-1">
              {/* Stars */}
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.8 + i * 0.1, duration: 0.3 }}
                  >
                    ⭐
                  </motion.div>
                ))}
              </div>

              {/* Customer Count Text */}
              <span className="text-gray-600 font-medium text-sm">
                38,456 Happy Customers.
              </span>
            </div>
          </motion.div>

          {/* Main Heading - Both parts same size */}
          <motion.div variants={itemVariants} className="mb-8">
            <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold tracking-tighter text-gray-900 mb-2 leading-tight">
              India&apos;s #1 trusted
            </h1>
            <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold tracking-tighter leading-tight">
              <AuroraText>AI Powered</AuroraText>{" "}
                <PointerHighlight
            rectangleClassName="bg-pink-200 dark:bg-pink-200 pr-4 border-neutral-300 dark:border-neutral-600 leading-loose"
            pointerClassName="text-yellow-500 h-3 w-3"
            containerClassName="inline-block mr-1"
          >
              <span className="relative z-10 text-gray-900 ">Restaurant POS  </span>
          </PointerHighlight>
            </h1>
          </motion.div>

          {/* Subtitle */}
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 mb-12 max-w-2xl leading-relaxed"
          >
            Learn to grow your wealth with powerful analytics, customized
            insights
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-8 mb-8"
          >
            {/* Download Button - Pill shaped */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium text-base px-8 py-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border-none outline-none cursor-pointer"
            >
              Download →
            </motion.button>

            {/* Watch Demo Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center space-x-3 bg-white border border-gray-200 rounded-full px-6 py-3 cursor-pointer group hover:border-gray-300 hover:shadow-md transition-all duration-200"
            >
              <span className="text-blue-600 text-lg group-hover:text-blue-700 transition-colors duration-200">
                ▶
              </span>
              <span className="text-gray-700 font-medium text-base group-hover:text-gray-900 transition-colors duration-200">
                Watch demo
              </span>
            </motion.div>
          </motion.div>

          {/* Free Trial Notice */}
          <motion.div
            variants={itemVariants}
            className="flex items-center justify-center"
          >
            <div className="group rounded-full border border-black/5 bg-neutral-100 text-base transition-all ease-in hover:cursor-pointer hover:bg-neutral-200">
              <AnimatedShinyText className="inline-flex items-center justify-center px-4 py-1 transition ease-out hover:text-neutral-600 hover:duration-300">
                <span className="text-sm text-gray-600">
                  ✨ No credit card required. Free 14 days trial
                </span>
                <span className="ml-1 text-xs transition-transform duration-300 ease-in-out group-hover:translate-x-0.5">
                  →
                </span>
              </AnimatedShinyText>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default HeroSection;
